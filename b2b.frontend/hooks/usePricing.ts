import { useMutation, useQuery } from '@tanstack/react-query';

// Pricing API Types
export interface ItemPricingRequest {
  price: number;
  discountedPrice?: number;
  quantity?: number;
}

export interface CartPricingRequest {
  items: ItemPricingRequest[];
}

export interface OrderPricingRequest {
  items: ItemPricingRequest[];
  shippingAmount: number;
  campaignDiscountAmount: number;
}

export interface PricingCalculationResult {
  originalPrice: number;
  discountedPrice?: number;
  quantity: number;
  discountAmount: number;
  taxAmount: number;
  netAmount: number;
  totalAmount: number;
  hasDiscount: boolean;
  effectivePrice: number;
}

export interface PricingCalculationSummary {
  totalOriginalAmount: number;
  totalDiscountAmount: number;
  totalTaxAmount: number;
  totalNetAmount: number;
  totalAmount: number;
  shippingAmount: number;
  campaignDiscountAmount: number;
  finalAmount: number;
  itemCount: number;
  totalQuantity: number;
  hasAnyDiscount: boolean;
  discountedItemCount: number;
  subTotal: number;
  totalSavings: number;
}

// API Functions
const calculateItemPricing = async (request: ItemPricingRequest): Promise<PricingCalculationResult> => {
  const response = await fetch('/web-api/pricing/calculate-item', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error('Fiyat hesaplama hatası');
  }

  return response.json();
};

const calculateCartPricing = async (request: CartPricingRequest): Promise<PricingCalculationSummary> => {
  const response = await fetch('/web-api/pricing/calculate-cart', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error('Sepet fiyat hesaplama hatası');
  }

  return response.json();
};

const calculateOrderPricing = async (request: OrderPricingRequest): Promise<PricingCalculationSummary> => {
  const response = await fetch('/web-api/pricing/calculate-order', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error('Sipariş fiyat hesaplama hatası');
  }

  return response.json();
};

const calculateTax = async (grossAmount: number): Promise<number> => {
  const response = await fetch('/web-api/pricing/calculate-tax', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ grossAmount }),
  });

  if (!response.ok) {
    throw new Error('Vergi hesaplama hatası');
  }

  return response.json();
};

// Hooks
export const useItemPricing = () => {
  return useMutation({
    mutationFn: calculateItemPricing,
  });
};

export const useCartPricing = () => {
  return useMutation({
    mutationFn: calculateCartPricing,
  });
};

export const useOrderPricing = () => {
  return useMutation({
    mutationFn: calculateOrderPricing,
  });
};

export const useTaxCalculation = () => {
  return useMutation({
    mutationFn: calculateTax,
  });
};

// Utility Hooks
export const useCalculateItemPrice = (price: number, discountedPrice?: number, quantity: number = 1) => {
  return useQuery({
    queryKey: ['item-pricing', price, discountedPrice, quantity],
    queryFn: () => calculateItemPricing({ price, discountedPrice, quantity }),
    enabled: price > 0,
  });
};

export const useCalculateCartTotal = (items: ItemPricingRequest[]) => {
  return useQuery({
    queryKey: ['cart-pricing', items],
    queryFn: () => calculateCartPricing({ items }),
    enabled: items.length > 0,
  });
};

// Helper Functions
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 2,
  }).format(price);
};

export const formatPriceSimple = (price: number): string => {
  return `${price.toFixed(2)} TL`;
};

export const calculateDiscountPercentage = (originalPrice: number, discountedPrice: number): number => {
  if (originalPrice <= 0 || discountedPrice >= originalPrice) return 0;
  return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
};

export const getTaxRate = (): number => {
  return 20; // %20 KDV
};

export const calculateNetFromGross = (grossAmount: number): number => {
  return grossAmount / 1.2;
};

export const calculateTaxFromGross = (grossAmount: number): number => {
  return grossAmount - (grossAmount / 1.2);
};
