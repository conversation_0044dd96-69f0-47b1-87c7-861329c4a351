{"customer": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "Müş<PERSON>i", "details": "Müşteri Detayları", "list": "Müşteri <PERSON>esi", "add": "Müşter<PERSON>", "addDescription": "<PERSON>ni müşteri bilgilerini girin ve kaydedin", "edit": "Müşteri Düzenle", "editDescription": "Mevcut müşteri bilgilerini düzenleyin ve güncelleyin", "delete": "Müşteri Sil", "search": "Müşteri ara...", "noCustomers": "Müşteri bulunamadı", "fields": {"nameSurname": "Ad Soyad", "email": "E-posta", "phoneNumber": "Telefon Numarası", "password": "Şifre", "taxOrIdentityNumber": "Vergi/TC Numarası", "taxOffice": "<PERSON><PERSON><PERSON>", "status": "Durum", "isActive": "Aktif", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderCount": "Sipariş Sayısı", "totalOrderAmount": "Toplam Sipariş Tutarı", "lastOrderDate": "<PERSON>", "pointBalance": "<PERSON><PERSON>", "addressCount": "<PERSON><PERSON>", "couponCount": "<PERSON><PERSON>n <PERSON>", "reviewCount": "<PERSON><PERSON>", "favouriteCount": "<PERSON><PERSON><PERSON>", "lastLoginDate": "<PERSON> <PERSON><PERSON>", "acceptedKvkk": "KVKK Onayı", "acceptedMembershipAgreement": "Üyelik Sözleşmesi Onayı", "kvkkAcceptedAt": "KVKK Onay Tarihi", "membershipAgreementAcceptedAt": "Üyelik Sözleşmesi Onay Tarihi"}, "placeholders": {"nameSurname": "Müşteri adı ve soyadını giriniz", "email": "<EMAIL>", "phoneNumber": "+90 555 123 45 67", "password": "En az 6 karakter", "taxOrIdentityNumber": "12345678901", "taxOffice": "Vergi dairesi adı"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>"}, "actions": {"toggleStatus": "<PERSON><PERSON><PERSON>", "changePassword": "<PERSON><PERSON><PERSON>", "viewOrders": "Siparişleri Görüntüle", "viewAddresses": "Adresleri Görüntüle", "viewPoints": "Puanları Görüntüle", "viewCoupons": "Kuponları Görüntüle"}, "deleteConfirm": {"title": "Müşteriyi Sil", "description": "Bu müşteriyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."}, "messages": {"createSuccess": "Müşteri başarıyla oluşturuldu", "updateSuccess": "Müşteri başarıyla güncellendi", "deleteSuccess": "Müşteri başarıyla silindi", "statusToggleSuccess": "Müşteri durumu başarıyla değiştirildi", "passwordChangeSuccess": "Müşteri şifresi başarıyla değiştirildi", "createError": "Müşteri oluşturulurken bir hata oluştu", "updateError": "Müşteri güncellenirken bir hata oluş<PERSON>", "deleteError": "Müşteri silinirken bir hata oluş<PERSON>", "fetchError": "Müşteri bilgileri yüklenirken bir hata oluştu", "emailExists": "Bu e-posta adresi zaten kullanılıyor", "phoneExists": "Bu telefon numarası zaten kullanılıyor", "taxNumberExists": "Bu vergi/TC numarası zaten kullanılıyor"}, "analytics": {"title": "Müşteri Analitikleri", "totalCustomers": "Toplam Müşteri", "activeCustomers": "<PERSON><PERSON><PERSON>", "inactiveCustomers": "<PERSON><PERSON><PERSON>", "newCustomersThisMonth": "Bu Ay Yeni <PERSON>i", "newCustomersLastMonth": "Geçen Ay Yeni Müşteri", "averageOrderAmount": "Ortalama Sipariş Tutarı", "totalCustomerValue": "Toplam Müşteri Değeri", "registrationsByMonth": "<PERSON><PERSON><PERSON><PERSON>", "topSpenders": "En Çok Harcayan <PERSON>", "customerGrowth": "Müşteri Büyümesi", "monthlyRegistrations": "<PERSON><PERSON><PERSON><PERSON>"}, "tabs": {"general": "<PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addresses": "<PERSON><PERSON><PERSON>", "points": "<PERSON><PERSON><PERSON>", "coupons": "<PERSON><PERSON><PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON><PERSON>"}, "addresses": {"title": "Müşteri Adresleri", "noAddresses": "<PERSON><PERSON><PERSON>z ad<PERSON>", "addAddress": "<PERSON><PERSON>", "editAddress": "<PERSON><PERSON>", "deleteAddress": "<PERSON><PERSON>", "setAsDefault": "Varsayılan Ya<PERSON>", "fields": {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "line1": "Adres Satırı 1", "line2": "Adres Satırı 2", "city": "Şehir", "district": "İlçe", "country": "<PERSON><PERSON><PERSON>", "postalCode": "Posta Kodu", "isDefault": "Varsayılan Adres"}, "types": {"billing": "<PERSON><PERSON>", "shipping": "Teslimat Adresi"}, "placeholders": {"name": "Ev, İş, vb.", "type": "<PERSON><PERSON> türünü se<PERSON>", "line1": "Mahalle, sokak, bina no", "line2": "Daire no, kat (opsiyonel)", "city": "<PERSON><PERSON><PERSON>", "district": "İlçe seçiniz", "country": "<PERSON><PERSON><PERSON>", "postalCode": "34000"}, "messages": {"createSuccess": "<PERSON><PERSON> b<PERSON>şarıyla eklendi", "updateSuccess": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "deleteSuccess": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "setDefaultSuccess": "Varsayılan adres başarıyla değiştirildi", "createError": "<PERSON><PERSON> bir hata <PERSON>", "updateError": "<PERSON><PERSON> gü<PERSON>llenirken bir hata o<PERSON>", "deleteError": "<PERSON><PERSON> si<PERSON> bir hata <PERSON>", "setDefaultError": "Varsayılan adres değiştirilirken bir hata oluştu", "fetchError": "Adresler yüklenirken bir hata o<PERSON>ş<PERSON>"}, "deleteConfirm": {"title": "<PERSON><PERSON><PERSON>", "description": "Bu adresi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."}}, "orders": {"title": "Müşteri Siparişleri", "noOrders": "Hen<PERSON>z sipariş verilmemiş", "viewOrder": "Sipariş Detayı", "orderHistory": "Sipariş Geçmişi", "fields": {"orderNumber": "Sipariş No", "status": "Durum", "totalAmount": "Toplam Tutar", "itemCount": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Sipariş Tarihi", "customerName": "Müşteri Adı"}, "status": {"pending": "Beklemede", "processing": "İşleniyor", "shipped": "Kargolandı", "delivered": "<PERSON><PERSON><PERSON>", "cancelled": "İptal Edildi", "refunded": "<PERSON>ade Edildi"}, "messages": {"fetchError": "Siparişler yüklenirken bir hata oluş<PERSON>"}, "summary": {"totalOrders": "Toplam Sipariş", "totalAmount": "Toplam Tutar", "averageOrder": "Ortalama <PERSON>ş", "lastOrder": "<PERSON>"}}, "points": {"title": "Müşteri Puanları", "noPoints": "Henüz puan hareketi yok", "addPoints": "<PERSON><PERSON>", "subtractPoints": "<PERSON><PERSON>", "pointHistory": "<PERSON><PERSON>", "currentBalance": "Mevcut Ba<PERSON>ye", "fields": {"pointAmount": "<PERSON><PERSON>", "pointType": "<PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "createdAt": "İşlem Tarihi", "balance": "Bakiye"}, "types": {"earned": "Kazanılan", "spent": "<PERSON><PERSON><PERSON>"}, "placeholders": {"pointAmount": "<PERSON>uan miktarını girin", "description": "İşlem açıklaması (opsiyonel)"}, "messages": {"addSuccess": "<PERSON>uan başarıyla eklendi", "subtractSuccess": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "addError": "<PERSON>uan eklenir<PERSON> bir hata o<PERSON>", "subtractError": "<PERSON><PERSON>ürk<PERSON> bir hata olu<PERSON>", "fetchError": "Puan geçmişi yüklenirken bir hata oluştu", "insufficientPoints": "<PERSON><PERSON><PERSON>an b<PERSON>i"}, "actions": {"addPoints": "<PERSON><PERSON>", "subtractPoints": "<PERSON><PERSON>", "viewHistory": "Geçmişi Görüntüle"}, "summary": {"totalEarned": "Toplam Kazanılan", "totalSpent": "<PERSON><PERSON>", "currentBalance": "Mevcut Ba<PERSON>ye", "lastTransaction": "Son İşlem"}}, "filters": {"all": "Tümü", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "hasOrders": "Sipari<PERSON><PERSON>", "noOrders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "highValue": "<PERSON><PERSON><PERSON><PERSON>", "newCustomers": "<PERSON><PERSON>"}}}