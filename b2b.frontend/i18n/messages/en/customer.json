{"customer": {"title": "Customers", "single": "Customer", "details": "Customer Details", "list": "Customer List", "add": "Add Customer", "addDescription": "Enter new customer information and save", "edit": "Edit Customer", "editDescription": "Edit and update existing customer information", "delete": "Delete Customer", "search": "Search customers...", "noCustomers": "No customers found", "fields": {"nameSurname": "Name Surname", "email": "Email", "phoneNumber": "Phone Number", "password": "Password", "taxOrIdentityNumber": "Tax/Identity Number", "taxOffice": "Tax Office", "status": "Status", "isActive": "Active", "createdAt": "Created Date", "updatedAt": "Updated Date", "orderCount": "Order Count", "totalOrderAmount": "Total Order Amount", "lastOrderDate": "Last Order Date", "pointBalance": "Point Balance", "addressCount": "Address Count", "couponCount": "Coupon Count", "reviewCount": "Review Count", "favouriteCount": "Favourite Count", "lastLoginDate": "Last Login Date", "acceptedKvkk": "KVKK Approval", "acceptedMembershipAgreement": "Membership Agreement Approval", "kvkkAcceptedAt": "KVKK Accepted At", "membershipAgreementAcceptedAt": "Membership Agreement Accepted At"}, "placeholders": {"nameSurname": "Enter customer name and surname", "email": "<EMAIL>", "phoneNumber": "+90 555 123 45 67", "password": "At least 6 characters", "taxOrIdentityNumber": "12345678901", "taxOffice": "Tax office name"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"toggleStatus": "Toggle Status", "changePassword": "Change Password", "viewOrders": "View Orders", "viewAddresses": "View Addresses", "viewPoints": "View Points", "viewCoupons": "View Coupons"}, "deleteConfirm": {"title": "Delete Customer", "description": "Are you sure you want to delete this customer? This action cannot be undone."}, "messages": {"createSuccess": "Customer created successfully", "updateSuccess": "Customer updated successfully", "deleteSuccess": "Customer deleted successfully", "statusToggleSuccess": "Customer status changed successfully", "passwordChangeSuccess": "Customer password changed successfully", "createError": "An error occurred while creating customer", "updateError": "An error occurred while updating customer", "deleteError": "An error occurred while deleting customer", "fetchError": "An error occurred while loading customer information", "emailExists": "This email address is already in use", "phoneExists": "This phone number is already in use", "taxNumberExists": "This tax/identity number is already in use"}, "analytics": {"title": "Customer Analytics", "totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "inactiveCustomers": "Inactive Customers", "newCustomersThisMonth": "New Customers This Month", "newCustomersLastMonth": "New Customers Last Month", "averageOrderAmount": "Average Order Amount", "totalCustomerValue": "Total Customer Value", "registrationsByMonth": "Monthly Registrations", "topSpenders": "Top Spending Customers", "customerGrowth": "Customer Growth", "monthlyRegistrations": "Monthly Registrations"}, "tabs": {"general": "General Information", "orders": "Orders", "addresses": "Addresses", "points": "Points", "coupons": "Coupons", "analytics": "Analytics"}, "addresses": {"title": "Customer Addresses", "noAddresses": "No addresses added yet", "addAddress": "Add New Address", "editAddress": "Edit Address", "deleteAddress": "Delete Address", "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "fields": {"name": "Address Name", "type": "Address Type", "line1": "Address Line 1", "line2": "Address Line 2", "city": "City", "district": "District", "country": "Country", "postalCode": "Postal Code", "isDefault": "<PERSON><PERSON><PERSON> Address"}, "types": {"billing": "Billing Address", "shipping": "Shipping Address"}, "placeholders": {"name": "Home, Office, etc.", "line1": "Street, building number", "line2": "Apartment, floor (optional)", "city": "Select city", "district": "Select district", "country": "Select country", "postalCode": "34000"}, "messages": {"createSuccess": "Address added successfully", "updateSuccess": "Address updated successfully", "deleteSuccess": "Address deleted successfully", "setDefaultSuccess": "Default address changed successfully", "createError": "An error occurred while adding address", "updateError": "An error occurred while updating address", "deleteError": "An error occurred while deleting address", "fetchError": "An error occurred while loading addresses"}, "deleteConfirm": {"title": "Delete Address", "description": "Are you sure you want to delete this address? This action cannot be undone."}}, "orders": {"title": "Customer Orders", "noOrders": "No orders placed yet", "viewOrder": "Order Details", "orderHistory": "Order History", "fields": {"orderNumber": "Order Number", "status": "Status", "totalAmount": "Total Amount", "itemCount": "<PERSON><PERSON>", "createdAt": "Order Date", "customerName": "Customer Name"}, "status": {"pending": "Pending", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "refunded": "Refunded"}, "messages": {"fetchError": "An error occurred while loading orders"}, "summary": {"totalOrders": "Total Orders", "totalAmount": "Total Amount", "averageOrder": "Average Order", "lastOrder": "Last Order"}}, "points": {"title": "Customer Points", "noPoints": "No point transactions yet", "addPoints": "Add Points", "subtractPoints": "Subtract Points", "pointHistory": "Point History", "currentBalance": "Current Balance", "fields": {"pointAmount": "Point Amount", "pointType": "Point Type", "description": "Description", "createdAt": "Transaction Date", "balance": "Balance"}, "types": {"earned": "Earned", "spent": "Spent"}, "placeholders": {"pointAmount": "Enter point amount", "description": "Transaction description (optional)"}, "messages": {"addSuccess": "Points added successfully", "subtractSuccess": "Points subtracted successfully", "addError": "An error occurred while adding points", "subtractError": "An error occurred while subtracting points", "fetchError": "An error occurred while loading point history", "insufficientPoints": "Insufficient point balance"}, "actions": {"addPoints": "Add Points", "subtractPoints": "Subtract Points", "viewHistory": "View History"}, "summary": {"totalEarned": "Total Earned", "totalSpent": "Total Spent", "currentBalance": "Current Balance", "lastTransaction": "Last Transaction"}}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "hasOrders": "Has Orders", "noOrders": "No Orders", "highValue": "High Value", "newCustomers": "New Customers"}}}