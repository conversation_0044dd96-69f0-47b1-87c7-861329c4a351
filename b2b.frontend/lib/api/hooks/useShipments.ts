'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../client';
import {
  Shipment,
  ShipmentCreate,
  ShipmentUpdate,
  ShipmentStatusUpdate,
  ShipmentFilterParams,
  ShipmentStatus
} from '@/types/shipment';

// API functions
const shipmentApi = {
  getAll: async () => {
    return api.get<Shipment[]>('/shipment');
  },

  getById: async (id: string) => {
    return api.get<Shipment>(`/shipment/${id}`);
  },

  getByOrderId: async (orderId: string) => {
    return api.get<Shipment[]>(`/shipment/order/${orderId}`);
  },

  getByStatus: async (status: ShipmentStatus) => {
    return api.get<Shipment[]>(`/shipment/status/${status}`);
  },

  getByTrackingNumber: async (trackingNumber: string) => {
    return api.get<Shipment>(`/shipment/tracking/${trackingNumber}`);
  },

  search: async (searchTerm: string) => {
    return api.get<Shipment[]>(`/shipment/search?searchTerm=${encodeURIComponent(searchTerm)}`);
  },

  create: async (data: ShipmentCreate) => {
    return api.post<{ data: Shipment; message: string }>('/shipment', data);
  },

  update: async (id: string, data: ShipmentUpdate) => {
    return api.put<{ data: Shipment; message: string }>(`/shipment/${id}`, data);
  },

  updateStatus: async (id: string, data: ShipmentStatusUpdate) => {
    return api.put<{ message: string }>(`/shipment/${id}/status`, data);
  },

  cancel: async (id: string) => {
    return api.post<{ message: string }>(`/shipment/${id}/cancel`);
  },

  delete: async (id: string) => {
    return api.delete<{ message: string }>(`/shipment/${id}`);
  }
};

// Query keys
export const shipmentKeys = {
  all: ['shipments'] as const,
  lists: () => [...shipmentKeys.all, 'list'] as const,
  list: (filters: ShipmentFilterParams) => [...shipmentKeys.lists(), { filters }] as const,
  details: () => [...shipmentKeys.all, 'detail'] as const,
  detail: (id: string) => [...shipmentKeys.details(), id] as const,
  byOrderId: (orderId: string) => [...shipmentKeys.all, 'order', orderId] as const,
  byStatus: (status: ShipmentStatus) => [...shipmentKeys.all, 'status', status] as const,
  byTrackingNumber: (trackingNumber: string) => [...shipmentKeys.all, 'tracking', trackingNumber] as const,
  search: (searchTerm: string) => [...shipmentKeys.all, 'search', searchTerm] as const,
};

// Hooks
export const useShipments = (filters: ShipmentFilterParams = {}) => {
  return useQuery({
    queryKey: shipmentKeys.list(filters),
    queryFn: async () => {
      const result = await shipmentApi.getAll();
      return result ?? [];
    },
  });
};

export const useShipment = (id: string) => {
  return useQuery({
    queryKey: shipmentKeys.detail(id),
    queryFn: async () => {
      const result = await shipmentApi.getById(id);
      return result ?? null;
    },
    enabled: !!id,
  });
};

export const useShipmentsByOrderId = (orderId: string) => {
  return useQuery({
    queryKey: shipmentKeys.byOrderId(orderId),
    queryFn: async () => {
      const result = await shipmentApi.getByOrderId(orderId);
      return result ?? [];
    },
    enabled: !!orderId,
  });
};

export const useShipmentsByStatus = (status: ShipmentStatus) => {
  return useQuery({
    queryKey: shipmentKeys.byStatus(status),
    queryFn: async () => {
      const result = await shipmentApi.getByStatus(status);
      return result ?? [];
    },
  });
};

export const useShipmentByTrackingNumber = (trackingNumber: string) => {
  return useQuery({
    queryKey: shipmentKeys.byTrackingNumber(trackingNumber),
    queryFn: async () => {
      const result = await shipmentApi.getByTrackingNumber(trackingNumber);
      return result ?? null;
    },
    enabled: !!trackingNumber,
  });
};

export const useSearchShipments = (searchTerm: string) => {
  return useQuery({
    queryKey: shipmentKeys.search(searchTerm),
    queryFn: async () => {
      const result = await shipmentApi.search(searchTerm);
      return result ?? [];
    },
    enabled: !!searchTerm && searchTerm.length >= 2,
  });
};

// Mutations
export const useCreateShipment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shipmentApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: shipmentKeys.lists() });
    },
  });
};

export const useUpdateShipment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ShipmentUpdate }) =>
      shipmentApi.update(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: shipmentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: shipmentKeys.detail(variables.id) });
    },
  });
};

export const useUpdateShipmentStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ShipmentStatusUpdate }) =>
      shipmentApi.updateStatus(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: shipmentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: shipmentKeys.detail(variables.id) });
    },
  });
};

export const useCancelShipment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shipmentApi.cancel,
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: shipmentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: shipmentKeys.detail(id) });
    },
  });
};

export const useDeleteShipment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shipmentApi.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: shipmentKeys.lists() });
    },
  });
};
