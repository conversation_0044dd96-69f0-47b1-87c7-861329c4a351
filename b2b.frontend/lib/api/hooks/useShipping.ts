'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../client';
import { Shipment, ShipmentStatus } from '@/types/order';

// API functions
const shippingApi = {
  getByOrderId: async (orderId: string) => {
    return api.get<Shipment[]>(`/shipment/order/${orderId}`);
  },

  getById: async (id: string) => {
    return api.get<Shipment>(`/shipment/${id}`);
  },

  create: async (data: any) => {
    return api.post<{ data: Shipment; message: string }>('/shipment', data);
  },

  updateTrackingNumber: async (id: string, trackingNumber: string) => {
    return api.put<{ data: Shipment; message: string }>(`/shipment/${id}/tracking-number`, {
      trackingNumber
    });
  },

  updateStatus: async (id: string, status: ShipmentStatus) => {
    return api.put<{ message: string }>(`/shipment/${id}/status`, {
      status
    });
  },

  createWithCarrier: async (orderId: string, data: CreateShipmentWithCarrierRequest) => {
    return api.post<{
      data: Shipment;
      trackingNumber: string;
      message: string
    }>(`/shipment/create-shipment-with-carrier/${orderId}`, data);
  },

  delete: async (id: string) => {
    return api.delete<{ message: string }>(`/shipment/${id}`);
  },

  getCarriers: async () => {
    return api.get<ShippingCarrier[]>('/shippingcarrier/available');
  }
};

// Types
export interface CreateShipmentWithCarrierRequest {
  carrierShortCode: string;
  recipientName: string;
  recipientPhone: string;
  recipientEmail?: string;
  address: string;
  city: string;
  district: string;
  postalCode?: string;
  weight?: number;
  declaredValue?: number;
  specialInstructions?: string;
}

export interface ShippingCarrier {
  id: string;
  staticId: number;
  name: string;
  shortCode: string;
  description?: string;
  isImplemented: boolean;
  isActive: boolean;
  apiUrl?: string;
  logoUrl?: string;
  settings?: Record<string, string>;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

// Query keys
export const shippingKeys = {
  all: ['shipping'] as const,
  lists: () => [...shippingKeys.all, 'list'] as const,
  details: () => [...shippingKeys.all, 'detail'] as const,
  detail: (id: string) => [...shippingKeys.details(), id] as const,
  byOrder: (orderId: string) => [...shippingKeys.all, 'order', orderId] as const,
  carriers: () => [...shippingKeys.all, 'carriers'] as const,
};

// Hooks
export const useShipmentsByOrder = (orderId: string) => {
  return useQuery({
    queryKey: shippingKeys.byOrder(orderId),
    queryFn: async () => {
      const result = await shippingApi.getByOrderId(orderId);
      return result ?? [];
    },
    enabled: !!orderId,
  });
};

export const useShipment = (id: string) => {
  return useQuery({
    queryKey: shippingKeys.detail(id),
    queryFn: async () => {
      const result = await shippingApi.getById(id);
      return result ?? null;
    },
    enabled: !!id,
  });
};

export const useShippingCarriers = () => {
  return useQuery({
    queryKey: shippingKeys.carriers(),
    queryFn: async () => {
      const result = await shippingApi.getCarriers();
      return result ?? [];
    },
    staleTime: 5 * 60 * 1000, // 5 dakika cache
  });
};

// Mutations
export const useCreateShipment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shippingApi.create,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: shippingKeys.byOrder(variables.orderId) });
      queryClient.invalidateQueries({ queryKey: shippingKeys.lists() });
    },
  });
};

export const useUpdateTrackingNumber = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, trackingNumber }: { id: string; trackingNumber: string }) =>
      shippingApi.updateTrackingNumber(id, trackingNumber),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: shippingKeys.detail(variables.id) });
      // Shipment'ın order ID'sini bilmiyoruz, bu yüzden tüm order shipment'larını invalidate ediyoruz
      queryClient.invalidateQueries({ queryKey: shippingKeys.all });
    },
  });
};

export const useUpdateShipmentStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: ShipmentStatus }) =>
      shippingApi.updateStatus(id, status),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: shippingKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: shippingKeys.all });
    },
  });
};

export const useCreateShipmentWithCarrier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, data }: { orderId: string; data: CreateShipmentWithCarrierRequest }) =>
      shippingApi.createWithCarrier(orderId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: shippingKeys.byOrder(variables.orderId) });
      queryClient.invalidateQueries({ queryKey: shippingKeys.lists() });
    },
  });
};

export const useDeleteShipment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shippingApi.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: shippingKeys.all });
    },
  });
};
