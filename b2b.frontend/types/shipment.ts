export enum ShipmentStatus {
  Pending = 0,
  Processing = 1,
  Shipped = 2,
  InTransit = 3,
  Delivered = 4,
  Cancelled = 5,
  Returned = 6
}

export interface Shipment {
  id: string;
  orderId: string;
  trackingNumber: string;
  carrierId: string;
  carrierName: string;
  carrierShortCode: string;
  status: ShipmentStatus;
  shippedAt: string;
  deliveredAt?: string;
  notes?: string;
  createdAt: string;
}

export interface ShipmentCreate {
  orderId: string;
  trackingNumber: string;
  carrierId: string;
  status?: ShipmentStatus;
  shippedAt: string;
  deliveredAt?: string;
  notes?: string;
}

export interface ShipmentUpdate {
  trackingNumber?: string;
  carrierId?: string;
  status?: ShipmentStatus;
  shippedAt?: string;
  deliveredAt?: string;
  notes?: string;
}

export interface ShipmentStatusUpdate {
  status: ShipmentStatus;
}

export interface ShipmentFilterParams {
  orderId?: string;
  status?: ShipmentStatus;
  carrierId?: string;
  searchTerm?: string;
}

export const ShipmentStatusLabels: Record<ShipmentStatus, string> = {
  [ShipmentStatus.Pending]: 'Beklemede',
  [ShipmentStatus.Processing]: 'İşleniyor',
  [ShipmentStatus.Shipped]: '<PERSON><PERSON>ya <PERSON>ld<PERSON>',
  [ShipmentStatus.InTransit]: 'Yolda',
  [ShipmentStatus.Delivered]: 'Teslim Edildi',
  [ShipmentStatus.Cancelled]: 'İptal Edildi',
  [ShipmentStatus.Returned]: 'İade Edildi'
};

export const ShipmentStatusColors: Record<ShipmentStatus, string> = {
  [ShipmentStatus.Pending]: 'bg-yellow-100 text-yellow-800',
  [ShipmentStatus.Processing]: 'bg-blue-100 text-blue-800',
  [ShipmentStatus.Shipped]: 'bg-purple-100 text-purple-800',
  [ShipmentStatus.InTransit]: 'bg-indigo-100 text-indigo-800',
  [ShipmentStatus.Delivered]: 'bg-green-100 text-green-800',
  [ShipmentStatus.Cancelled]: 'bg-red-100 text-red-800',
  [ShipmentStatus.Returned]: 'bg-orange-100 text-orange-800'
};
