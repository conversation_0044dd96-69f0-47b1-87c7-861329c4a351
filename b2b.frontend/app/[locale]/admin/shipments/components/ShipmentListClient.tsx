'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useShipments, useSearchShipments, useCancelShipment } from '@/lib/api/hooks/useShipments';
import { ShipmentStatus, ShipmentStatusLabels, ShipmentStatusColors } from '@/types/shipment';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Search, Plus, Eye, Edit, Trash2, Package, Ban } from 'lucide-react';
import ShipmentCreateForm from './ShipmentCreateForm';
import { usePermissions } from '@/hooks/use-permissions';
import { toast } from 'sonner';

export default function ShipmentListClient() {
  const t = useTranslations('shipment');
  const commonT = useTranslations('common');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ShipmentStatus | 'all'>('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const { hasPermission } = usePermissions();
  const canRead = hasPermission('shipment', 'read');
  const canCreate = hasPermission('shipment', 'create');
  const canUpdate = hasPermission('shipment', 'update');
  const canDelete = hasPermission('shipment', 'delete');

  // Query all shipments
  const { data: allShipments = [], isLoading, error } = useShipments();

  // Mutations
  const cancelMutation = useCancelShipment();

  // Search query
  const { data: searchResults = [] } = useSearchShipments(searchTerm);

  // Filter shipments based on status
  const filteredShipments = statusFilter === 'all'
    ? allShipments
    : allShipments.filter(shipment => shipment.status === statusFilter);

  // Use search results if searching, otherwise use filtered shipments
  const displayShipments = searchTerm.length >= 2 ? searchResults : filteredShipments;

  const getStatusBadge = (status: ShipmentStatus) => {
    return (
      <Badge className={ShipmentStatusColors[status]}>
        {ShipmentStatusLabels[status]}
      </Badge>
    );
  };

  const handleCancelShipment = async (shipmentId: string) => {
    try {
      await cancelMutation.mutateAsync(shipmentId);
      toast.success(t('cancelSuccess'));
    } catch (error) {
      toast.error(t('cancelError'));
    }
  };

  if (!canRead) {
    return <></>;
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">{commonT('loading')}</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">{commonT('error')}: {error.message}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{commonT('filters.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={t('searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <Select value={statusFilter.toString()} onValueChange={(value) => setStatusFilter(value === 'all' ? 'all' : parseInt(value) as ShipmentStatus)}>
                <SelectTrigger>
                  <SelectValue placeholder={t('selectStatus')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('allStatuses')}</SelectItem>
                  {Object.entries(ShipmentStatusLabels).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {canCreate && (
              <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    {t('addShipment')}
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>{t('createShipment')}</DialogTitle>
                  </DialogHeader>
                  <ShipmentCreateForm onSuccess={() => setIsCreateModalOpen(false)} />
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Shipments Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {t('shipments')} ({displayShipments.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('trackingNumber')}</TableHead>
                  <TableHead>{t('carrier')}</TableHead>
                  <TableHead>{t('status')}</TableHead>
                  <TableHead>{t('shippedAt')}</TableHead>
                  <TableHead>{t('deliveredAt')}</TableHead>
                  <TableHead>{t('notes')}</TableHead>
                  <TableHead className="text-right">{commonT('actions.title')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayShipments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      {searchTerm.length >= 2 ? t('noSearchResults') : t('noShipments')}
                    </TableCell>
                  </TableRow>
                ) : (
                  displayShipments.map((shipment) => (
                    <TableRow key={shipment.id}>
                      <TableCell className="font-medium font-mono">
                        {shipment.trackingNumber}
                      </TableCell>
                      <TableCell>{shipment.carrierName}</TableCell>
                      <TableCell>
                        {getStatusBadge(shipment.status)}
                      </TableCell>
                      <TableCell>
                        {new Date(shipment.shippedAt).toLocaleDateString('tr-TR')}
                      </TableCell>
                      <TableCell>
                        {shipment.deliveredAt
                          ? new Date(shipment.deliveredAt).toLocaleDateString('tr-TR')
                          : '-'
                        }
                      </TableCell>
                      <TableCell className="max-w-32 truncate">
                        {shipment.notes || '-'}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          {canRead && (
                            <Button variant="ghost" size="sm" asChild>
                              <a href={`/admin/shipments/${shipment.id}`}>
                                <Eye className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                          {canUpdate && (
                            <Button variant="ghost" size="sm" asChild>
                              <a href={`/admin/shipments/edit/${shipment.id}`}>
                                <Edit className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                          {canUpdate && shipment.status !== ShipmentStatus.Cancelled && shipment.status !== ShipmentStatus.Delivered && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="ghost" size="sm" className="text-orange-600 hover:text-orange-700">
                                  <Ban className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>{t('cancelShipment')}</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    {t('confirmCancel')}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>{commonT('actions.cancel')}</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleCancelShipment(shipment.id)}
                                    className="bg-orange-600 hover:bg-orange-700"
                                  >
                                    {t('cancelShipment')}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                          {canDelete && (
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
