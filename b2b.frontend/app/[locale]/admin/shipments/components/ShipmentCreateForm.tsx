'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import { useCreateShipment } from '@/lib/api/hooks/useShipments';
import { useOrders } from '@/lib/api/hooks/useOrders';
import { useShippingCarriers } from '@/lib/api/hooks/useShipping';
import { ShipmentCreate, ShipmentStatus, ShipmentStatusLabels } from '@/types/shipment';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Combobox } from '@/components/ui/combobox';
import { toast } from 'sonner';

interface ShipmentCreateFormProps {
  onSuccess?: () => void;
}

export default function ShipmentCreateForm({ onSuccess }: ShipmentCreateFormProps) {
  const t = useTranslations('shipment');
  const commonT = useTranslations('common');
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: orders = [] } = useOrders();
  const { data: carriers = [] } = useShippingCarriers();
  const createMutation = useCreateShipment();

  // Prepare order options for combobox
  const orderOptions = orders.map((order) => ({
    value: order.id,
    label: `${order.orderNumber} - ${order.customerName} (${order.totalAmount.toLocaleString('tr-TR')} ₺)`,
  }));

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
  } = useForm<ShipmentCreate>({
    defaultValues: {
      status: ShipmentStatus.Pending,
      shippedAt: new Date().toISOString().split('T')[0],
    },
  });

  const selectedOrderId = watch('orderId');

  const onSubmit = async (data: ShipmentCreate) => {
    setIsSubmitting(true);
    try {
      await createMutation.mutateAsync(data);
      toast.success(t('createSuccess'));
      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/admin/shipments');
      }
    } catch (error) {
      toast.error(t('createError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Order Selection */}
      <div className="space-y-2">
        <Label htmlFor="orderId">{t('order')} *</Label>
        <Combobox
          options={orderOptions}
          value={selectedOrderId}
          onValueChange={(value) => setValue('orderId', Array.isArray(value) ? value[0] : value)}
          placeholder={t('selectOrder')}
          searchPlaceholder={t('searchOrder')}
          noResultsText={t('noOrderFound')}
        />
        {errors.orderId && (
          <p className="text-sm text-red-500">{t('orderRequired')}</p>
        )}
      </div>

      {/* Tracking Number */}
      <div className="space-y-2">
        <Label htmlFor="trackingNumber">{t('trackingNumber')} *</Label>
        <Input
          id="trackingNumber"
          placeholder={t('trackingNumberPlaceholder')}
          {...register('trackingNumber', {
            required: t('trackingNumberRequired'),
          })}
        />
        {errors.trackingNumber && (
          <p className="text-sm text-red-500">{errors.trackingNumber.message}</p>
        )}
      </div>

      {/* Carrier */}
      <div className="space-y-2">
        <Label htmlFor="carrierId">{t('carrier')} *</Label>
        <Controller
          name="carrierId"
          control={control}
          rules={{ required: t('carrierRequired') }}
          render={({ field }) => (
            <Select value={field.value || ''} onValueChange={field.onChange}>
              <SelectTrigger>
                <SelectValue placeholder={t('selectCarrier')} />
              </SelectTrigger>
              <SelectContent>
                {carriers.map((carrier) => (
                  <SelectItem key={carrier.id} value={carrier.id}>
                    {carrier.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
        {errors.carrierId && (
          <p className="text-sm text-red-500">{errors.carrierId.message}</p>
        )}
      </div>

      {/* Status */}
      <div className="space-y-2">
        <Label htmlFor="status">{t('status')}</Label>
        <Controller
          name="status"
          control={control}
          render={({ field }) => (
            <Select
              value={field.value?.toString() || ShipmentStatus.Pending.toString()}
              onValueChange={(value) => field.onChange(parseInt(value) as ShipmentStatus)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(ShipmentStatusLabels).map(([key, label]) => (
                  <SelectItem key={key} value={key}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
      </div>

      {/* Shipped At */}
      <div className="space-y-2">
        <Label htmlFor="shippedAt">{t('shippedAt')} *</Label>
        <Input
          id="shippedAt"
          type="datetime-local"
          {...register('shippedAt', {
            required: t('shippedAtRequired'),
          })}
        />
        {errors.shippedAt && (
          <p className="text-sm text-red-500">{errors.shippedAt.message}</p>
        )}
      </div>

      {/* Delivered At */}
      <div className="space-y-2">
        <Label htmlFor="deliveredAt">{t('deliveredAt')}</Label>
        <Input
          id="deliveredAt"
          type="datetime-local"
          {...register('deliveredAt')}
        />
      </div>

      {/* Notes */}
      <div className="space-y-2">
        <Label htmlFor="notes">{t('notes')}</Label>
        <Textarea
          id="notes"
          placeholder={t('notesPlaceholder')}
          {...register('notes')}
        />
      </div>

      {/* Submit Buttons */}
      <div className="flex gap-4 pt-4">
        <Button
          type="submit"
          disabled={isSubmitting}
          className="flex-1"
        >
          {isSubmitting ? commonT('creating') : t('createShipment')}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => onSuccess ? onSuccess() : router.push('/admin/shipments')}
          className="flex-1"
        >
          {commonT('actions.cancel')}
        </Button>
      </div>
    </form>
  );
}
