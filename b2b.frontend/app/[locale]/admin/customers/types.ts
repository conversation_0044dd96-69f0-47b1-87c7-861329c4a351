// Customer Types based on Backend DTOs

export interface CustomerDto {
    id: string;
    nameSurname: string;
    phoneNumber?: string;
    email?: string;
    taxOrIdentityNumber?: string;
    taxOffice?: string;
    isActive: boolean;
    acceptedKvkk: boolean;
    acceptedMembershipAgreement: boolean;
    kvkkAcceptedAt?: string;
    membershipAgreementAcceptedAt?: string;
    createdAt: string;
    updatedAt: string;

    // Navigation properties
    addressCount: number;
    orderCount: number;
    totalOrderAmount: number;
    pointBalance: number;
    couponCount: number;
}

export interface CustomerListDto {
    id: string;
    nameSurname: string;
    email?: string;
    phoneNumber?: string;
    isActive: boolean;
    createdAt: string;
    orderCount: number;
    totalOrderAmount: number;
    lastOrderDate?: string;
}

export interface CustomerCreateDto {
    nameSurname: string;
    password: string;
    email?: string;
    phoneNumber?: string;
    taxOrIdentityNumber?: string;
    taxOffice?: string;
    acceptedKvkk: boolean;
    acceptedMembershipAgreement: boolean;
    isActive: boolean;
}

export interface CustomerUpdateDto {
    id: string;
    nameSurname: string;
    email?: string;
    phoneNumber?: string;
    taxOrIdentityNumber?: string;
    taxOffice?: string;
    isActive: boolean;
}

export interface CustomerPasswordChangeDto {
    customerId: string;
    newPassword: string;
}

export interface CustomerPasswordVerifyDto {
    customerId: string;
    password: string;
}

export interface CustomerSearchDto {
    searchTerm?: string;
    email?: string;
    phoneNumber?: string;
    isActive?: boolean;
    createdAfter?: string;
    createdBefore?: string;
    minOrderAmount?: number;
    maxOrderAmount?: number;
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortDirection?: string;
}

export interface CustomerAnalyticsDto {
    totalCustomers: number;
    activeCustomers: number;
    inactiveCustomers: number;
    newCustomersThisMonth: number;
    newCustomersLastMonth: number;
    averageOrderAmount: number;
    totalCustomerValue: number;
    registrationsByMonth: CustomerRegistrationByMonthDto[];
    topSpenders: CustomerTopSpendersDto[];
}

export interface CustomerRegistrationByMonthDto {
    year: number;
    month: number;
    monthName: string;
    count: number;
}

export interface CustomerTopSpendersDto {
    customerId: string;
    customerName: string;
    email?: string;
    totalSpent: number;
    orderCount: number;
}

export interface CustomerDetailDto {
    id: string;
    nameSurname: string;
    phoneNumber?: string;
    email?: string;
    taxOrIdentityNumber?: string;
    taxOffice?: string;
    isActive: boolean;
    acceptedKvkk: boolean;
    acceptedMembershipAgreement: boolean;
    kvkkAcceptedAt?: string;
    membershipAgreementAcceptedAt?: string;
    createdAt: string;
    updatedAt: string;

    // Statistics
    addressCount: number;
    orderCount: number;
    totalOrderAmount: number;
    pointBalance: number;
    couponCount: number;
    reviewCount: number;
    favouriteCount: number;
    lastOrderDate?: string;
    lastLoginDate?: string;

    // Related data
    addresses: AddressDto[];
    recentOrders: OrderListDto[];
    recentPoints: UserPointListDto[];
    activeCoupons: CouponListDto[];
}

// Related types (simplified versions)
export interface AddressDto {
    id: string;
    addressType: number;
    name: string;
    line1: string;
    line2?: string;
    city: string;
    district: string;
    country: string;
    postalCode?: string;
    isDefault: boolean;
    customerId?: string;
    dealerId?: string;
    createdAt: string;
    updatedAt: string;
}

export interface AddressCreateDto {
    addressType: number;
    name: string;
    line1: string;
    line2?: string;
    city: string;
    district: string;
    country: string;
    postalCode?: string;
    isDefault: boolean;
    customerId?: string;
    dealerId?: string;
}

export interface AddressUpdateDto {
    id: string;
    addressType: number;
    name: string;
    line1: string;
    line2?: string;
    city: string;
    district: string;
    country: string;
    postalCode?: string;
    isDefault: boolean;
}

export interface OrderListDto {
    id: string;
    orderNumber: string;
    status: number;
    totalAmount: number;
    createdAt: string;
    customerName: string;
    itemCount: number;
}

export interface UserPointListDto {
    id: string;
    customerId: string;
    pointAmount: number;
    pointType: number;
    description?: string;
    createdAt: string;
}

export interface CouponListDto {
    id: string;
    customerId: string;
    customerName: string;
    couponCode: string;
    discountTypeText: string;
    discountAmount: number;
    expirationDate: string;
    status: string;
    usageCount: number;
    usageLimit: number;
    createdAt: string;
}

export interface UserPointDto {
    id: string;
    customerId: string;
    pointAmount: number;
    pointType: number; // 1: Earned, 2: Spent
    description?: string;
    createdAt: string;
    updatedAt: string;
}

export interface UserPointCreateDto {
    customerId: string;
    pointAmount: number;
    description?: string;
}

// Coupon Types
export interface CouponDto {
    id: string;
    customerId: string;
    customerName: string;
    couponCode: string;
    discountType: number; // 0: Flat, 1: Percentage
    discountTypeText: string;
    discountAmount: number;
    expirationDate: string;
    usageLimit: number;
    isUsed: boolean;
    isSingleUse: boolean;
    usageCount: number;
    isExpired: boolean;
    isActive: boolean;
    remainingUsage: number;
    createdAt: string;
    updatedAt: string;
}

export interface CouponCreateDto {
    customerId: string;
    couponCode: string;
    discountType: number; // 0: Fixed, 1: Percentage
    discountAmount: number;
    expirationDate: string;
    usageLimit: number;
    isSingleUse: boolean;
}

export interface CouponUpdateDto {
    id: string;
    couponCode: string;
    discountType: number; // 0: Fixed, 1: Percentage
    discountAmount: number;
    expirationDate: string;
    usageLimit: number;
    isSingleUse: boolean;
}

export interface CouponSummaryDto {
    customerId: string;
    totalCoupons: number;
    activeCoupons: number;
    usedCoupons: number;
    expiredCoupons: number;
    recentCoupons: CouponListDto[];
}