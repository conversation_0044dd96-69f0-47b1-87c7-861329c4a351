'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Save, X, User, MapPin, ShoppingCart, Gift, Star } from 'lucide-react';
import { useCustomer, useUpdateCustomer } from '@/hooks/api/useCustomers';
import { CustomerUpdateDto } from '../types';
import { CustomerAddressesTab } from './CustomerAddressesTab';
import { CustomerOrdersTab } from './CustomerOrdersTab';
import { CustomerPointsTab } from './CustomerPointsTab';
import { CustomerCouponsTab } from './CustomerCouponsTab';

// Form validation schema
const customerSchema = z.object({
  nameSurname: z.string().min(2, 'İsim en az 2 karakter olmalıdır').max(200, 'İsim en fazla 200 karakter olabilir'),
  email: z.string().email('Geçerli bir e-posta adresi giriniz').optional().or(z.literal('')),
  phoneNumber: z.string().max(20, 'Telefon numarası en fazla 20 karakter olabilir').optional().or(z.literal('')),
  taxOrIdentityNumber: z.string().max(50, 'Vergi/TC numarası en fazla 50 karakter olabilir').optional().or(z.literal('')),
  taxOffice: z.string().max(200, 'Vergi dairesi en fazla 200 karakter olabilir').optional().or(z.literal('')),
  acceptedKvkk: z.boolean(),
  acceptedMembershipAgreement: z.boolean(),
  isActive: z.boolean(),
});

type CustomerFormData = z.infer<typeof customerSchema>;

interface CustomerEditFormProps {
  customerId: string;
}

export function CustomerEditForm({ customerId }: CustomerEditFormProps) {
  const t = useTranslations("customer");
  const commonT = useTranslations("common");
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { data: customer, isLoading, error: fetchError } = useCustomer(customerId);
  const updateCustomerMutation = useUpdateCustomer();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      nameSurname: '',
      email: '',
      phoneNumber: '',
      taxOrIdentityNumber: '',
      taxOffice: '',
      acceptedKvkk: false,
      acceptedMembershipAgreement: false,
      isActive: true,
    },
  });

  const isActive = watch('isActive');

  // Load customer data when it's available
  useEffect(() => {
    if (customer) {
      reset({
        nameSurname: customer.nameSurname,
        email: customer.email || '',
        phoneNumber: customer.phoneNumber || '',
        taxOrIdentityNumber: customer.taxOrIdentityNumber || '',
        taxOffice: customer.taxOffice || '',
        acceptedKvkk: customer.acceptedKvkk || false,
        acceptedMembershipAgreement: customer.acceptedMembershipAgreement || false,
        isActive: customer.isActive,
      });
    }
  }, [customer, reset]);

  const onSubmit = async (data: CustomerFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const customerData: CustomerUpdateDto = {
        id: customerId,
        nameSurname: data.nameSurname,
        email: data.email || undefined,
        phoneNumber: data.phoneNumber || undefined,
        taxOrIdentityNumber: data.taxOrIdentityNumber || undefined,
        taxOffice: data.taxOffice || undefined,
        isActive: data.isActive,
      };

      await updateCustomerMutation.mutateAsync(customerData);
      router.push('/admin/customers');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Müşteri güncellenirken bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">{commonT("status.loading")}</span>
        </CardContent>
      </Card>
    );
  }

  if (fetchError || !customer) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertDescription>
              {fetchError?.message || 'Müşteri bilgileri yüklenirken bir hata oluştu'}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="general">
        {/* Tabs Navigation */}
        <div className="overflow-x-auto">
          <TabsList className="inline-flex h-auto p-1 bg-muted rounded-lg min-w-max">
            <TabsTrigger value="general" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
              <User className="h-4 w-4" />
              <span className="whitespace-nowrap">{t("tabs.general")}</span>
            </TabsTrigger>
            <TabsTrigger value="addresses" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
              <MapPin className="h-4 w-4" />
              <span className="whitespace-nowrap">{t("tabs.addresses")}</span>
            </TabsTrigger>
            <TabsTrigger value="orders" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
              <ShoppingCart className="h-4 w-4" />
              <span className="whitespace-nowrap">{t("tabs.orders")}</span>
            </TabsTrigger>
            <TabsTrigger value="points" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
              <Star className="h-4 w-4" />
              <span className="whitespace-nowrap">{t("tabs.points")}</span>
            </TabsTrigger>
            <TabsTrigger value="coupons" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
              <Gift className="h-4 w-4" />
              <span className="whitespace-nowrap">{t("tabs.coupons")}</span>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Tab Content */}
        <Card>
          <CardContent className="p-6">
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <TabsContent value="general" className="space-y-6 mt-0">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">

                {/* Name Surname */}
                <div className="space-y-2">
                  <Label htmlFor="nameSurname">{t("fields.nameSurname")} *</Label>
                  <Input
                    id="nameSurname"
                    {...register('nameSurname')}
                    placeholder={t("placeholders.nameSurname")}
                  />
                  {errors.nameSurname && (
                    <p className="text-sm text-red-600">{errors.nameSurname.message}</p>
                  )}
                </div>

                {/* Email */}
                <div className="space-y-2">
                  <Label htmlFor="email">{t("fields.email")}</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder={t("placeholders.email")}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>

                {/* Phone Number */}
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">{t("fields.phoneNumber")}</Label>
                  <Input
                    id="phoneNumber"
                    {...register('phoneNumber')}
                    placeholder={t("placeholders.phoneNumber")}
                  />
                  {errors.phoneNumber && (
                    <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
                  )}
                </div>

                {/* Tax or Identity Number */}
                <div className="space-y-2">
                  <Label htmlFor="taxOrIdentityNumber">{t("fields.taxOrIdentityNumber")}</Label>
                  <Input
                    id="taxOrIdentityNumber"
                    {...register('taxOrIdentityNumber')}
                    placeholder={t("placeholders.taxOrIdentityNumber")}
                  />
                  {errors.taxOrIdentityNumber && (
                    <p className="text-sm text-red-600">{errors.taxOrIdentityNumber.message}</p>
                  )}
                </div>

                {/* Tax Office */}
                <div className="space-y-2">
                  <Label htmlFor="taxOffice">{t("fields.taxOffice")}</Label>
                  <Input
                    id="taxOffice"
                    {...register('taxOffice')}
                    placeholder={t("placeholders.taxOffice")}
                  />
                  {errors.taxOffice && (
                    <p className="text-sm text-red-600">{errors.taxOffice.message}</p>
                  )}
                </div>

                {/* KVKK Onayı */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="acceptedKvkk"
                    checked={watch('acceptedKvkk')}
                    onCheckedChange={(checked) => setValue('acceptedKvkk', checked)}
                  />
                  <Label htmlFor="acceptedKvkk">KVKK Onayı</Label>
                </div>

                {/* Üyelik Sözleşmesi Onayı */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="acceptedMembershipAgreement"
                    checked={watch('acceptedMembershipAgreement')}
                    onCheckedChange={(checked) => setValue('acceptedMembershipAgreement', checked)}
                  />
                  <Label htmlFor="acceptedMembershipAgreement">Üyelik Sözleşmesi Onayı</Label>
                </div>

                {/* Is Active */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={isActive}
                    onCheckedChange={(checked) => setValue('isActive', checked)}
                  />
                  <Label htmlFor="isActive">{t("fields.isActive")}</Label>
                </div>

                {/* Customer Statistics */}
                <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
                  <div>
                    <Label className="text-sm font-medium">{t("fields.orderCount")}</Label>
                    <p className="text-lg font-semibold">{customer.orderCount}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">{t("fields.totalOrderAmount")}</Label>
                    <p className="text-lg font-semibold">
                      {new Intl.NumberFormat('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                      }).format(customer.totalOrderAmount)}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">{t("fields.pointBalance")}</Label>
                    <p className="text-lg font-semibold">{customer.pointBalance}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">{t("fields.addressCount")}</Label>
                    <p className="text-lg font-semibold">{customer.addressCount}</p>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex gap-4 pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {commonT("actions.saving")}
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        {commonT("actions.save")}
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push('/admin/customers')}
                    disabled={isSubmitting}
                  >
                    <X className="mr-2 h-4 w-4" />
                    {commonT("actions.cancel")}
                  </Button>
                </div>
              </form>
            </TabsContent>

            {/* Addresses Tab */}
            <TabsContent value="addresses" className="mt-0">
              <CustomerAddressesTab customerId={customerId} />
            </TabsContent>

            {/* Orders Tab */}
            <TabsContent value="orders" className="mt-0">
              <CustomerOrdersTab customerId={customerId} />
            </TabsContent>

            {/* Points Tab */}
            <TabsContent value="points" className="mt-0">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">{t("tabs.points")}</h3>
                <CustomerPointsTab customerId={customerId} currentBalance={customer.pointBalance} />
              </div>
            </TabsContent>

            {/* Coupons Tab */}
            <TabsContent value="coupons" className="mt-0">
              <CustomerCouponsTab customerId={customerId} />
            </TabsContent>

          </CardContent>
        </Card>
      </Tabs>
    </div>
  );
}
