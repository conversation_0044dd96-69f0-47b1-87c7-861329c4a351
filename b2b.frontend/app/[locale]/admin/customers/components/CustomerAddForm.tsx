'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Save, X } from 'lucide-react';
import { useCreateCustomer } from '@/hooks/api/useCustomers';
import { CustomerCreateDto } from '../types';

// Form validation schema
const customerSchema = z.object({
  nameSurname: z.string().min(2, 'İsim en az 2 karakter olmalıdır').max(200, '<PERSON>sim en fazla 200 karakter olabilir'),
  password: z.string().min(6, 'Şifre en az 6 karakter olmalıdır').max(100, 'Şifre en fazla 100 karakter olabilir'),
  email: z.string().email('Geçerli bir e-posta adresi giriniz').optional().or(z.literal('')),
  phoneNumber: z.string().max(20, 'Telefon numarası en fazla 20 karakter olabilir').optional().or(z.literal('')),
  taxOrIdentityNumber: z.string().max(50, 'Vergi/TC numarası en fazla 50 karakter olabilir').optional().or(z.literal('')),
  taxOffice: z.string().max(200, 'Vergi dairesi en fazla 200 karakter olabilir').optional().or(z.literal('')),
  acceptedKvkk: z.boolean(),
  acceptedMembershipAgreement: z.boolean(),
  isActive: z.boolean(),
});

type CustomerFormData = z.infer<typeof customerSchema>;

export function CustomerAddForm() {
  const t = useTranslations("customer");
  const commonT = useTranslations("common");
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createCustomerMutation = useCreateCustomer();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      nameSurname: '',
      password: '',
      email: '',
      phoneNumber: '',
      taxOrIdentityNumber: '',
      taxOffice: '',
      acceptedKvkk: true,
      acceptedMembershipAgreement: true,
      isActive: true,
    },
  });

  const isActive = watch('isActive');

  const onSubmit = async (data: CustomerFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const customerData: CustomerCreateDto = {
        nameSurname: data.nameSurname,
        password: data.password,
        email: data.email || undefined,
        phoneNumber: data.phoneNumber || undefined,
        taxOrIdentityNumber: data.taxOrIdentityNumber || undefined,
        taxOffice: data.taxOffice || undefined,
        isActive: data.isActive,
      };

      await createCustomerMutation.mutateAsync(customerData);
      router.push('/admin/customers');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Müşteri oluşturulurken bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Name Surname */}
            <div className="space-y-2">
              <Label htmlFor="nameSurname">{t("fields.nameSurname")} *</Label>
              <Input
                id="nameSurname"
                {...register('nameSurname')}
                placeholder={t("placeholders.nameSurname")}
              />
              {errors.nameSurname && (
                <p className="text-sm text-red-600">{errors.nameSurname.message}</p>
              )}
            </div>

            {/* Password */}
            <div className="space-y-2">
              <Label htmlFor="password">{t("fields.password")} *</Label>
              <Input
                id="password"
                type="password"
                {...register('password')}
                placeholder={t("placeholders.password")}
              />
              {errors.password && (
                <p className="text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">{t("fields.email")}</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder={t("placeholders.email")}
              />
              {errors.email && (
                <p className="text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            {/* Phone Number */}
            <div className="space-y-2">
              <Label htmlFor="phoneNumber">{t("fields.phoneNumber")}</Label>
              <Input
                id="phoneNumber"
                {...register('phoneNumber')}
                placeholder={t("placeholders.phoneNumber")}
              />
              {errors.phoneNumber && (
                <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
              )}
            </div>

            {/* Tax or Identity Number */}
            <div className="space-y-2">
              <Label htmlFor="taxOrIdentityNumber">{t("fields.taxOrIdentityNumber")}</Label>
              <Input
                id="taxOrIdentityNumber"
                {...register('taxOrIdentityNumber')}
                placeholder={t("placeholders.taxOrIdentityNumber")}
              />
              {errors.taxOrIdentityNumber && (
                <p className="text-sm text-red-600">{errors.taxOrIdentityNumber.message}</p>
              )}
            </div>

            {/* Tax Office */}
            <div className="space-y-2">
              <Label htmlFor="taxOffice">{t("fields.taxOffice")}</Label>
              <Input
                id="taxOffice"
                {...register('taxOffice')}
                placeholder={t("placeholders.taxOffice")}
              />
              {errors.taxOffice && (
                <p className="text-sm text-red-600">{errors.taxOffice.message}</p>
              )}
            </div>

            {/* KVKK Onayı */}
            <div className="flex items-center space-x-2">
              <Switch
                id="acceptedKvkk"
                checked={watch('acceptedKvkk')}
                onCheckedChange={(checked) => setValue('acceptedKvkk', checked)}
              />
              <Label htmlFor="acceptedKvkk">KVKK Onayı</Label>
            </div>

            {/* Üyelik Sözleşmesi Onayı */}
            <div className="flex items-center space-x-2">
              <Switch
                id="acceptedMembershipAgreement"
                checked={watch('acceptedMembershipAgreement')}
                onCheckedChange={(checked) => setValue('acceptedMembershipAgreement', checked)}
              />
              <Label htmlFor="acceptedMembershipAgreement">Üyelik Sözleşmesi Onayı</Label>
            </div>

            {/* Is Active */}
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked) => setValue('isActive', checked)}
              />
              <Label htmlFor="isActive">{t("fields.isActive")}</Label>
            </div>

            {/* Form Actions */}
            <div className="flex gap-4 pt-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {commonT("actions.saving")}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {commonT("actions.save")}
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/admin/customers')}
                disabled={isSubmitting}
              >
                <X className="mr-2 h-4 w-4" />
                {commonT("actions.cancel")}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
