{"ConnectionStrings": {"DefaultConnection": "Host=postgres;Database=b2b_database;Username=b2b_user;Password=b2b_password_2024"}, "JWT": {"SecretKey": "your-super-secret-jwt-key-here-min-32-chars", "Issuer": "B2B.Backend", "Audience": "B2B.Frontend", "ExpiryMinutes": 60}, "RabbitMQ": {"Host": "rabbitmq", "Username": "b2b_rabbit", "Password": "rabbit_password_2024", "Port": 5672}, "SMTP": {"Host": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "B2B Platform", "EnableSsl": true}, "Storage": {"Type": "Local", "Path": "/app/wwwroot/images"}, "CDN": {"BaseUrl": "http://localhost:33800"}, "ApiUrls": {"PanelApi": "http://panel-api:5000", "WebApi": "http://web-api:5000", "MediaApi": "http://media-api:5000", "MailApi": "http://mail-api:5000"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://b2b-frontend:3000", "http://vineta-theme:3000"]}}