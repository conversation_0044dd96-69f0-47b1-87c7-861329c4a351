import { api } from '@/lib/api/client';
import { serverApi } from '@/lib/api/server';
import { withCache } from '@/lib/cache/strategies';

export const getWishListItems = async (customerId) => {
  try {
    return await api.get('/wish-list', customerId);
  } catch (error) {
    console.error('Error fetching product page data:', error);
    throw error;
  }
};
//
// Server-side rendering için unified ürün listesi
export const getWishListItemsSSR = async (customerId) => {
  try {
    return await serverApi.get('/wish-list', customerId);
  } catch (error) {
    console.error('Error fetching product page data (SSR):', error);
    throw error;
  }
};
export const addWishListItem = async (filter = {}) => {
  try {
    return await api.post('/wish-list', filter);
  } catch (error) {
    console.error('Error fetching product page data:', error);
    throw error;
  }
};
export const deleteWishListItem = async (filter = {}) => {
  try {
    return await api.delete('/wish-list', filter);
  } catch (error) {
    console.error('Error fetching product page data:', error);
    throw error;
  }
};
