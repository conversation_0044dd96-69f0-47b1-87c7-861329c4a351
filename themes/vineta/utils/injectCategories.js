export const injectCategories = (data, cats) => ({
  ...data,                                        // ↩︎ üst objeyi klonla
  productsMenu: data.productsMenu.map(menu =>     // ↩︎ menü dizisi kopyası
    menu.categoryName === "Kategoriler"
      ? {
        ...menu,
        products: cats.map(menuItem => ({
          ...menuItem,
          slug: `urunler/${menuItem.slug}`
        }))
      }               //  ← sadece bu eleman güncellenir
      : menu
  )
});
