'use client'
import React, { useState, useEffect } from "react";
import Sidebar from "./Sidebar";
import Link from "next/link";
import Image from "next/image";
import { accountRequests } from "@/services/account";

export default function Orders() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const ordersData = await accountRequests.getOrders();
        setOrders(ordersData || []);
      } catch (err) {
        console.error('Error fetching orders:', err);
        setError(err.message || 'Siparişler yüklenirken bir hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    });
  };

  const getStatusText = (status) => {
    const statusMap = {
      'Pending': 'Beklemede',
      'Processing': 'İşleniyor',
      'Shipped': 'Kargoda',
      'Delivered': 'Teslim Edildi',
      'Cancelled': 'İptal Edildi',
      'Returned': 'İade Edildi'
    };
    return statusMap[status] || status;
  };

  const getStatusClass = (status) => {
    const statusClassMap = {
      'Pending': 'text-warning',
      'Processing': 'text-info',
      'Shipped': 'text-on-the-way',
      'Delivered': 'text-delivered',
      'Cancelled': 'text-danger',
      'Returned': 'text-secondary'
    };
    return statusClassMap[status] || 'text-muted';
  };
  return (
    <div className="flat-spacing-13">
      <div className="container-7">
        {/* sidebar-account */}
        <div className="btn-sidebar-mb d-lg-none">
          <button data-bs-toggle="offcanvas" data-bs-target="#mbAccount">
            <i className="icon icon-sidebar" />
          </button>
        </div>
        {/* /sidebar-account */}
        {/* Section-acount */}

        <div className="main-content-account">
          <div className="sidebar-account-wrap sidebar-content-wrap sticky-top d-lg-block d-none">
            <ul className="my-account-nav">
              <Sidebar />
            </ul>
          </div>
          <div className="my-acount-content account-orders">
            {loading ? (
              <div className="text-center py-5">
                <div className="spinner-border" role="status">
                  <span className="visually-hidden">Yükleniyor...</span>
                </div>
                <div className="mt-2">Siparişleriniz yükleniyor...</div>
              </div>
            ) : error ? (
              <div className="alert alert-danger" role="alert">
                <strong>Hata:</strong> {error}
              </div>
            ) : orders.length === 0 ? (
              <div className="account-no-orders-wrap">
                <Image
                  className="lazyload"
                  data-src="/images/section/account-no-order.png"
                  alt=""
                  src="/images/section/account-no-order.png"
                  width={169}
                  height={168}
                />
                <div className="display-sm fw-medium title">
                  Henüz sipariş vermediniz.
                </div>
                <div className="text text-sm">
                  Alışverişe başlamak için tıklayın.
                </div>
                <Link
                  href={`/urunler`}
                  className="tf-btn animate-btn d-inline-flex bg-dark-2 justify-content-center"
                >
                  Alışverişe Başla
                </Link>
              </div>
            ) : (
              <div className="account-orders-wrap">
                <h5 className="title">Sipariş Geçmişi</h5>
                <div className="wrap-account-order">
                  <table>
                    <thead>
                      <tr>
                        <th className="text-md fw-medium">Sipariş No</th>
                        <th className="text-md fw-medium">Tarih</th>
                        <th className="text-md fw-medium">Durum</th>
                        <th className="text-md fw-medium">Toplam</th>
                        <th className="text-md fw-medium">İşlem</th>
                      </tr>
                    </thead>
                    <tbody>
                      {orders.map((order) => (
                        <tr key={order.id} className="tf-order-item">
                          <td className="text-md">#{order.orderNumber}</td>
                          <td className="text-md">{formatDate(order.createdAt)}</td>
                          <td className={`text-md ${getStatusClass(order.status)}`}>
                            {getStatusText(order.status)}
                          </td>
                          <td className="text-md">
                            ₺{order.totalAmount.toFixed(2)} / {order.itemCount} ürün
                          </td>
                          <td>
                            <a
                              href="#order_detail"
                              data-bs-toggle="modal"
                              className="view-detail"
                              onClick={() => {
                                // Modal açılmadan önce önceki veriyi temizle ve yeni veriyi set et
                                window.selectedOrder = null;
                                setTimeout(() => {
                                  window.selectedOrder = order;
                                  // Modal'ı manuel olarak tetikle
                                  const modalElement = document.getElementById('order_detail');
                                  if (modalElement) {
                                    const event = new Event('show.bs.modal');
                                    modalElement.dispatchEvent(event);
                                  }
                                }, 10);
                              }}
                            >
                              Detay
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* /Account */}
    </div>
  );
}
