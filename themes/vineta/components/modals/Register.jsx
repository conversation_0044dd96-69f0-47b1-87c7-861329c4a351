"use client";
import React, { useState } from "react";
import { useCustomerAuth } from "@/hooks/useCustomerAuth";
import { useRouter } from "next/navigation";

export default function Register() {
  const [formData, setFormData] = useState({
    nameSurname: "",
    email: "",
    password: "",
    phoneNumber: "+905",
    taxOrIdentityNumber: "",
    taxOffice: "",
    acceptedKvkk: false,
    acceptedMembershipAgreement: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const { register, isLoading, error, clearError } = useCustomerAuth();
  const router = useRouter();

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    clearError();
    setSuccessMessage("");

    if (!formData.nameSurname || !formData.email || !formData.password) {
      return;
    }

    const result = await register(formData);
    if (result.success) {
      setSuccessMessage(result.message);

      if (result.autoLogin) {
        // Close modal and redirect to home page
        setTimeout(async () => {
          const modal = document.getElementById('register');
          const bootstrap = await import('bootstrap');
          const modalInstance = bootstrap.Offcanvas.getInstance(modal);
          modalInstance?.hide();

          router.push('/');
        }, 1500);
      } else {
        // Show login modal
        setTimeout(async () => {
          const registerModal = document.getElementById('register');
          const loginModal = document.getElementById('login');
          const bootstrap = await import('bootstrap');

          const registerInstance = bootstrap.Offcanvas.getInstance(registerModal);
          registerInstance?.hide();

          const loginInstance = new bootstrap.Offcanvas(loginModal);
          loginInstance.show();
        }, 1500);
      }
    }
  };

  return (
    <div
      className="offcanvas offcanvas-end popup-style-1 popup-register"
      id="register"
    >
      <div className="canvas-wrapper">
        <div className="canvas-header popup-header">
          <span className="title">Hesap Oluştur</span>
          <button
            className="icon-close icon-close-popup"
            data-bs-dismiss="offcanvas"
            aria-label="Kapat"
          />
        </div>
        <div className="canvas-body popup-inner">
          <form onSubmit={handleSubmit} className="form-login">
            {error && (
              <div className="alert alert-danger mb-3" role="alert">
                {error}
              </div>
            )}
            {successMessage && (
              <div className="alert alert-success mb-3" role="alert">
                {successMessage}
              </div>
            )}
            <div className="">
              <fieldset className="text mb_12">
                <input
                  type="text"
                  name="nameSurname"
                  placeholder="Ad Soyad*"
                  value={formData.nameSurname}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                />
              </fieldset>
              <fieldset className="email mb_12">
                <input
                  type="email"
                  name="email"
                  placeholder="E-posta*"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                />
              </fieldset>
              <fieldset className="password mb_12">
                <div className="position-relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    placeholder="Şifre*"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    minLength={6}
                  />
                  <button
                    type="button"
                    className="btn-show-password"
                    onClick={() => setShowPassword(!showPassword)}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer'
                    }}
                  >
                    {showPassword ? '👁️' : '👁️‍🗨️'}
                  </button>
                </div>
              </fieldset>
              <fieldset className="text mb_12">
                <input
                  type="tel"
                  name="phoneNumber"
                  placeholder="Telefon Numarası"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
              </fieldset>
              <fieldset className="text mb_12">
                <input
                  type="text"
                  name="taxOrIdentityNumber"
                  placeholder="TC Kimlik / Vergi No"
                  value={formData.taxOrIdentityNumber}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
              </fieldset>
              <fieldset className="text mb_12">
                <input
                  type="text"
                  name="taxOffice"
                  placeholder="Vergi Dairesi"
                  value={formData.taxOffice}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
              </fieldset>

              {/* KVKK ve Üyelik Sözleşmesi Onayları */}
              <div className="checkbox-group mb_20">
                <div className="checkbox-item mb_12">
                  <label className="checkbox-label d-flex align-items-start">
                    <input
                      type="checkbox"
                      name="acceptedKvkk"
                      checked={formData.acceptedKvkk}
                      onChange={handleInputChange}
                      disabled={isLoading}
                      className="me-2 mt-1"
                      style={{ minWidth: '16px' }}
                    />
                    <span className="text-sm">
                      <a href="/kvkk" target="_blank" className="text-decoration-underline">
                        Kişisel Verilerin İşlenmesine İlişkin Aydınlatma Metni
                      </a>'ni okudum ve kabul ediyorum. *
                    </span>
                  </label>
                </div>
                <div className="checkbox-item mb_12">
                  <label className="checkbox-label d-flex align-items-start">
                    <input
                      type="checkbox"
                      name="acceptedMembershipAgreement"
                      checked={formData.acceptedMembershipAgreement}
                      onChange={handleInputChange}
                      disabled={isLoading}
                      className="me-2 mt-1"
                      style={{ minWidth: '16px' }}
                    />
                    <span className="text-sm">
                      <a href="/uyelik-sozlesmesi" target="_blank" className="text-decoration-underline">
                        Üyelik Sözleşmesi
                      </a>'ni okudum ve kabul ediyorum. *
                    </span>
                  </label>
                </div>

                {/* Bilgilendirme mesajı */}
                {(!formData.acceptedKvkk || !formData.acceptedMembershipAgreement) && (
                  <div className="alert alert-info mt-2 p-2" style={{ fontSize: '12px', backgroundColor: '#f8f9fa', border: '1px solid #dee2e6', borderRadius: '4px' }}>
                    <small className="text-muted">
                      Kayıt olmak için KVKK ve Üyelik Sözleşmesi onayları gereklidir.
                    </small>
                  </div>
                )}
              </div>
            </div>
            <div className="bot">
              <p className="text text-sm text-main-2">
                Erken indirim erişimi ve kişiselleştirilmiş yeni ürünler, trendler ve promosyonlar için kaydolun.
              </p>
              <div className="button-wrap">
                <button
                  className={`subscribe-button tf-btn animate-btn w-100 ${isLoading || !formData.nameSurname || !formData.email || !formData.password || !formData.acceptedKvkk || !formData.acceptedMembershipAgreement
                    ? 'bg-gray-400 cursor-not-allowed opacity-60'
                    : 'bg-dark-2'
                    }`}
                  type="submit"
                  disabled={isLoading || !formData.nameSurname || !formData.email || !formData.password || !formData.acceptedKvkk || !formData.acceptedMembershipAgreement}
                >
                  {isLoading ? 'Kayıt Olunuyor...' : 'Kayıt Ol'}
                </button>
                <button
                  type="button"
                  data-bs-target="#login"
                  data-bs-toggle="offcanvas"
                  className="tf-btn btn-out-line-dark2 w-100"
                  disabled={isLoading}
                >
                  Giriş Yap
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
