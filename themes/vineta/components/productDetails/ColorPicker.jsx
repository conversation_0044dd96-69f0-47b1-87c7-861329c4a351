"use client";
import { useState } from "react";

const ColorPicker = ({ colors = [], activeColor, setActiveColor }) => {
  const [currentColor, setCurrentColor] = useState(
    colors && colors.length > 0 ? colors[0]?.display || colors[0]?.label || "" : ""
  );

  const handleColorClick = (color) => {
    setActiveColor(color.value);
    setCurrentColor(color.display || color.label);
  };

  // Eğer colors yoksa veya boşsa bileşeni render etme
  if (!colors || colors.length === 0) {
    return null;
  }

  return (
    <div className="variant-picker-item variant-color">
      <div className="variant-picker-label">
        <div>
          Renk:
          <span className="variant-picker-label-value value-currentColor">
            {currentColor}
          </span>
        </div>
      </div>
      <div className="variant-picker-values">
        {colors.map((color) => (
          <span
            key={color.value}
            className={`color-btn ${activeColor === color.value ? "active" : ""}`}
            style={{
              backgroundColor: color.value.toLowerCase(),
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              border: activeColor === color.value ? '2px solid #000' : '1px solid #ddd',
              cursor: 'pointer',
              display: 'inline-block',
              marginRight: '8px'
            }}
            data-color={color.value}
            onClick={() => handleColorClick(color)}
            title={color.display || color.label}
          />
        ))}
      </div>
    </div>
  );
};

export default ColorPicker;
