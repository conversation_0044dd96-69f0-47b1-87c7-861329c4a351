import React from "react";

export default function ProductAttributes({ product }) {
  // Ürün attribute mapping'lerinden özellik bilgilerini çıkar
  // Sadece IsListAttribute = true olan attribute'lar<PERSON> göster
  const productAttributes = product?.attributeMappings?.filter(mapping => 
    mapping.attribute?.isListAttribute === true
  ) || [];

  if (productAttributes.length === 0) {
    return (
      <div className="text-muted">
        <p>Bu ürün için özellik bilgisi bulunmuyor.</p>
      </div>
    );
  }

  return (
    <table className="tb-info-product text-md">
      <tbody>
        {productAttributes.map((mapping, index) => (
          <tr key={index} className="tb-attr-item">
            <th className="tb-attr-label">
              {mapping.attribute?.name || mapping.attribute?.shortName}
            </th>
            <td className="tb-attr-value">
              <p>{mapping.attributeValue?.value}</p>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
