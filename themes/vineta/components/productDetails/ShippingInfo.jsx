import React from "react";

export default function ShippingInfo({ product, shippingSettings }) {
  // Default shipping settings - bu bilgiler backend'den gelebilir
  const defaultSettings = {
    freeShippingThreshold: 150,
    estimatedDeliveryDays: "3-5",
    internationalDelivery: true,
    currency: "TL"
  };

  const settings = shippingSettings || defaultSettings;

  // Ürün ağırlığına veya kategorisine göre özel teslimat süresi
  const getEstimatedDelivery = () => {
    if (product?.categoryName?.toLowerCase().includes('elektronik')) {
      return "1-2 gün";
    }
    if (product?.categoryName?.toLowerCase().includes('kitap')) {
      return "2-3 gün";
    }
    return `${settings.estimatedDeliveryDays} gün`;
  };

  // Ücretsiz kargo kontrolü
  const isFreeShipping = product?.price >= settings.freeShippingThreshold;

  return (
    <div className="tf-product-delivery-return">
      <div className="product-delivery">
        <div className="icon icon-car2" />
        <p className="text-md">
          Tahmini teslimat süresi:
          <span className="fw-medium">
            {getEstimatedDelivery()}
            {settings.internationalDelivery && " (yurt içi)"}
          </span>
        </p>
      </div>
      
      <div className="product-delivery">
        <div className="icon icon-shipping3" />
        <p className="text-md">
          {isFreeShipping ? (
            <span className="fw-medium text-success">Ücretsiz kargo</span>
          ) : (
            <>
              <span className="fw-medium">{settings.freeShippingThreshold} {settings.currency}</span>
              {" "}ve üzeri siparişlerde ücretsiz kargo
            </>
          )}
        </p>
      </div>

      {/* İade politikası */}
      <div className="product-delivery">
        <div className="icon icon-return" />
        <p className="text-md">
          <span className="fw-medium">14 gün</span> içinde ücretsiz iade
        </p>
      </div>

      {/* Stok durumuna göre özel mesaj */}
      {product?.stockQuantity && product.stockQuantity <= 5 && (
        <div className="product-delivery">
          <div className="icon icon-warning" />
          <p className="text-md text-warning">
            <span className="fw-medium">Son {product.stockQuantity} adet!</span> Hızlı teslimat için bugün sipariş verin
          </p>
        </div>
      )}
    </div>
  );
}
