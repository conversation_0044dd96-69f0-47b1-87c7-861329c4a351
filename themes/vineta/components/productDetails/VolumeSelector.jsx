"use client";
import React, { useState, useEffect } from "react";

export default function VolumeSelector({ 
  volumes = [], 
  selectedVolume = null, 
  onVolumeChange = () => {},
  disabled = false 
}) {
  const [activeVolume, setActiveVolume] = useState(selectedVolume);

  // selectedVolume prop'u değiştiğinde activeVolume'u güncelle
  useEffect(() => {
    setActiveVolume(selectedVolume);
  }, [selectedVolume]);

  const handleVolumeSelect = (volume) => {
    if (disabled) return;
    
    setActiveVolume(volume);
    onVolumeChange(volume);
  };

  // Eğer volume seçeneği yoksa component'i gösterme
  if (!volumes || volumes.length === 0) {
    return null;
  }

  // Tek volume varsa seçim gösterme
  if (volumes.length === 1) {
    return (
      <div className="tf-product-variant-volume">
        <div className="variant-title text-md fw-medium">
          Hacim: <span className="fw-normal">{volumes[0].displayText}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="tf-product-variant-volume">
      <div className="variant-title text-md fw-medium">Hacim</div>
      <div className="variant-list-volume">
        {volumes.map((volume) => (
          <div
            key={volume.id}
            className={`variant-volume-item ${
              activeVolume?.id === volume.id ? "active" : ""
            } ${disabled ? "disabled" : ""} ${!volume.inStock ? "out-of-stock" : ""}`}
            onClick={() => handleVolumeSelect(volume)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleVolumeSelect(volume);
              }
            }}
          >
            <div className="volume-info">
              <span className="volume-text">{volume.displayText}</span>
              {volume.price && (
                <span className="volume-price">
                  {volume.price.toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                  })}
                </span>
              )}
            </div>
            {!volume.inStock && (
              <span className="stock-status">Stokta Yok</span>
            )}
          </div>
        ))}
      </div>
      
      {/* Seçili volume bilgisi */}
      {activeVolume && (
        <div className="selected-volume-info">
          <div className="volume-details">
            <span className="selected-volume-text">
              Seçili: {activeVolume.displayText}
            </span>
            {activeVolume.price && (
              <span className="selected-volume-price">
                {activeVolume.price.toLocaleString('tr-TR', {
                  style: 'currency',
                  currency: 'TRY',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0
                })}
              </span>
            )}
          </div>
          {activeVolume.stockQuantity !== undefined && (
            <div className="stock-info">
              <span className={`stock-quantity ${activeVolume.stockQuantity <= 5 ? 'low-stock' : ''}`}>
                {activeVolume.inStock 
                  ? `${activeVolume.stockQuantity} adet stokta`
                  : 'Stokta yok'
                }
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
