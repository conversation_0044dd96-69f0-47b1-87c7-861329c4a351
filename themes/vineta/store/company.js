'use client'
import { create } from 'zustand'

export const useCompanyStore = create((set) => ({
  info: {
    CompanyName: '',
    Address: '',
    PhoneNumber: '',
    Email: '',
    LogoURL: '',
    facebook: '',
    instagram: '',
    twitter: '',
  },
  setCompany: (companyData) =>
    set(() => ({
      info: {
        // backend’den gelen tüm alanları değil,
        // sadece ihtiyacın olanları al:
        CompanyName: companyData.CompanyName,
        Address: companyData.Address,
        PhoneNumber: companyData.PhoneNumber,
        Email: companyData.Email,
        LogoURL: companyData.LogoUrl || companyData.LogoURL || '',
        facebook: companyData.FacebookUrl,
        instagram: companyData.InstagramUrl,
        twitter: companyData.TwitterUrl
      },
    })),
}))
