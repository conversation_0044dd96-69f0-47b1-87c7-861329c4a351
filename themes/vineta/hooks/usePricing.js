import { api } from "@/lib/api/client";
import { useState, useCallback } from "react";

/**
 * Fiyatlandırma hesaplama hook'u
 * Backend'deki merkezi fiyatlandırma servisini kullanır
 */
export const usePricing = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Tek ürün için fiyat hesaplama
   * @param {number} price - Ürün fiyatı
   * @param {number|null} discountedPrice - İndirimli fiyat
   * @param {number} quantity - Miktar
   * @returns {Promise<Object>} Hesaplama sonucu
   */
  const calculateItemPricing = useCallback(async (price, discountedPrice = null, quantity = 1) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.post('/pricing/calculate-item', {
        price,
        discountedPrice,
        quantity
      });

      return response;
    } catch (err) {
      setError(err.message || 'Fiyat hesaplama hatası');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Sepet için toplam fiyat hesaplama
   * @param {Array} items - Sepet ürünleri [{price, discountedPrice, quantity}]
   * @returns {Promise<Object>} Sepet özeti
   */
  const calculateCartPricing = useCallback(async (items) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.post('/pricing/calculate-cart', {
        items: items.map(item => ({
          price: item.price,
          discountedPrice: item.discountedPrice || null,
          quantity: item.quantity || 1
        }))
      });

      return response;
    } catch (err) {
      setError(err.message || 'Sepet fiyat hesaplama hatası');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Sipariş için toplam fiyat hesaplama (kargo ve kampanya dahil)
   * @param {Array} items - Sipariş ürünleri
   * @param {number} shippingAmount - Kargo tutarı
   * @param {number} campaignDiscountAmount - Kampanya indirim tutarı
   * @returns {Promise<Object>} Sipariş özeti
   */
  const calculateOrderPricing = useCallback(async (items, shippingAmount = 0, campaignDiscountAmount = 0) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.post('/pricing/calculate-order', {
        items: items.map(item => ({
          price: item.price,
          discountedPrice: item.discountedPrice || null,
          quantity: item.quantity || 1
        })),
        shippingAmount,
        campaignDiscountAmount
      });

      return response;
    } catch (err) {
      setError(err.message || 'Sipariş fiyat hesaplama hatası');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Vergi dahil tutardan vergi miktarını hesapla
   * @param {number} grossAmount - Vergi dahil tutar
   * @returns {Promise<number>} Vergi tutarı
   */
  const calculateTax = useCallback(async (grossAmount) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.post('/pricing/calculate-tax', {
        grossAmount
      });

      return response;
    } catch (err) {
      setError(err.message || 'Vergi hesaplama hatası');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Client-side basit hesaplamalar (API çağrısı olmadan)
   * Hızlı hesaplamalar için kullanılabilir
   */
  const calculateClientSide = {
    /**
     * İndirim tutarını hesapla
     */
    discountAmount: (price, discountedPrice, quantity = 1) => {
      if (!discountedPrice || discountedPrice >= price) return 0;
      return (price - discountedPrice) * quantity;
    },

    /**
     * Vergi tutarını hesapla (KDV %20)
     */
    taxAmount: (grossAmount) => {
      return grossAmount - (grossAmount / 1.2);
    },

    /**
     * Net tutarı hesapla (vergi hariç)
     */
    netAmount: (grossAmount) => {
      return grossAmount / 1.2;
    },

    /**
     * Toplam tutarı hesapla
     */
    totalAmount: (price, discountedPrice, quantity = 1) => {
      const effectivePrice = discountedPrice || price;
      return effectivePrice * quantity;
    }
  };

  return {
    loading,
    error,
    calculateItemPricing,
    calculateCartPricing,
    calculateOrderPricing,
    calculateTax,
    calculateClientSide
  };
};
