import { useSession, signIn, signOut } from 'next-auth/react'
import { useState } from 'react'

export function useCustomerAuth() {
  const { data: session, status } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const login = async (email, password) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        setError('Email veya şifre hatalı')
        return { success: false, error: 'Email veya şifre hatalı' }
      }

      return { success: true }
    } catch (error) {
      console.error('Login error:', error)
      setError('<PERSON><PERSON><PERSON> sırasında bir hata oluştu')
      return { success: false, error: '<PERSON><PERSON><PERSON> sırasında bir hata oluştu' }
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (customerData) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customerData),
      })

      const result = await response.json()

      if (result.isSuccessful) {
        // After successful registration, automatically sign in
        const loginResult = await signIn('credentials', {
          email: customerData.email,
          password: customerData.password,
          redirect: false,
        })

        if (loginResult?.error) {
          return { 
            success: true, 
            message: 'Kayıt başarılı. Lütfen giriş yapın.',
            autoLogin: false 
          }
        }

        return { 
          success: true, 
          message: result.message,
          autoLogin: true 
        }
      } else {
        setError(result.message)
        return { success: false, error: result.message }
      }
    } catch (error) {
      console.error('Register error:', error)
      setError('Kayıt sırasında bir hata oluştu')
      return { success: false, error: 'Kayıt sırasında bir hata oluştu' }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      await signOut({ redirect: false })
      return { success: true }
    } catch (error) {
      console.error('Logout error:', error)
      return { success: false, error: 'Çıkış sırasında bir hata oluştu' }
    } finally {
      setIsLoading(false)
    }
  }

  const getProfile = async () => {
    if (!session?.accessToken) {
      return { success: false, error: 'Oturum bulunamadı' }
    }

    try {
      const response = await fetch('/api/customer/profile', {
        headers: {
          'Authorization': `Bearer ${session.accessToken}`,
        },
      })

      const result = await response.json()

      if (result.isSuccessful) {
        return { success: true, customer: result.customer }
      } else {
        return { success: false, error: result.message }
      }
    } catch (error) {
      console.error('Get profile error:', error)
      return { success: false, error: 'Profil bilgileri getirilemedi' }
    }
  }

  return {
    // Session data
    session,
    customer: session?.customer,
    isAuthenticated: !!session?.customer,
    isLoading: status === 'loading' || isLoading,
    
    // Auth methods
    login,
    register,
    logout,
    getProfile,
    
    // Error state
    error,
    clearError: () => setError(null),
  }
}
