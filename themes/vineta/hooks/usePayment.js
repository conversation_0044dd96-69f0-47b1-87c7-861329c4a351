import { api } from "@/lib/api/client";
import { toast } from "sonner";

export async function initializeIyzicoPayment(paymentData) {
    try {
        // WebApi PaymentController route: /api/payment/iyzico/initialize
        const data = await api.post('/payment/iyzico/initialize', paymentData);
        const isSuccess = data?.isSuccess ?? data?.IsSuccess;
        const paymentPageUrl = data?.paymentPageUrl ?? data?.PaymentPageUrl;
        if (isSuccess && paymentPageUrl && typeof window !== 'undefined') {
            toast.loading("Ödeme sayfasına yönlendiriliyorsunuz...", { duration: 1200 });
            // küçük bir gecikme ile toast'ın görünmesine izin verelim
            setTimeout(() => { window.location.href = paymentPageUrl; }, 300);
            return;
        }
        return data;
    } catch (error) {
        console.error("Payment initialization error:", error);
        throw error;
    }
}