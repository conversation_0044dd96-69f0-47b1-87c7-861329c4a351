import NextAuth from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'

// Buraya route.js dosyasındaki tüm mantığı taşıyoruz
export const { handlers, auth, signIn, signOut } = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        try {
          // Customer login API call
          //
          const gatewayUrl = process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://localhost:33800';
          const response = await fetch(`${gatewayUrl}/customer/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });

          const result = await response.json();

          if (response.ok && result.success && result.data?.isSuccessful) {
            const customerData = result.data;
            return {
              id: customerData.customer.id,
              email: customerData.customer.email,
              name: customerData.customer.nameSurname,
              accessToken: customerData.token,
              customer: customerData.customer
            };
          }

          console.log('Login failed:', result.message || result.data?.message);
          return null;
        } catch (error) {
          console.error('Customer login error:', error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/login',
    signUp: '/register'
  },
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    // Önceki cevaptaki gibi tüm callback'lerin burada olacak
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.accessToken = user.accessToken;
        token.customer = user.customer;
      }
      // Token doğrulama mantığın burada devam edebilir...
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id;
        session.accessToken = token.accessToken;
        session.customer = token.customer;
      }
      return session;
    }
  },
  // secret burada belirtilmesine gerek yok, process.env'den otomatik okur.
  // Ama garanti olması için ekleyebilirsin.
  secret: process.env.NEXTAUTH_SECRET,
})
