/**
 * Product Types for B2C Frontend
 * Unified interface for both static data and API responses
 */

/**
 * Product Image Interface
 * @typedef {Object} ProductImage
 * @property {number|string} id - Image ID
 * @property {string} [color] - Associated color
 * @property {string} [size] - Associated size
 * @property {string} imgSrc - Image source URL
 * @property {string} alt - Alt text for accessibility
 */

/**
 * Variant Option Interface (Colors, Sizes)
 * @typedef {Object} VariantOption
 * @property {string} label - Display label
 * @property {string} value - Value identifier
 * @property {string} display - Display text
 * @property {string} [img] - Associated image URL
 */

/**
 * Volume Option Interface
 * @typedef {Object} VolumeOption
 * @property {string} id - Volume ID
 * @property {number} volume - Volume amount
 * @property {string} label - Display label (e.g., "50ml")
 * @property {string} unit - Unit type (ml, g, etc.)
 * @property {number} price - Price for this volume
 * @property {boolean} inStock - Stock availability
 * @property {number} [stockQuantity] - Available quantity
 * @property {boolean} isDefault - Is default selection
 * @property {number} sortOrder - Display order
 */

/**
 * Product Campaign Interface
 * @typedef {Object} ProductCampaign
 * @property {string} id - Campaign ID
 * @property {string} name - Campaign name
 * @property {string} [description] - Campaign description
 * @property {string} campaignType - Campaign type
 * @property {boolean} isBuyXGetYFree - Is Buy X Get Y Free campaign
 * @property {number} [buyQuantity] - Buy quantity for BXGY
 * @property {number} [getQuantity] - Get quantity for BXGY
 * @property {number} [totalQuantity] - Total quantity for BXGY
 * @property {number} [calculatedSavings] - Calculated savings amount
 * @property {number} [oldPrice] - Original price
 * @property {number} [newPrice] - Discounted price
 * @property {string} [campaignText] - Campaign display text
 * @property {string} startDate - Campaign start date
 * @property {string} endDate - Campaign end date
 */

/**
 * Product Review Interface
 * @typedef {Object} ProductReview
 * @property {number} id - Review ID
 * @property {string} name - Reviewer name
 * @property {string} date - Review date
 * @property {string} [avatar] - Reviewer avatar URL
 * @property {number} rating - Rating (1-5)
 * @property {string} comment - Review comment
 */

/**
 * Product Attribute Mapping Interface
 * @typedef {Object} ProductAttributeMapping
 * @property {number} id - Mapping ID
 * @property {Object} [attribute] - Attribute details
 * @property {Object} [attributeValue] - Attribute value details
 */

/**
 * Product Metadata Interface
 * @typedef {Object} ProductMetadata
 * @property {string} title - SEO title
 * @property {string} description - SEO description
 * @property {string} [keywords] - SEO keywords
 * @property {string} [canonicalUrl] - Canonical URL
 */

/**
 * Main Product Interface
 * Unified interface for both static data and API responses
 * @typedef {Object} Product
 * @property {number|string} id - Product ID
 * @property {string} title - Product title/name
 * @property {string} slug - URL-friendly identifier
 * @property {string} [sku] - Stock keeping unit
 * @property {string} [brand] - Brand name
 * @property {string} [barcode] - Product barcode
 * @property {string} [categoryName] - Category name
 * @property {string} [categorySlug] - Category slug
 * @property {number} price - Current price
 * @property {number} [oldPrice] - Original price before discount
 * @property {number} [discountPercentage] - Discount percentage
 * @property {number} [stockQuantity] - Available stock quantity
 * @property {boolean} inStock - Stock availability
 * @property {number} [countdownTimer] - Countdown timer for offers
 * @property {string} [imgSrc] - Main product image
 * @property {string} [imgHover] - Hover state image
 * @property {string} [description] - Product description
 * @property {number} [reviewRate] - Average review rating
 * @property {number} [reviewCount] - Total review count
 * 
 * // Collections
 * @property {ProductImage[]} [images] - Product images
 * @property {VariantOption[]} [colors] - Color variants
 * @property {VariantOption[]} [sizes] - Size variants
 * @property {VolumeOption[]} [volumes] - Volume variants
 * @property {ProductReview[]} [reviews] - Product reviews
 * @property {ProductAttributeMapping[]} [attributeMappings] - Attribute mappings
 * 
 * // Volume Information
 * @property {VolumeOption} [selectedVolume] - Currently selected volume
 * @property {boolean} [hasVolumeVariants] - Has volume variants
 * 
 * // Campaign Information
 * @property {ProductCampaign[]} [campaigns] - Available campaigns
 * @property {ProductCampaign} [activeBuyXGetYCampaign] - Active Buy X Get Y campaign
 * 
 * // SEO Metadata
 * @property {ProductMetadata} [metadata] - SEO metadata
 * 
 * // Legacy fields for backward compatibility
 * @property {string[]} [filterSizes] - Filter sizes (legacy)
 * @property {string[]} [filterBrands] - Filter brands (legacy)
 * @property {string[]} [filterColor] - Filter colors (legacy)
 * @property {string} [saleLabel] - Sale label (legacy)
 * @property {number} [width] - Image width (legacy)
 * @property {number} [height] - Image height (legacy)
 */

/**
 * List Product Interface (for product listings)
 * @typedef {Object} ListProduct
 * @property {number|string} id - Product ID
 * @property {string} title - Product title
 * @property {string} slug - URL-friendly identifier
 * @property {string} [brand] - Brand name
 * @property {number} price - Current price
 * @property {number} [oldPrice] - Original price
 * @property {string} [imgSrc] - Main image URL
 * @property {string} [imgHover] - Hover image URL
 * @property {string} [discountType] - Discount type
 * @property {number} [discountPercentage] - Discount percentage
 * @property {number} [countdownTimer] - Countdown timer
 * @property {boolean} inStock - Stock availability
 * @property {string} [description] - Product description
 * @property {string} [category] - Category name
 * @property {VariantOption[]} [sizes] - Available sizes
 */

/**
 * Product Detail Page Data Interface
 * @typedef {Object} ProductDetailPageData
 * @property {Product} product - Main product data
 * @property {ListProduct[]} [recommendedProducts] - Recommended products
 * @property {ListProduct[]} [relatedProducts] - Related products
 * @property {Object} [breadcrumb] - Breadcrumb data
 * @property {ProductMetadata} [metadata] - Page metadata
 */

/**
 * Product Page Data Interface (for listings)
 * @typedef {Object} ProductPageData
 * @property {ListProduct[]} products - Product list
 * @property {Object} filters - Available filters
 * @property {Object} pagination - Pagination data
 * @property {ProductMetadata} [metadata] - Page metadata
 */

// Export types for use in other files
export {
  // Main interfaces
  Product,
  ListProduct,
  ProductDetailPageData,
  ProductPageData,
  
  // Sub-interfaces
  ProductImage,
  VariantOption,
  VolumeOption,
  ProductCampaign,
  ProductReview,
  ProductAttributeMapping,
  ProductMetadata
};
