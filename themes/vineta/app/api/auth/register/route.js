import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { nameSurname, email, password, phoneNumber, taxOrIdentityNumber, taxOffice, acceptedKvkk, acceptedMembershipAgreement } = body

    if (!nameSurname || !email || !password) {
      return NextResponse.json(
        {
          isSuccessful: false,
          message: 'Ad Soyad, Email ve Şifre alanları zorunludur.'
        },
        { status: 400 }
      )
    }

    if (!acceptedKvkk) {
      return NextResponse.json(
        {
          isSuccessful: false,
          message: 'KVK<PERSON> onayı gereklidir.'
        },
        { status: 400 }
      )
    }

    if (!acceptedMembershipAgreement) {
      return NextResponse.json(
        {
          isSuccessful: false,
          message: 'Üyelik Sözleşmesi onayı gereklidir.'
        },
        { status: 400 }
      )
    }

    // Use the correct API Gateway URL - use internal network communication
    const gatewayUrl = process.env.NEXT_PUBLIC_API_GATEWAY_URL || 'http://api_gateway:33800';
    console.log('Calling register API:', `${gatewayUrl}/customer/register`);
    
    // Call backend register API
    const response = await fetch(`${gatewayUrl}/customer/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        nameSurname,
        email,
        password,
        phoneNumber: phoneNumber || null,
        taxOrIdentityNumber: taxOrIdentityNumber || null,
        taxOffice: taxOffice || null,
        acceptedKvkk: acceptedKvkk || false,
        acceptedMembershipAgreement: acceptedMembershipAgreement || false,
      }),
    })

    const result = await response.json()
    console.log('Register API response:', result);

    if (response.ok && result.success && result.data?.isSuccessful) {
      return NextResponse.json({
        isSuccessful: true,
        message: result.data.message || 'Kayıt başarılı',
        customer: result.data.customer,
        token: result.data.token
      })
    } else {
      console.error('Register failed:', result);
      return NextResponse.json(
        {
          isSuccessful: false,
          message: result.data?.message || result.message || 'Kayıt sırasında bir hata oluştu'
        },
        { status: response.status || 400 }
      )
    }
  } catch (error) {
    console.error('Customer register error:', error)
    return NextResponse.json(
      {
        isSuccessful: false,
        message: 'Kayıt sırasında bir hata oluştu'
      },
      { status: 500 }
    )
  }
}
