import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'

export async function GET(request) {
  try {
    // Get session from NextAuth
    const session = await getServerSession()
    
    if (!session || !session.accessToken) {
      return NextResponse.json(
        { 
          isSuccessful: false, 
          message: 'Oturum bulunamadı. Lütfen giriş yapın.' 
        },
        { status: 401 }
      )
    }

    // Call backend profile API
    const response = await fetch('http://localhost:33800/customer/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
    })

    const result = await response.json()

    if (response.ok && result.success) {
      return NextResponse.json({
        isSuccessful: true,
        message: result.message || 'Profil bilgileri getirildi',
        customer: result.data
      })
    } else {
      return NextResponse.json(
        {
          isSuccessful: false,
          message: result.message || 'Profil bilgileri getirilemedi'
        },
        { status: response.status || 400 }
      )
    }
  } catch (error) {
    console.error('Customer profile error:', error)
    return NextResponse.json(
      {
        isSuccessful: false,
        message: 'Profil bilgileri getirilirken bir hata oluştu'
      },
      { status: 500 }
    )
  }
}

export async function PUT(request) {
  try {
    // Get session from NextAuth
    const session = await getServerSession()
    
    if (!session || !session.accessToken) {
      return NextResponse.json(
        { 
          isSuccessful: false, 
          message: 'Oturum bulunamadı. Lütfen giriş yapın.' 
        },
        { status: 401 }
      )
    }

    const body = await request.json()

    // Call backend profile update API (to be implemented in backend)
    const response = await fetch('http://localhost:33800/customer/profile', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    const result = await response.json()

    if (response.ok && result.isSuccessful) {
      return NextResponse.json({
        isSuccessful: true,
        message: result.message || 'Profil güncellendi',
        customer: result.data
      })
    } else {
      return NextResponse.json(
        {
          isSuccessful: false,
          message: result.message || 'Profil güncellenemedi'
        },
        { status: response.status || 400 }
      )
    }
  } catch (error) {
    console.error('Customer profile update error:', error)
    return NextResponse.json(
      {
        isSuccessful: false,
        message: 'Profil güncellenirken bir hata oluştu'
      },
      { status: 500 }
    )
  }
}
