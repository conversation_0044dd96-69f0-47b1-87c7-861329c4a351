import Breadcumb from "@/components/common/Breadcumb";
import Address from "@/components/dashboard/Address";
import { getSession } from "next-auth/react";
import React from "react";
import { accountRequests } from "@/services/account";
import { getServerSession } from "next-auth";
import { auth } from "@/auth"; // auth.js'ten gelen auth fonksiyonu
import { authOptions } from "@/lib/auth/auth-options";

/**
 * Metadata yapısı
 * @typedef {Object} Metadata
 * @property {string} title - Başlık
 * @property {string} description - Açıklama
 */

/**
 * <PERSON><PERSON> yapısı
 * @typedef {Object} Address
 * @property {number} id - Benzersiz adres tanımlayıcısı
 * @property {string} firstName - Adres sahibinin adı
 * @property {string} lastName - Adres sahibinin soyadı
 * @property {string} company - Şirket adı
 * @property {string} address1 - Adres satırı 1
 * @property {string} city - Şehir
 * @property {string} region - Bölge
 * @property {string} province - İl
 * @property {string} zipCode - Posta kodu
 * @property {string} phone - Telefon numarası
 * @property {boolean} isDefault - Varsayılan adres mi?
 * @property {string} email - E-posta adresi
 */

/**
 * Adres listesi ve metadata bilgilerini içeren yapı 
 * @typedef {Object} AddressData
 * @property {Address[]} addresses - Adres listesi
 * @property {Metadata} metadata - Metadata bilgileri
 */

/**
 * @type {AddressData}
 */

async function getAddresses(customerId) {
  return await accountRequests.getAddresses(customerId);
}
/* 
const accountAddressesRequests = {
  // Adresleri getir
  getAddresses: () => api.get('/account/addresses'),
    
  // Adres ekle
  addAddress: (address) => api.post('/account/addresses', address),
  
  // Adres sil
  removeFromWishlist: (addressId) => 
    api.delete(`/account/addresses/${addressId}`)
};
 */

export const metadata = {
  title: "Adreslerim || Future Cosmetics",
  description: "Sipariş ve Fatura adreslerinizi yönetin.",
};
export default async function page() {
  let session = await getServerSession(authOptions);
  const addresses = getAddresses(session.user.id);
  return (
    <>
      <Breadcumb pageName="Adreslerim" pageTitle="Adreslerim" />
      <Address customerId={session.user.id} addresses={addresses} />
    </>
  );
}
