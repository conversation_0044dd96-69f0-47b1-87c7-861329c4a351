
import Wishlist from "@/components/otherPages/Wishlist";
import Breadcumb from "@/components/common/Breadcumb";
import Link from "next/link";
import React from "react";
import { getWishListItemsSSR } from "@/services/wishlist";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth-options";
export const metadata = {
  title: "İstek Listem || Dermedic",
  description: "Beğendiğiniz ürünleri istek listesine ekleyerek daha sonra satın alabilirsiniz.",
};

/**
 * Wishlist interface representing user's wishlist data structure
 * @typedef {Object} Wishlist
 * @property {Product[]} products - Array of product items in wishlist
 * @property {Pagination} pagination - Pagination information
 */

/**
 * Product interface representing individual product items
 * @typedef {Object} Product
 * @property {number} id - Unique identifier for the product
 * @property {string} title - Product title/name
 * @property {string} imgSrc - Main product image source URL
 * @property {string} imgHover - Hover state product image source URL
 * @property {number} price - Current product price
 * @property {number} oldPrice - Original price before discount
 * @property {number} discount - Discount percentage
 * @property {string} stockStatus - Status text for stock availability
 * @property {boolean} inStock - Whether product is in stock
 * @property {string} slug - URL-friendly product identifier
 * @property {Size[]} sizes - Array of available size options
 */

/**
 * Size interface for product size/color options
 * @typedef {Object} Size
 * @property {string} label - Type of variation (e.g., "Color")
 * @property {string} value - Internal value (e.g., "black")
 * @property {string} display - Display text (e.g., "Black")
 */

/**
 * Pagination interface for managing product lists
 * @typedef {Object} Pagination
 * @property {number} page - Current page number
 * @property {number} itemCount - Total number of items
 * @property {number} itemsPerPage - Number of items per page
 * @property {number} totalPages - Total number of pages
 */



/* 
const wishlistRequests = {
  // İstek listesini getir
  getWishlist: () => api.get('/account/wishlist'),
  
  // Ürün ekle
  addToWishlist: (productId) => 
    api.post('/account/wishlist', { productId }),
    
  // Ürün çıkar
  removeFromWishlist: (productId) => 
    api.delete(`/account/wishlist/${productId}`)
}; 
*/

async function wishListItems() {
  let session = await getServerSession(authOptions);
  if (session == null) {
    return [];
  }
  let wishList = await getWishListItemsSSR(session.user.id);
  return wishList.data.products;
}


export default async function page() {
  const items = await wishListItems();
  return (
    <>

      <Breadcumb pageName="İstek Listem" pageTitle="İstek Listem" />

      <Wishlist products={items} />

    </>
  );
}
