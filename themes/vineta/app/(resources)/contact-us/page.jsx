
import Contact from "@/components/otherPages/Contact";
import React from "react";
import Breadcumb from "@/components/common/Breadcumb";
export const metadata = {
  title: "Contact Us | Future Cosmetics",
  description: "Get in touch with Future Cosmetics for inquiries, support, and feedback.",
};

/**
 * @typedef {Object} SocialMedia
 * @property {string} facebook - Facebook profil URL'i
 * @property {string} instagram - Instagram profil URL'i
 */

/**
 * @typedef {Object} ContactInfo
 * @property {string} address - Şirket fiziksel adresi
 * @property {string} phone - İletişim telefon numarası
 * @property {string} email - <PERSON>letişim email adresi
 * @property {string} mapEmbedUrl - Google Maps gömme URL'si
 * @property {SocialMedia} socialMedia - Sosyal medya hesapları
 */

/**
 * <PERSON>let<PERSON><PERSON>im bilgileri
 * @type {ContactInfo}
 */


/**
 * İletişim bilgilerini getir
 * @returns {Promise<import('@/models/contact-us').ContactInfo>}
 */
export const getContactInfo = async () => {
  return api.get('/contact');
};

// Data Example
const contactInfo = {
  address: "123 Main St, Istanbul, Turkey",
  phone: "+90 ************",
  email: "<EMAIL>",
  socialMedia: {
    facebook: "https://www.facebook.com/dermedicturkiye",
    instagram: "https://www.instagram.com/dermedictr",
  },
  mapEmbedUrl: "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d27294.62418958524!2d151.25730233429948!3d-33.82005608618041!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6b12ab8bc95a137f%3A0x358f04a7f6f5f6a6!2sGrotto%20Point%20Lighthouse!5e0!3m2!1sen!2s!4v1733976867160!5m2!1sen!2s"
};

// export default function page() {
//   return (
//     <>
//       <Breadcumb pageName="Contact Us" pageTitle="Contact Us" />
//       <Contact {...contactInfo} />
//     </>
//   );
// }