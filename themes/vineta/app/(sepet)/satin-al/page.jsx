import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth-options";
import { redirect } from "next/navigation";
import Checkout from "@/components/otherPages/Checkout";
import Link from "next/link";
import React from "react";

export const getShippingMethods = async () => {
  return api.get('/shipping-methods');
};




export const metadata = {
  title: "Satın Al || Future Cosmetics",
  description: "Future Cosmetics, İyzico ile Güvenli alışveriş sunar.",
};
export default async function page() {
  const session = await getServerSession(authOptions);
  if (!session?.customer) {
    redirect('/');
  }
  return (
    <>
      <>
        {/* Breadcrumb */}
        <div className="tf-breadcrumb">
          <div className="container">
            <ul className="breadcrumb-list">
              <li className="item-breadcrumb">
                <Link href={`/`} className="text">
                  Anasay<PERSON>
                </Link>
              </li>
              <li className="item-breadcrumb dot">
                <span />
              </li>
              <li className="item-breadcrumb">
                <span className="text">Satın Al</span>
              </li>
            </ul>
          </div>
        </div>
        {/* /Breadcrumb */}
        {/* Title Page */}
        <section className="page-title">
          <div className="container">
            <div className="box-title text-center justify-items-center">
              <h4 className="title">Satın Al</h4>
            </div>
          </div>
        </section>
        {/* /Title Page */}
      </>
      <Checkout />
    </>
  );
}
