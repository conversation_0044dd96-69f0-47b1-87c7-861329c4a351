import Breadcumb from "@/components/products/Breadcumb";
import Products2 from "@/components/products/Products2";
import ErrorRetryButton from "@/components/common/ErrorRetryButton";
import { getCategoryProductsSSR, getCategoryBySlugSSR, getCategoryBreadcrumbSSR } from "@/services/category";
import { getProductListFiltersSSR } from "@/services/productList";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import React from "react";
import { getCompanyInfo } from "@/lib/api/server";

// Force dynamic rendering - SSR for every request
export const dynamic = 'force-dynamic';
export const revalidate = 0;

// Optimized metadata generation with caching
export async function generateMetadata({ params }) {
  try {
    const { slug } = await params;

    // Fetch category data for metadata generation
    const categoryData = await getCategoryBySlugSSR(slug);
    const companyInfo = (await getCompanyInfo()).data;
    console.log(companyInfo.companyName)
    if (!categoryData?.success || !categoryData?.data) {
      return {
        title: `Kategori Bulunamadı | ${companyInfo.companyName}`,
        description: "Aradığınız kategori bulunamadı.",
        robots: "noindex, nofollow",
      };
    }

    const category = categoryData.data;

    // Generate comprehensive metadata
    const title = `${category.name} | ${companyInfo.companyName}`;
    const description = `${category.metadata.description || category.description}`;

    // Add additional SEO optimizations
    return {
      title,
      description,
      keywords: `${category.name}, ürünler, alışveriş, online mağaza`,
      alternates: {
        canonical: `/urunler/${slug}`,
      },
      openGraph: {
        title,
        description,
        url: `/urunler/${slug}`,
        type: 'website',
        siteName: `${companyInfo.companyName}`,
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
      },
      robots: "index, follow",
    };
  } catch (error) {
    console.error('Error generating metadata for category slug:', params?.slug, error);
    return {
      title: `Kategori | Dermedic`,
      description: "Kategori sayfası",
      robots: "noindex, nofollow",
    };
  }
}

/**
 * Kategori filtresi için tip tanımı
 * @typedef {Object} CategoryFilter
 * @property {number} id - Kategori ID'si
 * @property {string} name - Kategori adı
 * @property {string} slug - Kategori URL'i
 * @property {number} count - Ürün sayısı
 */

/**
 * Liste ürünü için tip tanımı
 * @typedef {Object} ListProduct
 * @property {number} id - Ürün ID'si
 * @property {string} title - Ürün başlığı
 * @property {string} slug - Ürün URL'i
 * @property {string} brand - Marka adı
 * @property {number} price - Güncel fiyat
 * @property {number} oldPrice - Eski fiyat
 * @property {string} imgSrc - Ana görsel URL'i
 * @property {boolean} inStock - Stok durumu
 * @property {string} description - Ürün açıklaması
 * @property {string} category - Kategori adı
 */

/**
 * Kategori sayfası veri yapısı
 * @typedef {Object} CategoryPageData
 * @property {CategoryFilter} category - Kategori bilgisi
 * @property {ListProduct[]} products - Ürün listesi
 * @property {Object} filters - Filtreleme seçenekleri
 * @property {Object} pagination - Sayfalama bilgisi
 */

/* //Request example

const categoryRequests = {
  // Kategori ürünlerini getir
  getCategoryProducts: (slug, filters) => api.get(`/category/${slug}/products`, filters),
*/

// Enhanced skeleton component for category page
function CategoryPageSkeleton() {
  return (
    <div className="container">
      <div className="animate-pulse">
        {/* Breadcrumb skeleton */}
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>

        {/* Filter and sort controls skeleton */}
        <div className="flex justify-between items-center mb-6">
          <div className="h-6 bg-gray-200 rounded w-32"></div>
          <div className="h-10 bg-gray-200 rounded w-48"></div>
        </div>

        {/* Products grid skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(12)].map((_, i) => (
            <div key={i} className="space-y-3">
              <div className="h-64 bg-gray-200 rounded-lg"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>

        {/* Pagination skeleton */}
        <div className="flex justify-center mt-8">
          <div className="flex space-x-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-10 w-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Optimized server-side data fetching for category page
async function getCategoryPageData(slug, searchParams) {
  const startTime = Date.now();

  try {
    // Parse variant attributes from search params
    const variantAttributes = {};
    Object.entries(searchParams).forEach(([key, value]) => {
      if (key.startsWith('attr_')) {
        const attrName = key.replace('attr_', '');
        variantAttributes[attrName] = value.split(',').filter(Boolean);
      }
    });

    // Build filter parameters from URL search params
    const filters = {
      page: parseInt(searchParams.page) || 1,
      limit: parseInt(searchParams.limit) || 12,
      sort: searchParams.sort || 'default',
      categories: searchParams.categories?.split(',').filter(Boolean) || [],
      brands: searchParams.brands?.split(',').filter(Boolean) || [],
      sizes: searchParams.sizes?.split(',').filter(Boolean) || [],
      variantAttributes: Object.keys(variantAttributes).length > 0 ? variantAttributes : undefined,
      priceMin: parseFloat(searchParams.price_min) || null,
      priceMax: parseFloat(searchParams.price_max) || null,
      inStock: searchParams.in_stock === 'true' ? true : searchParams.in_stock === 'false' ? false : null,
      search: searchParams.search || '',
      categorySlug: slug
    };

    // Parallel data fetching for better performance
    const [categoryProductsResponse, filtersResponse] = await Promise.all([
      getCategoryProductsSSR(slug, filters),
      getProductListFiltersSSR('category', slug)
    ]);

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Category page data fetched in ${Date.now() - startTime}ms for slug: ${slug}`);
      console.log('Category Products Response Success:', categoryProductsResponse?.success);
      console.log('Filters Response Success:', filtersResponse?.success);
    }

    // Check if we have valid data
    const hasValidProducts = categoryProductsResponse?.success && categoryProductsResponse?.data?.products && Array.isArray(categoryProductsResponse.data.products);
    const hasValidFilters = filtersResponse?.success && filtersResponse?.data;

    return {
      initialData: {
        products: hasValidProducts ? categoryProductsResponse.data.products : [],
        pagination: categoryProductsResponse?.success ? categoryProductsResponse.data?.pagination || {} : {},
        category: categoryProductsResponse?.success ? categoryProductsResponse.data?.category : null
      },
      initialFilters: hasValidFilters ? filtersResponse.data : {},
      error: !hasValidProducts ? (categoryProductsResponse?.message || 'Kategori ürünleri yüklenirken bir hata oluştu.') : null,
      fetchTime: Date.now() - startTime
    };
  } catch (error) {
    console.error('Error fetching category page data:', error);
    return {
      initialData: { products: [], pagination: {}, category: null },
      initialFilters: {},
      error: 'Kategori ürünleri yüklenirken bir hata oluştu.',
      fetchTime: Date.now() - startTime
    };
  }
}

// Main SSR component
export default async function CategoryPage({ params, searchParams }) {
  try {
    const { slug } = await params;
    const resolvedSearchParams = await searchParams;

    // Performance optimization: Start data fetching immediately
    const startTime = Date.now();

    // Server-side rendering ile kategori verilerini getir
    const { initialData, initialFilters, error, fetchTime } = await getCategoryPageData(slug, resolvedSearchParams);

    // Log total page generation time in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Category page (SSR) generated in ${Date.now() - startTime}ms (data fetch: ${fetchTime}ms)`);
    }

    if (!initialData.category && !error) {
      notFound();
    }

    const category = initialData.category;

    return (
      <>
        <Breadcumb
          title={category?.name || "Kategori"}
          description={`${category?.name || "Kategori"} kategorisindeki ürünleri keşfedin`}
          crumbs={[
            { label: "Tüm Ürünler", path: "/urunler" },
            { label: category?.name || "Kategori", path: `/urunler/${slug}` }
          ]}
          fullWidth={true}
        />

        {error ? (
          <div className="container">
            <div className="alert alert-danger" role="alert">
              {error}
              <ErrorRetryButton />
            </div>
          </div>
        ) : (
          <Suspense fallback={<CategoryPageSkeleton />}>
            <Products2
              initialData={initialData}
              initialFilters={initialFilters}
              pageType="category"
              categorySlug={slug}
              initialLayout={3}
            />
          </Suspense>
        )}
      </>
    );
  } catch (error) {
    console.error('Error loading category page for slug:', params?.slug, error);

    // Better error handling with specific error types
    if (error.message?.includes('404') || error.status === 404) {
      notFound();
    }

    // For other errors, show a generic error page
    throw new Error('Kategori sayfası yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
  }
}
