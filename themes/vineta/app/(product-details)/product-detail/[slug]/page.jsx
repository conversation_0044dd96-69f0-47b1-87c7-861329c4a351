import Footer1 from "@/components/footers/Footer1";
import Header1 from "@/components/headers/Header1";
import Topbar2 from "@/components/headers/Topbar2";
import Breadcumb from "@/components/productDetails/Breadcumb";
import Description1 from "@/components/productDetails/Description1";
import Details4 from "@/components/productDetails/Details4";
import RecentlyViewedProducts from "@/components/productDetails/RecentlyViewedProducts";
import RecommendedProdtcts from "@/components/productDetails/RecommendedProducts";
import { getProductBySlugSSR } from "@/services/product";
import { generateProductMetadata } from "@/lib/seo/metadata";
import { ProductStructuredData, BreadcrumbStructuredData } from "@/components/seo/StructuredData";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import React from "react";

// Layout data import
const data = {
  productsMenu: [
    {
      categoryName: "Hydrain",
      products: [
        {
          name: "Hialuro <PERSON>remsi Temizleme Jeli",
          slug: "hialuro-kremsi-temizleme-jeli"
        },
        {
          name: "Hialuro Nemlendirici Tonik",
          slug: "hialuro-nemlendirici-tonik"
        },
        {
          name: "Misel SU H2O",
          slug: "misel-su-h2o"
        },
        {
          name: "Hialuro Ultra Nemlendirici Krem Jel",
          slug: "hialuro-ultra-nemlendirici-krem-jel"
        },
      ],
    },
    {
      categoryName: "Cicatopy",
      products: [
        {
          name: "Yoğun Nemlendirici Vücut Kremi",
          slug: "yogun-nemlendirici-vucut-kremi"
        },
        {
          name: "Canlandırıcı El Kremi",
          slug: "canlandirici-el-kremi"
        },
      ],
    },
  ]
};

// Cache configuration for better performance
export const revalidate = 3600; // Revalidate every hour
export const dynamicParams = true; // Allow dynamic params for new products

// Generate static params for popular products (optional optimization)
export async function generateStaticParams() {
  try {
    // This could fetch popular product slugs for pre-generation
    // For now, return empty array to allow all dynamic generation
    return [];
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

// Optimized metadata generation with caching
export async function generateMetadata({ params }) {
  try {
    const { slug } = await params;

    // Fetch product data for metadata generation
    const productData = await getProductBySlugSSR(slug);

    if (!productData?.success || !productData?.data?.product) {
      return {
        title: "Ürün Bulunamadı | B2C Mağaza",
        description: "Aradığınız ürün bulunamadı.",
        robots: "noindex, nofollow",
      };
    }

    // Generate comprehensive metadata
    const metadata = generateProductMetadata(productData.data);

    // Add additional SEO optimizations
    return {
      ...metadata,
      alternates: {
        canonical: `/product-detail/${slug}`,
      },
      openGraph: {
        ...metadata.openGraph,
        url: `/product-detail/${slug}`,
        type: 'website',
        images: productData.data.product.images?.map(img => ({
          url: img.imgSrc,
          alt: img.alt || productData.data.product.title,
          width: 800,
          height: 600,
        })) || [],
      },
      twitter: {
        card: 'summary_large_image',
        title: metadata.title,
        description: metadata.description,
        images: productData.data.product.imgSrc ? [productData.data.product.imgSrc] : [],
      },
    };
  } catch (error) {
    console.error('Error generating metadata for slug:', params?.slug, error);
    return {
      title: "Ürün Detayı | B2C Mağaza",
      description: "Ürün detay sayfası",
      robots: "noindex, nofollow",
    };
  }
}

/**
 * Ürün görseli için tip tanımı
 * @typedef {Object} ProductImage
 * @property {number} id - Görsel ID'si
 * @property {string} color - Renk bilgisi
 * @property {string} size - Boyut bilgisi
 * @property {string} imgSrc - Görsel URL'i
 * @property {string} alt - Alternatif metin
 */

/**
 * Ürün varyant seçenekleri için tip tanımı
 * @typedef {Object} VariantOption
 * @property {string} label - Seçenek etiketi
 * @property {string} value - Seçenek değeri
 * @property {string} display - Görüntülenecek metin
 */

/**
 * Ürün yorumu için tip tanımı
 * @typedef {Object} Review
 * @property {number} id 
 * @property {string} name 
 * @property {string} date 
 * @property {string} avatar
 * @property {number} rating
 * @property {string} comment 
 */

/**
 * Ürün metadata bilgileri
 * @typedef {Object} ProductMetadata
 * @property {string} title
 * @property {string} description
 */

/**
 * Ürün yapısını temsil eden obje
 * @typedef {Object} Product
 * @property {number} id
 * @property {string} title
 * @property {string} sku - Ürün stok kodu
 * @property {string} brand
 * @property {string} barcode
 * @property {string} categoryName
 * @property {string} categorySlug
 * @property {string} slug 
 * @property {number} price
 * @property {number} oldPrice
 * @property {number} discountPercentage
 * @property {number} stockQuantity
 * @property {boolean} inStock
 * @property {number} countdownTimer - Geri sayım sayacı
 * @property {string} imgSrc - Ana ürün görseli
 * @property {ProductImage[]} images 
 * @property {VariantOption[]} colors
 * @property {VariantOption[]} sizes
 * @property {string} description
 * @property {number} reviewRate 
 * @property {number} reviewCount
 * @property {Review[]} reviews
 * @property {ProductMetadata} metadata
 */

/**
 * @type {Product}
 */

/* //Request example

const productDetailRequests = {
  // Ürün detayını getir
  getProductDetail: (slug) => api.get(`/product/${slug}`),
*/

// Enhanced skeleton component for better UX
function ProductDetailSkeleton() {
  return (
    <div className="container">
      <div className="animate-pulse">
        {/* Breadcrumb skeleton */}
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>

        {/* Product detail skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Product images skeleton */}
          <div className="space-y-4">
            <div className="h-96 bg-gray-200 rounded-lg"></div>
            <div className="flex space-x-2">
              <div className="h-16 w-16 bg-gray-200 rounded"></div>
              <div className="h-16 w-16 bg-gray-200 rounded"></div>
              <div className="h-16 w-16 bg-gray-200 rounded"></div>
              <div className="h-16 w-16 bg-gray-200 rounded"></div>
            </div>
          </div>

          {/* Product info skeleton */}
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="h-8 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>

            <div className="space-y-2">
              <div className="h-6 bg-gray-200 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            </div>

            {/* Size options skeleton */}
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-16"></div>
              <div className="flex space-x-2">
                <div className="h-10 w-10 bg-gray-200 rounded"></div>
                <div className="h-10 w-10 bg-gray-200 rounded"></div>
                <div className="h-10 w-10 bg-gray-200 rounded"></div>
              </div>
            </div>

            {/* Add to cart skeleton */}
            <div className="space-y-4">
              <div className="flex space-x-4">
                <div className="h-12 w-24 bg-gray-200 rounded"></div>
                <div className="h-12 flex-1 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Recommended products skeleton */}
        <div className="space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-48 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Optimized main component with error boundaries
export default async function ProductDetailPage({ params }) {
  try {
    const { slug } = await params;

    // Performance optimization: Start data fetching immediately
    const startTime = Date.now();

    // Server-side rendering ile ürün detayını getir
    const productData = await getProductBySlugSSR(slug);

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Product data fetched in ${Date.now() - startTime}ms for slug: ${slug}`);
    }

    if (!productData?.success || !productData?.data?.product) {
      notFound();
    }

    const { product, recommendedProducts, relatedProducts, breadcrumb } = productData.data;

    return (
      <>

        {/* SEO Structured Data - Critical for SEO */}
        <ProductStructuredData product={product} />
        {breadcrumb && <BreadcrumbStructuredData breadcrumbs={breadcrumb} />}

        {/* Main product content */}
        <Breadcumb product={product} breadcrumb={breadcrumb} />
        <Details4 product={product} />
        <Description1 product={product} />

        {/* Lazy load recommended products for better performance */}
        {recommendedProducts && recommendedProducts.length > 0 && (
          <Suspense fallback={
            <div className="space-y-6 animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="h-48 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            </div>
          }>
            <RecommendedProdtcts products={recommendedProducts} />
          </Suspense>
        )}

        {/* Lazy load related products */}
        {relatedProducts && relatedProducts.length > 0 && (
          <Suspense fallback={
            <div className="space-y-6 animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="h-48 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            </div>
          }>
            <RecentlyViewedProducts products={relatedProducts} />
          </Suspense>
        )}

        <Footer1 paddingBottom />
      </>
    );
  } catch (error) {
    console.error('Error loading product detail page for slug:', params?.slug, error);

    // Better error handling with specific error types
    if (error.message?.includes('404') || error.status === 404) {
      notFound();
    }

    // For other errors, show a generic error page
    throw new Error('Ürün detayları yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
  }
}
