api:
  dashboard: true
  debug: true
  insecure: true  # HTTP dashboard on port 8080

entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https
          permanent: true

  websecure:
    address: ":443"

serversTransport:
  insecureSkipVerify: true

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
  file:
    filename: /config.yml

certificatesResolvers:
  letsencrypt:
    acme:
      tlsChallenge: {}
      email: <EMAIL>
      storage: acme.json
      keyType: EC256

log:
  level: INFO

accessLog: {}
