# B2B Project Docker Setup

Bu proje Docker Compose kullanarak tüm servisleri çalıştırmak için ya<PERSON>ılandırılmıştır.

## Gere<PERSON>inimler

- Docker
- Docker Compose
- Traefik (ayrı bir docker-compose dosyası ile)

## Kurulum

### 1. Environment Variables

`.env.example` dosyasını `.env` olarak kopyalayın ve gerekli değerleri düzenleyin:

```bash
cp .env.example .env
```

**Önemli Ayarlar:**
- **Database**: `ConnectionStrings__VeriTabani` - PostgreSQL bağlantı bilgileri
- **RabbitMQ**: `RabbitMQ__*` - Message queue ayarları
- **NextAuth**: `NextAuth__Secret` - JWT secret key (minimum 32 karakter)
- **SMTP**: `SmtpSettings__*` - E-posta gönder<PERSON> (MailAPI)
- **Storage**: `StorageSettings__*` - <PERSON><PERSON><PERSON> dos<PERSON>ı için depolama ayarları
- **API URLs**: `ApiSettings__*` - Mikroservisler arası iletişim
- **CORS**: `CorsHosts__*` - Frontend erişim ayarları

**Not**: Environment variables mevcut .NET kodlarındaki naming convention'a uygun olarak ayarlandı. Kod değişikliği gerekmedi!

### 2. Traefik Network Oluşturma

Önce traefik-proxy network'ünü oluşturun:

```bash
docker network create traefik-proxy
```

### 3. Traefik Kurulumu (Ayrı)

Traefik için ayrı bir docker-compose dosyası oluşturun ve çalıştırın.

### 4. Projeyi Çalıştırma

```bash
# Tüm servisleri build et ve çalıştır
docker-compose up --build

# Arka planda çalıştırmak için
docker-compose up -d --build
```

## Servisler

### Database & Infrastructure
- **PostgreSQL**: Internal network (sadece container'lar arası erişim)
- **RabbitMQ**: Management UI - http://localhost:15672
- **PgAdmin**: http://localhost:5050

### Backend APIs (Internal Network)
- **API Gateway**: Traefik üzerinden erişilebilir
- **Panel API**: Admin panel için backend
- **Web API**: Web frontend için backend  
- **Media API**: Medya dosyaları için
- **Mail API**: E-posta servisleri için

### Frontend Applications
- **B2B Frontend**: http://localhost:3000
- **Vineta Theme**: http://localhost:3001

## Port Yapılandırması

### Dışarıya Açık Portlar:
- 3000: B2B Frontend
- 3001: Vineta Theme
- 5050: PgAdmin
- 15672: RabbitMQ Management

### Internal Portlar (sadece container'lar arası):
- 5432: PostgreSQL
- 5672: RabbitMQ
- 5080: API Gateway
- 5000: Backend APIs

## Network Yapısı

- **b2b-network**: Internal network (tüm servisler)
- **traefik-proxy**: External network (Traefik ile paylaşılan)

## Volumes

- **postgres_data**: PostgreSQL verileri
- **media_data**: Media API dosyaları

## Geliştirme

### Logs İzleme

```bash
# Tüm servislerin loglarını izle
docker-compose logs -f

# Belirli bir servisin loglarını izle
docker-compose logs -f api-gateway
```

### Servis Yeniden Başlatma

```bash
# Belirli bir servisi yeniden başlat
docker-compose restart panel-api

# Belirli bir servisi yeniden build et
docker-compose up --build panel-api
```

### Container'a Bağlanma

```bash
# PostgreSQL container'ına bağlan
docker-compose exec postgres psql -U b2b_user -d b2b_database

# Backend API container'ına bağlan
docker-compose exec panel-api bash
```

## Sorun Giderme

### Container'lar Başlamıyorsa

1. Logları kontrol edin:
```bash
docker-compose logs [service-name]
```

2. Network'leri kontrol edin:
```bash
docker network ls
```

3. Traefik network'ünün var olduğundan emin olun:
```bash
docker network inspect traefik-proxy
```

### Database Bağlantı Sorunları

1. PostgreSQL container'ının çalıştığından emin olun
2. Environment variables'ları kontrol edin
3. Health check'lerin geçtiğinden emin olun

### Frontend Build Sorunları

1. Node.js version'ını kontrol edin (20-alpine kullanılıyor)
2. Package.json ve lock dosyalarının doğru olduğundan emin olun
3. Standalone output'un etkin olduğundan emin olun

**Not**: Demo amaçlı olarak ESLint ve TypeScript uyarıları build sırasında görmezden gelinmektedir. Production ortamında bu ayarları kaldırmanız önerilir.

### .NET API Build Sorunları

**"The application 'ApiGateway.dll' does not exist" hatası:**

1. Dockerfile'ların doğru multi-stage build kullandığından emin olun
2. Build context'in doğru olduğunu kontrol edin (B2B.Backend klasörü)
3. Container'ı yeniden build edin:
```bash
docker-compose build --no-cache api-gateway
docker-compose build --no-cache mail-api
```

4. Build loglarını kontrol edin:
```bash
docker-compose build api-gateway 2>&1 | grep -i error
```

**Dependency sorunları:**
1. .csproj dosyalarının doğru kopyalandığından emin olun
2. NuGet restore işleminin başarılı olduğunu kontrol edin
3. Proje referanslarının doğru olduğunu kontrol edin

## Temizlik

```bash
# Tüm container'ları durdur ve sil
docker-compose down

# Volume'ları da sil (DİKKAT: Veri kaybı olur!)
docker-compose down -v

# Kullanılmayan image'ları temizle
docker image prune -a
```
