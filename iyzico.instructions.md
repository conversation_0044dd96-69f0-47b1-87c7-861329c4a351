## CF Başlatma Modelleri 
POST https://api.iyzipay.com/payment/iyzipos/checkoutform/initialize/auth/ecom
### OrderStartRequest Modeli

| Parametre | Tip | Açıklama |
|-----------|-----|----------|
| price* | Decimal | Ödeme sepet tutarı. Kırılım tutarlar toplamı, sepet tutarına eşit olmalı. |
| contactName(BillingAddress)* | String | Contact name of billing address |
| registrationAddress(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait kayıt adresi. |
| ip(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait IP adresi. |
| email(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait e-posta bilgisi. E-posta adresi alıcıya ait geçerli ve erişilebilir bir adres olmalıdır. |
| country(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait ülke bilgisi. |
| city(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait şehir bilgisi. |
| identityNumber(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait kimlik (TCKN) numarası. |
| surname(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait soyad. |
| name(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait ad. |
| id(Buyer)* | String | Üye işyeri tarafındaki alıcıya ait ad. |
| currency* | String | Para birimi. Default değeri TL'dir. Kullanılabilen diğer değerler ise USD, EUR, GBP ve IRR'dir. |
| paidPrice* | Decimal | İndirim, vergi, taksit komisyonları gibi değerlerin dahil edildiği tutar. |
| zipCode(BillingAddress) | String | Üye işyeri tarafındaki fatura adresi posta kodu. |
| zipCode(ShippingAddress) | String | Üye işyeri tarafındaki teslimat adresi posta kodu. |
| zipCode(Buyer) | String | Üye işyeri tarafındaki alıcıya ait posta kodu. |
| category1(BasketItems) | String | Üye işyeri tarafındaki sepetteki ürüne ait kategori 1. |
| name(BasketItems)* | String | Üye işyeri tarafındaki sepetteki ürüne ait ismi. |
| price(BasketItems)* | Decimal | Üye işyeri tarafındaki sepetteki ürüne ait tutar. 0 ve 0'dan küçük olamaz, tutarlar toplamı sepet tutarına (price) eşit olmalıdır. |
| itemType(BasketItems)* | String | Üye işyeri tarafındaki sepetteki ürüne ait tip. Geçerli enum değerler: PHYSICAL ve VIRTUAL |
| id(BasketItems)* | String | Üye işyeri tarafındaki sepetteki ürüne ait id. Not: Bir ödeme isteğine maksimum 500 basketItem eklenebilir. |
| address(ShippingAddress)* | String | Üye işyeri tarafındaki teslimat adresi. Sepetteki ürünlerden en az 1 tanesi fiziksel ürün (itemType=PHYSICAL) ise zorunludur. |
| country(ShippingAddress)* | String | Üye işyeri tarafındaki teslimat adresi ülke bilgisi. Sepetteki ürünlerden en az 1 tanesi fiziksel ürün (itemType=PHYSICAL) ise zorunludur. |
| city(ShippingAddress)* | String | Üye işyeri tarafındaki teslimat adresi şehir bilgisi. Sepetteki ürünlerden en az 1 tanesi fiziksel ürün (itemType=PHYSICAL) ise zorunludur. |
| contactName(ShippingAddress)* | String | Üye işyeri tarafındaki teslimat adresi ad soyad bilgisi. Sepetteki ürünlerden en az 1 tanesi fiziksel ürün (itemType=PHYSICAL) ise zorunludur. |
| address(BillingAddress)* | String | Address details of billing address |
| country(BillingAddress)* | String | Country name of billing address |
| city(BillingAddress)* | String | City name of billing address |
| paymentGroup | String | Ödeme grubu, varsayılan PRODUCT. Geçerli değerler enum içinde sunulmaktadır: PRODUCT, LISTING, SUBSCRIPTION |
| basketId | String | Üye işyeri sepet id'si. |
| conversationId | String | İstek esnasında gönderip, sonuçta alabileceğiniz bir değer, request/response eşleşmesi yapmak için kullanılabilir. |
| locale | String | iyzico istek sonucunda dönen metinlerin dilini ayarlamak için kullanılır. Varsayılan değeri tr'dir. |
| lastLoginDate(Buyer) | String | Üye işyeri tarafındaki alıcıya ait son giriş tarihi. Tarih formatı 2015- 09-17 23:45:06 şeklinde olmalıdır. |
| registrationDate(Buyer) | String | Üye işyeri tarafındaki alıcıya ait kayıt tarihi. Tarih formatı 2015-09- 17 23:45:06 şeklinde olmalıdır. |
| gsmNumber(Buyer) | String | Üye işyeri tarafındaki alıcıya ait GSM numarası. |
| category2(BasketItems) | String | Üye işyeri tarafındaki sepetteki ürüne ait kategori 2. |
| paymentSource | String | Ödeme kanalı. Geçerli değerler SHOPIFY, MAGENTO, PRESTASHOP, WOOCOMMERCE, OPENCART |
| callbackUrl* | String | Ödeme akışında üye işyerine başarılı ve hatalı sonucu bildirmek üzere alınan URL adresi. Geçerli bir ssl sertifikasına sahip olmalıdır. |
| enabledInstallments | Integer | Taksit bilgisi, tek çekim için 1 gönderilmelidir. Geçerli değerler: 1, 2, 3, 6, 9. |

### OrderStartResponse Modeli

| Parametre | Tip | Açıklama |
|-----------|-----|----------|
| checkoutFormContent | string | Checkout formun açılması için gereken HTML kod. |
| paymentPageUrl | string | iyzico ortak ödeme sayfasına erişimi için gereken URL. |
| token | string | Checkout form için oluşturulan tekil değer. Her istek için özel üretilir ve işyerine dönülür. Ödemenin sonucunu öğrenmek için zorunlu bir alandır. |
| tokenExpireTime | Integer | Checkout form için üretilmiş olan token değerinin geçerlilik süresi. |
| status | string | Yapılan isteğin sonucunu bildirir. İşlem başarılı ise success, hatalı ise failure döner. |
| errorCode | string | İşlem hatalıysa, bu hataya dair belirtilen koddur. |
| errorMessage | string | İşlem hatalıysa, bu hataya dair belirtilen mesajdır, locale parametresine göre dil desteği sunar. |
| errorGroup | string | İşlem hatalıysa, bu hataya dair belirtilen gruptur. |
| locale | string | İstekte belirtilen locale değeri geri dönülür, varsayılan değeridir. |
| systemTime | integer | Dönen sonucun o anki unix timestamp değeridir. |
| conversationId | string | İstek esnasında gönderilmişse, sonuçta aynen geri iletilir. |

## CF Sorgulama Modelleri
POST https://api.iyzipay.com/payment/iyzipos/checkoutform/auth/ecom/detail

### OrderQueryRequest Modeli

| Parametre | Tip | Açıklama |
|-----------|-----|----------|
| conversationId | String | İstek esnasında gönderip, sonuçta alabileceğiniz bir değer, request/response eşleşmesi yapmak için kullanılabilir. |
| locale | String | iyzico istek sonucunda dönen metinlerin dilini ayarlamak için kullanılır. Varsayılan değeri tr'dir. |
| token* | String | Checkout form için oluşturulan tekil değer. Her istek için özel üretilir ve işyerine dönülür. Ödemenin sonucunu öğrenmek için zorunlu bir alandır. |

### OrderQueryResponse Modeli

| Parametre | Tip | Açıklama |
|-----------|-----|----------|
| token | String | Checkout form için oluşturulan tekil değer. Her istek için özel üretilir ve işyerine dönülür. Ödemenin sonucunu öğrenmek için zorunlu bir alandır. |
| callbackUrl | String | Ödeme akışında üye işyerine başarılı ve hatalı sonucu bildirmek üzere alınan URL adresi. |
| status | String | Yapılan isteğin sonucunu bildirir. Başarılı ise success, hatalı ise failure döner. |
| paymentStatus | String | Ödeme isteğinin durumunu gösterir. Success ise karttan ilgili tutar çekilmiştir. SUCCESS, FAILURE, INIT_THREEDS, CALLBACK_THREEDS, BKM_POS_SELECTED, CALLBACK_PECCO |
| errorCode | String | İşlem hatalıysa, bu hataya dair belirtilen koddur. |
| errorMessage | String | İşlem hatalıysa, bu hataya dair belirtilen mesajdır, locale parametresine göre dil desteği sunar. |
| errorGroup | String | İşlem hatalıysa, bu hataya dair belirtilen gruptur. |
| locale | String | İstekte belirtilen locale değeri geri dönülür, varsayılan değeri tr'dir. |
| systemTime | Long | Dönen sonucun o anki unix timestamp değeridir. |
| conversationId | String | İstek esnasında gönderilmişse, sonuçta aynen geri iletilir. |
| paymentId | String | Ödemeye ait id, üye işyeri tarafından mutlaka saklanmalıdır. Ödemenin iptali ve iyzico ile iletişimde kullanılır. |
| price | Decimal | Ödeme sepet tutarı. Kırılım tutarlar toplamı sepet tutarına eşit olmalı. |
| paidPrice | Decimal | İndirim vade farkı vs. hesaplanmış POS'tan geçen, tahsil edilen, nihai tutar. |
| currency | String | Ödemenin alındığı para birimi. |
| installment | Integer | Ödemenin taksit bilgisi, tek çekim için 1 döner. Geçerli değerler: 1, 2, 3, 6, 9, 12 |
| basketId | String | Üye işyeri tarafından gönderilen sepet id'si. |
| binNumber | String | Ödeme yapılan kartın ilk 6 hanesi. |
| lastFourDigits | String | Ödeme yapılan kartın son 4 hanesi. |
| cardAssociation | String | Eğer ödeme yapılan kart yerel bir kart ise, kartın ait olduğu kuruluş. Geçerli değerler: VISA, MASTER_CARD, AMERICAN_EXPRESS, TROY |
| cardFamily | String | Eğer ödeme yapılan kart yerel bir kart ise, kartın ait olduğu aile. Geçerli değerler: Bonus, Axess, World, Maximum, Paraf, CardFinans, Advantage |
| cardType | String | Eğer ödeme yapılan kart yerel bir kart ise, kartın ait olduğu tipi. Geçerli değerler: CREDIT_CARD, DEBIT_CARD, PREPAID_CARD |
| fraudStatus | Integer | Ödeme işleminin fraud filtrelerine göre durumu. Eğer ödemenin fraud risk skoru düşük ise ödemeye anında onay verilir bu durumda 1 değeri döner. Eğer fraud risk skoru yüksek ise ödeme işlemi reddedilir ve -1 döner. Eğer ödeme işlemi daha sonradan incelenip karar verilecekse 0 döner. Geçerli değerler: 0, -1 ve 1. Üye işyeri sadece 1 olan işlemlerde ürünü kargoya vermelidir, 0 olan işlemler için bilgilendirme beklemelidir. |
| iyziCommissionFee | Decimal | Ödemeye ait iyzico işlem ücreti . |
| iyziCommissionRateAmount | Decimal | Ödemeye ait iyzico işlem komisyon tutarı. |
| merchantCommissionRate | Decimal | Üye işyerinin uyguladığı vade/komisyon oranı. Örneğin price=100, paidPrice=110 ise üye işyeri vade/komisyon oranı %10'dur. Bilgi amaçlıdır. |
| merchantCommissionRateAmount | Decimal | Üye işyerinin uyguladığı vade/komisyon tutarı. Örneğin price=100, paidPrice=110 ise üye işyeri vade/komisyon tutarı 10'dur. Bilgi amaçlıdır. |
| paymentTransactionId (ItemTransactions) | String | Ödeme kırılımına ait id, üye işyeri tarafından mutlaka saklanmalıdır. Ödeme kırılımının iadesi, onayı, onay geri çekmesi ve iyzico ile iletişimde kullanılır. Tercihen itemId ile ilişkili bir şekilde tutulmalıdır. |
| itemId (ItemTransactions) | String | Üye işyeri tarafından iletilen, sepetteki ürüne ait id. |
| price (ItemTransactions) | Decimal | Üye işyeri tarafındaki sepetteki ürüne ait tutar. |
| paidPrice (ItemTransactions) | Decimal | Tahsilat tutarının kırılım bazındaki dağılımı. Üye işyeri tarafından mutlaka saklanmalıdır. |
| transactionStatus (ItemTransactions) | Integer | Ödeme kırılımının durumu. Ödeme fraud kontrolünde ise 0 değeri döner, bu durumda fraudStatus değeri de 0'dır. Ödeme, fraud kontrolünden sonra reddedilirse -1 döner. Pazaryeri modelinde ürüne onay verilene dek bu değer 1 olarak döner. Pazaryeri modelinde ürüne onay verilmişse bu değer 2 olur. Geçerli değerler: 0, -1, 1, 2. |
| blockageRate (ItemTransactions) | Decimal | Kırılım bazında üye işyeri blokaj oranı. iyzico – üye işyeri anlaşmasına göre, üye işyerine işlem bazında blokaj uygulayabilir. Bu blokaj üye işyeri fraud riskini önlemek içindir, blokaj süresi boyunca para iyzico'da tutulur, bu süre sonrasında üye işyerine gönderilir. |
| blockageRateAmountMerchant (ItemTransactions) | Decimal | Kırılım bazında üye işyeri blokaj tutarının, üye işyerine yansıyan rakamı. Blokaj tutarı mümkün olduğunca üye işyerine yansıtılır. Eğer blokaj tutarı, üye işyeri tutarından daha büyükse bu durumda alt üye işyerine de yansıtılır. |
| blockageResolvedDate (ItemTransactions) | String | İşlem bazında blokaj çözülme tarihi. yyyy-MM-dd HH:mm:ss formatındadır, örneğin 2015-10-19 14:36:52. |
| iyziCommissionFee (ItemTransactions) | Decimal | iyzico işlem ücretinin kırılım bazında dağılmış tutarı. |
| iyziCommissionRateAmount (ItemTransactions) | Decimal | iyzico işlem komisyon tutarının kırılım bazında dağılmış tutarı. |
| merchantCommissionRate (ItemTransactions) | Decimal | Üye işyerinin uyguladığı vade/komisyon oranının kırılım bazında dağılmış oranı. |
| merchantCommissionRateAmount (ItemTransactions) | Decimal | Üye işyerinin uyguladığı vade/komisyon tutarıın, kırılım bazında dağılmış tutarı. |
| merchantPayoutAmount (ItemTransactions) | Decimal | Bu kırılım için, iyzico işlem ücreti, komisyon tutarı ve blokajlar düşüldükten sonra üye işyerine gönderilecek tutar. |
| paidPrice(ItemTransactions) (convertedPayout) | Decimal | Tahsilat tutarının kırılım bazındaki dağılımı. Üye işyeri tarafından mutlaka saklanmalıdır. |
| iyziCommissionFee (ItemTransactions) (convertedPayout) | Decimal | iyzico işlem ücretinin kırılım bazında dağılmış tutarı. |
| iyziCommissionRateAmount (ItemTransactions) (convertedPayout) | Decimal | iyzico işlem komisyon tutarının kırılım bazında dağılmış tutarı. |
| blockageRateAmountMerchant (ItemTransactions) (convertedPayout) | Decimal | Kırılım bazında üye işyeri blokaj tutarının, üye işyerine yansıyan rakamı. Blokaj tutarı mümkün olduğunca üye işyerine yansıtılır. Eğer blokaj tutarı, üye işyeri tutarından daha büyükse bu durumda alt üye işyerine de yansıtılır. |
| merchantPayoutAmount (ItemTransactions) (convertedPayout) | Decimal | Bu kırılım için, iyzico işlem ücreti, komisyon tutarı ve blokajlar düşüldükten sonra üye işyerine gönderilecek tutar. |
| iyziConversationRate (ItemTransactions) (convertedPayout) | Decimal | Bu kırılım için, iyzico işlem ücreti, komisyon tutarı ve blokajlar düşüldükten sonra üye işyerine gönderilecek tutar. |
| iyziConversationRateAmount (ItemTransactions) (convertedPayout) | Decimal | Bu kırılım için, iyzico işlem ücreti, komisyon tutarı ve blokajlar düşüldükten sonra üye işyerine gönderilecek tutar. |
| currency (ItemTransactions) (convertedPayout) | String | Ödemenin alındığı para birimi. |
| mdStatus | String | Bankadan dönen değerdir. Sadece ödeme başarısız ise ve işlem 3ds ile yapılmışsa bu değer döner. 0,2,3,4,5,6,7 değerlerini alabilir. |

## CF Entegrasyon Örneği

### CF Örnek Entegrasyon
Bu dokümanda, Redirect Form ile CF'nin kapsamlı entegrasyon adımlarını derinlemesine inceleyeceğiz ve hızlı, kolay ve güvenli bir entegrasyon için örnek istekler, yanıtlar ve en iyi uygulamalar sağlayacağız.

#### Ön Gereksinimler
Devam etmeden önce, lütfen aşağıdaki ön koşullara sahip olduğunuzdan emin olunuz.

- Sandbox Hesabı
- API ve Güvenlik Anahtarı
- API çağrılarını yönetmek için bir yazılım aracı; Postman, Insonmina, Github clients

#### Genel Bakış
CF'nin uygulanması, birbirini izleyen iki POST isteğinin birbiri ardına düzenlenmesini gerektirir.

Kısacası; 

1. İlk olarak, bir CF Başlatma POST isteğini göstereceğiz.
2. Ardından, CF Sorgulama POST isteği ile tamamlayacağız.

#### Adımlar
1. CF Başlatma
   - Token + URL
   - Yönlendirme
2. CF Sorgulama
3. Webhook

##### Adım 1 - CF Başlatma
CF uygulaması, CF Başlatma POST isteği ile başlar.

Bu adım, bir POST isteği göndermeyi içerir ve istek yapısı; locale, conversationId, price, basketId, paymentGroup, callbackUrl, currency, paidPrice, enabledInstallments, buyer details, shipping address, billing address ve basketItems gibi çeşitli parametreler içerir.

Aşağıdaki nesne, örnek bir CF Başlatma istek gövdesidir.

**Örnek CF Başlatma İsteği:**
```json
{
   "locale":"en",
   "conversationId":"sampleConversationId",
   "price":"5.2",
   "basketId":"B67832",
   "paymentGroup":"PRODUCT",
   "buyer":{
      "id":"BY789",
      "name":"Higher",
      "surname":"Faster",
      "identityNumber":"74300864791",
      "email":"<EMAIL>",
      "gsmNumber":"+905555434332",
      "registrationAddress":"Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
      "city":"Istanbul",
      "country":"Turkey",
      "ip":"************"
   },
   "shippingAddress":{
      "address":"Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
      "contactName":"Jane Doe",
      "city":"Istanbul",
      "country":"Turkey"
   },
   "billingAddress":{
      "address":"Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
      "contactName":"Jane Doe",
      "city":"Istanbul",
      "country":"Turkey"
   },
   "basketItems":[
      {
         "id":"BI101",
         "price":"5.2",
         "name":"Binocular",
         "category1":"Category 1",
         "itemType":"PHYSICAL"
      }
   ],
   "callbackUrl":"YOUR_CALLBACKURL",
   "currency":"TRY",
   "paidPrice":"5.2",
   "paymentSource":"zooz",
   "enabledInstallments":[
       2,3
   ]
}
```

Yukarıdaki isteği tetikledikten sonra, çoğunlukla izleyeceğiniz 3 (üç) senaryo vardır. Yanıt paymentPageUrl adlı bir değişken içerir.

**Örnek CF Başlatma Yanıtı:**
```json
{
    "status": "success",
    "locale": "en",
    "systemTime": *************,
    "conversationId": "sampleConversationId",
    "token": "a5b67652-c24a-4347-b61e-6c957bf30f1b",
    "tokenExpireTime": 1800,
    "paymentPageUrl": "https://sandbox-cpp.iyzipay.com?token=a5b67652-c24a-4347-b61e-6c957bf30f1b&lang=en",
    "payWithIyzicoPageUrl": "https://sandbox-ode.iyzico.com/?token=a5b67652-c24a-4347-b61e-6c957bf30f1b&lang=en"
}
```

##### Adım 2 - Token + URL
Burada token ve paymentPageUrl, ödeme yolculuğunun geri kalanı için temel düğümlerdir.

Bu adımda, önceki adımda aldığınız yanıttan token ve paymentPageUrl'yi çıkarmanız gerekir. Token daha sonra doğrulama için kaydedilmelidir ve kullanıcıyı ödeme sayfasına yönlendirmek için paymentPageUrl kullanılır.

- **token:** Satıcıların, ödeme yolculuğunun en sonunda ödemeyle yüzleşmek ve doğrulamak için token parametresini kaydetmesi beklenir.
- **paymentPageUrl:** ödeme sayfasının kısaca göründüğü yer. paymentPageUrl'ye yönlendirme, son kullanıcılar için ödeme işlemlerini güvenli bir şekilde gerçekleştirmek için bir arayüz sağlar.

##### Adım 3 - Yönlendirme
Yönlendirme aşaması V genel olarak CF Başlatma ve CF Sorgulama adımlarını birbirine bağlar.

iyzico, CF ödeme sayfasındaki son kullanıcı etkileşimini takip ederek sayfayı otomatik olarak "callbackUrl" parametresinde verilen adrese yönlendirecektir. Yönlendirme POST'u şunları içerir;

| Parametre ismi | Tip | Açıklama |
|----------------|-----|----------|
| token | String | Her ödeme formu isteği için oluşturulan benzersiz değer. Bu belirteç, ödeme sonucuna erişmek için kullanılır. |

##### Adım 4 - CF Sorgulama
CF Sorgulama, PWI zincirinin son adımıdır. Temel olarak, bir önceki adımda bir ödemeyi henüz kabul ettik. Şimdi bu adımda işlemi yasallaştırma zamanı.

Aşağıdaki örnek, örnek bir CF Sorgulama istek yapısıdır.

**Örnek CF Sorgulama İsteği:**
```json
{
    "locale": "en",
    "token": "a5b67652-c24a-4347-b61e-6c957bf30f1b",
    "conversationId": "sampleConversationId"
}
```

Yukarıdaki isteği tetikledikten sonra, çoğunlukla izleyeceğiniz 2 (iki) senaryo vardır. Yanıt PaymentStatus değişkeninde SUCCESS parametresini içerir.

**Örnek CF Sorgulama Yanıtı:**
```json
{
    "status": "success",
    "locale": "en",
    "systemTime": 1687113618054,
    "conversationId": "sampleConversationId",
    "price": 5.20000000,
    "paidPrice": 5.20000000,
    "installment": 1,
    "paymentId": "19831123",
    "fraudStatus": 1,
    "merchantCommissionRate": 0E-8,
    "merchantCommissionRateAmount": 0E-8,
    "iyziCommissionRateAmount": 0.20800000,
    "iyziCommissionFee": 0.25000000,
    "cardType": "CREDIT_CARD",
    "cardAssociation": "TROY",
    "cardFamily": "Cardfinans",
    "binNumber": "979203",
    "lastFourDigits": "0000",
    "basketId": "B67832",
    "currency": "TRY",
    "itemTransactions": [
        {
            "itemId": "BI101",
            "paymentTransactionId": "21097896",
            "transactionStatus": 2,
            "price": 5.20000000,
            "paidPrice": 5.20000000,
            "merchantCommissionRate": 0E-8,
            "merchantCommissionRateAmount": 0E-8,
            "iyziCommissionRateAmount": 0.20800000,
            "iyziCommissionFee": 0.25000000,
            "blockageRate": 0E-8,
            "blockageRateAmountMerchant": 0E-8,
            "blockageRateAmountSubMerchant": 0E-8,
            "blockageResolvedDate": "2023-06-26 00:00:00",
            "subMerchantPrice": 0E-8,
            "subMerchantPayoutRate": 0E-8,
            "subMerchantPayoutAmount": 0E-8,
            "merchantPayoutAmount": 4.74200000,
            "convertedPayout": {
                "paidPrice": 5.20000000,
                "iyziCommissionRateAmount": 0.20800000,
                "iyziCommissionFee": 0.25000000,
                "blockageRateAmountMerchant": 0E-8,
                                "blockageRateAmountSubMerchant": 0E-8,
                "merchantPayoutAmount": 4.74200000,
                "subMerchantPayoutAmount": 0E-8,
                "iyziConversionRate": 0E-8,
                "iyziConversionRateAmount": 0E-8,
                "currency": "TRY"
            }
        }
    ],
    "token": "a5b67652-c24a-4347-b61e-6c957bf30f1b",
    "callbackUrl": "YOUR_CALLBACKURL",
    "paymentStatus": "SUCCESS",
    "phase": "AUTH",
    "mdStatus": 1,
    "hostReference": "mock00007iyzihostrfn"
}
```

##### Adım 5 - Webhook
Webhook, CF entegrasyonunun isteğe bağlı bir parçasıdır. Webhook, ödeme durumundaki değişiklikleri gerçek zamanlı olarak takip etmek için kullanılır.

Webhook'lar, ödeme durumundaki değişiklikleri gerçek zamanlı olarak takip etmek için kullanılır. Webhook'lar, ödeme durumundaki değişiklikleri gerçek zamanlı olarak takip etmek için kullanılır.

#### Sonuç
Bu dokümanda, CF entegrasyonunun kapsamlı adımlarını derinlemesine inceledik ve hızlı, kolay ve güvenli bir entegrasyon için örnek istekler, yanıtlar ve en iyi uygulamalar sağladık.

## Ödeme Formu Entegrasyonu

### Ödeme Formu Entegrasyonu
Bu dokümanda, Ödeme Formu entegrasyonunun kapsamlı adımlarını derinlemesine inceleyeceğiz ve hızlı, kolay ve güvenli bir entegrasyon için örnek istekler, yanıtlar ve en iyi uygulamalar sağlayacağız.

#### Ön Gereksinimler
Devam etmeden önce, lütfen aşağıdaki ön koşullara sahip olduğunuzdan emin olunuz.

- Sandbox Hesabı
- API ve Güvenlik Anahtarı
- API çağrılarını yönetmek için bir yazılım aracı; Postman, Insonmina, Github clients

#### Genel Bakış
Ödeme Formu entegrasyonu, birbirini izleyen iki POST isteğinin birbiri ardına düzenlenmesini gerektirir.

Kısacası; 

1. İlk olarak, bir Ödeme Formu Başlatma POST isteğini göstereceğiz.
2. Ardından, Ödeme Formu Sorgulama POST isteği ile tamamlayacağız.

#### Adımlar
1. Ödeme Formu Başlatma
   - Token + HTML
   - Ödeme Formu Gösterimi
2. Ödeme Formu Sorgulama
3. Webhook

##### Adım 1 - Ödeme Formu Başlatma
Ödeme Formu entegrasyonu, Ödeme Formu Başlatma POST isteği ile başlar.

Bu adım, bir POST isteği göndermeyi içerir ve istek yapısı; locale, conversationId, price, basketId, paymentGroup, callbackUrl, currency, paidPrice, enabledInstallments, buyer details, shipping address, billing address ve basketItems gibi çeşitli parametreler içerir.

Aşağıdaki nesne, örnek bir Ödeme Formu Başlatma istek gövdesidir.

**Örnek Ödeme Formu Başlatma İsteği:**
```json
{
   "locale":"en",
   "conversationId":"sampleConversationId",
   "price":"5.2",
   "basketId":"B67832",
   "paymentGroup":"PRODUCT",
   "buyer":{
      "id":"BY789",
      "name":"Higher",
      "surname":"Faster",
      "identityNumber":"74300864791",
      "email":"<EMAIL>",
      "gsmNumber":"+905555434332",
      "registrationAddress":"Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
      "city":"Istanbul",
      "country":"Turkey",
      "ip":"************"
   },
   "shippingAddress":{
      "address":"Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
      "contactName":"Jane Doe",
      "city":"Istanbul",
      "country":"Turkey"
   },
   "billingAddress":{
      "address":"Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
      "contactName":"Jane Doe",
      "city":"Istanbul",
      "country":"Turkey"
   },
   "basketItems":[
      {
         "id":"BI101",
         "price":"5.2",
         "name":"Binocular",
         "category1":"Category 1",
         "itemType":"PHYSICAL"
      }
   ],
   "callbackUrl":"YOUR_CALLBACKURL",
   "currency":"TRY",
   "paidPrice":"5.2",
   "paymentSource":"zooz",
   "enabledInstallments":[
       2,3
   ]
}
```

Yukarıdaki isteği tetikledikten sonra, çoğunlukla izleyeceğiniz 3 (üç) senaryo vardır. Yanıt checkoutFormContent adlı bir değişken içerir.

**Örnek Ödeme Formu Başlatma Yanıtı:**
```json
{
    "status": "success",
    "locale": "en",
    "systemTime": *************,
    "conversationId": "sampleConversationId",
    "token": "a5b67652-c24a-4347-b61e-6c957bf30f1b",
    "tokenExpireTime": 1800,
    "checkoutFormContent": "<script type=\"text/javascript\">if (typeof iyziInit == 'undefined') {var iyziInit = {currency:\"TRY\",token:\"a5b67652-c24a-4347-b61e-6c957bf30f1b\",price:5.2,locale:\"en\",baseUrl:\"https://sandbox-api.iyzipay.com\", merchantGatewayBaseUrl:\"https://sandbox-merchantgw.iyzipay.com\", registerCardEnabled:false,bkmEnabled:false,bankTransferEnabled:false,bankTransferRedirectUrl:\"https://sandbox-api.iyzipay.com/payment/bank-transfer/view?token=a5b67652-c24a-4347-b61e-6c957bf30f1b&lang=en\",bankTransferCustomUIProps:{},userCards:[],fundEnabled:false,memberCheckoutOtpData:{},force3Ds:false,isSandbox:true,storeNewCardEnabled:true,paymentWithNewCardEnabled:true,enabledApmTypes:[],enabledCardFamily:\"\",binNumber:\"\",createTag:function(){var iyziCSSTag = document.createElement('link');iyziCSSTag.setAttribute('rel','stylesheet');iyziCSSTag.setAttribute('type','text/css');iyziCSSTag.setAttribute('href','https://sandbox-static.iyzipay.com/checkoutform/css/main.min.css?v=2023-06-18T12:12:10');document.head.appendChild(iyziCSSTag);var iyziJSTag = document.createElement('script');iyziJSTag.setAttribute('src','https://sandbox-static.iyzipay.com/checkoutform/js/iyzi.min.js?v=2023-06-18T12:12:10');document.head.appendChild(iyziJSTag);}};iyziInit.createTag();}</script>"
}
```

##### Adım 2 - Token + HTML
Burada token ve checkoutFormContent, ödeme yolculuğunun geri kalanı için temel düğümlerdir.

Bu adımda, önceki adımda aldığınız yanıttan token ve checkoutFormContent'i çıkarmanız gerekir. Token daha sonra doğrulama için kaydedilmelidir ve kullanıcıya ödeme formunu göstermek için checkoutFormContent kullanılır.

- **token:** Satıcıların, ödeme yolculuğunun en sonunda ödemeyle yüzleşmek ve doğrulamak için token parametresini kaydetmesi beklenir.
- **checkoutFormContent:** ödeme formunun kısaca göründüğü yer. checkoutFormContent, son kullanıcılar için ödeme işlemlerini güvenli bir şekilde gerçekleştirmek için bir arayüz sağlar.

##### Adım 3 - Ödeme Formu Gösterimi
Ödeme Formu gösterimi aşaması, Ödeme Formu Başlatma ve Ödeme Formu Sorgulama adımlarını birbirine bağlar.

iyzico, Ödeme Formu'ndaki son kullanıcı etkileşimini takip ederek sayfayı otomatik olarak "callbackUrl" parametresinde verilen adrese yönlendirecektir. Yönlendirme POST'u şunları içerir;

| Parametre ismi | Tip | Açıklama |
|----------------|-----|----------|
| token | String | Her ödeme formu isteği için oluşturulan benzersiz değer. Bu belirteç, ödeme sonucuna erişmek için kullanılır. |

##### Adım 4 - Ödeme Formu Sorgulama
Ödeme Formu Sorgulama, PWI zincirinin son adımıdır. Temel olarak, bir önceki adımda bir ödemeyi henüz kabul ettik. Şimdi bu adımda işlemi yasallaştırma zamanı.

Aşağıdaki örnek, örnek bir Ödeme Formu Sorgulama istek yapısıdır.

**Örnek Ödeme Formu Sorgulama İsteği:**
```json
{
    "locale": "en",
    "token": "a5b67652-c24a-4347-b61e-6c957bf30f1b",
    "conversationId": "sampleConversationId"
}
```

Yukarıdaki isteği tetikledikten sonra, çoğunlukla izleyeceğiniz 2 (iki) senaryo vardır. Yanıt PaymentStatus değişkeninde SUCCESS parametresini içerir.

**Örnek Ödeme Formu Sorgulama Yanıtı:**
```json
{
    "status": "success",
    "locale": "en",
    "systemTime": 1687113618054,
    "conversationId": "sampleConversationId",
    "price": 5.20000000,
    "paidPrice": 5.20000000,
    "installment": 1,
    "paymentId": "19831123",
    "fraudStatus": 1,
    "merchantCommissionRate": 0E-8,
    "merchantCommissionRateAmount": 0E-8,
    "iyziCommissionRateAmount": 0.20800000,
    "iyziCommissionFee": 0.25000000,
    "cardType": "CREDIT_CARD",
    "cardAssociation": "TROY",
    "cardFamily": "Cardfinans",
    "binNumber": "979203",
    "lastFourDigits": "0000",
    "basketId": "B67832",
    "currency": "TRY",
    "itemTransactions": [
        {
            "itemId": "BI101",
            "paymentTransactionId": "21097896",
            "transactionStatus": 2,
            "price": 5.20000000,
            "paidPrice": 5.20000000,
            "merchantCommissionRate": 0E-8,
            "merchantCommissionRateAmount": 0E-8,
            "iyziCommissionRateAmount": 0.20800000,
            "iyziCommissionFee": 0.25000000,
            "blockageRate": 0E-8,
            "blockageRateAmountMerchant": 0E-8,
            "blockageRateAmountSubMerchant": 0E-8,
            "blockageResolvedDate": "2023-06-26 00:00:00",
            "subMerchantPrice": 0E-8,
            "subMerchantPayoutRate": 0E-8,
            "subMerchantPayoutAmount": 0E-8,
            "merchantPayoutAmount": 4.74200000,
            "convertedPayout": {
                "paidPrice": 5.20000000,
                "iyziCommissionRateAmount": 0.20800000,
                "iyziCommissionFee": 0.25000000,
                "blockageRateAmountMerchant": 0E-8,
                "blockageRateAmountSubMerchant": 0E-8,
                "merchantPayoutAmount": 4.74200000,
                "subMerchantPayoutAmount": 0E-8,
                "iyziConversionRate": 0E-8,
                "iyziConversionRateAmount": 0E-8,
                "currency": "TRY"
            }
        }
    ],
    "token": "a5b67652-c24a-4347-b61e-6c957bf30f1b",
    "callbackUrl": "YOUR_CALLBACKURL",
    "paymentStatus": "SUCCESS",
    "phase": "AUTH",
    "mdStatus": 1,
    "hostReference": "mock00007iyzihostrfn"
}
```

##### Adım 5 - Webhook
Webhook, Ödeme Formu entegrasyonunun isteğe bağlı bir parçasıdır. Webhook, ödeme durumundaki değişiklikleri gerçek zamanlı olarak takip etmek için kullanılır.

Webhook'lar, ödeme durumundaki değişiklikleri gerçek zamanlı olarak takip etmek için kullanılır.
https://docs.iyzico.com/ek-servisler/webhook

#### Sonuç
Bu dokümanda, Ödeme Formu entegrasyonunun kapsamlı adımlarını derinlemesine inceledik ve hızlı, kolay ve güvenli bir entegrasyon için örnek istekler, yanıtlar ve en iyi uygulamalar sağladık.