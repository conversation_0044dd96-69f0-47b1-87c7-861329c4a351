using MassTransit;
using Core.Events;

namespace WebApi.Extensions;

public static class MassTransitExtensions
{
    public static IServiceCollection AddMassTransitConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMassTransit(x =>
        {
            // WebApi sadece event publish ediyor, consumer yok
            // Mail notification event'lerini publish edecek

            x.UsingRabbitMq((context, cfg) =>
            {
                // PanelApi ile aynı RabbitMQ konfigürasyonu
                var host = configuration.GetValue<string>("RabbitMQ:Host") ?? "localhost";
                var username = configuration.GetValue<string>("RabbitMQ:Username") ?? "guest";
                var password = configuration.GetValue<string>("RabbitMQ:Password") ?? "guest";

                cfg.Host(host, h =>
                {
                    h.Username(username);
                    h.Password(password);
                });

                // Mail notification event'leri için exchange konfigürasyonları
                cfg.Message<OrderConfirmationNotificationRequested>(e => e.SetEntityName("order-confirmation-notification"));
                cfg.Publish<OrderConfirmationNotificationRequested>(e => e.ExchangeType = "fanout");

                cfg.Message<WelcomeEmailNotificationRequested>(e => e.SetEntityName("welcome-email-notification"));
                cfg.Publish<WelcomeEmailNotificationRequested>(e => e.ExchangeType = "fanout");

                cfg.Message<PasswordResetNotificationRequested>(e => e.SetEntityName("password-reset-notification"));
                cfg.Publish<PasswordResetNotificationRequested>(e => e.ExchangeType = "fanout");
            });
        });

        return services;
    }
}
