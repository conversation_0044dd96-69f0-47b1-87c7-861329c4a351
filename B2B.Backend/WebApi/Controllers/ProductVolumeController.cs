using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProductVolumeController : ControllerBase
{
    private readonly IProductVolumeService _productVolumeService;
    private readonly ILogger<ProductVolumeController> _logger;

    public ProductVolumeController(
        IProductVolumeService productVolumeService,
        ILogger<ProductVolumeController> logger)
    {
        _productVolumeService = productVolumeService;
        _logger = logger;
    }

    /// <summary>
    /// Ürünün tüm hacim seçeneklerini getirir - B2C müşteri için
    /// </summary>
    [HttpGet("product/{productId}")]
    public async Task<ActionResult<ApiResponse<List<CustomerVolumeOptionDto>>>> GetProductVolumes(Guid productId)
    {
        try
        {
            var result = await _productVolumeService.GetCustomerVolumeOptionsAsync(productId);
            return Ok(ApiResponse<List<CustomerVolumeOptionDto>>.SuccessResponse(result, "Ürün hacim seçenekleri başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product volumes for product {ProductId}", productId);
            return StatusCode(500, ApiResponse<List<CustomerVolumeOptionDto>>.ErrorResponse("Ürün hacim seçenekleri getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Belirli bir hacim seçeneğinin detaylarını getirir
    /// </summary>
    [HttpGet("{volumeId}")]
    public async Task<ActionResult<ApiResponse<ProductVolumeDto>>> GetVolumeById(Guid volumeId)
    {
        try
        {
            var result = await _productVolumeService.GetByIdAsync(volumeId);
            if (result == null)
            {
                return NotFound(ApiResponse<ProductVolumeDto>.NotFoundResponse("Hacim seçeneği bulunamadı."));
            }
            return Ok(ApiResponse<ProductVolumeDto>.SuccessResponse(result, "Hacim seçeneği başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting volume {VolumeId}", volumeId);
            return StatusCode(500, ApiResponse<ProductVolumeDto>.ErrorResponse("Hacim seçeneği getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürün ve hacim değerine göre hacim seçeneğini getirir
    /// </summary>
    [HttpGet("product/{productId}/volume/{volume}")]
    public async Task<ActionResult<ApiResponse<ProductVolumeDto>>> GetVolumeByProductAndVolume(Guid productId, decimal volume)
    {
        try
        {
            var result = await _productVolumeService.GetByProductAndVolumeAsync(productId, volume);
            if (result == null)
            {
                return NotFound(ApiResponse<ProductVolumeDto>.NotFoundResponse("Hacim seçeneği bulunamadı."));
            }
            return Ok(ApiResponse<ProductVolumeDto>.SuccessResponse(result, "Hacim seçeneği başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting volume for product {ProductId} and volume {Volume}", productId, volume);
            return StatusCode(500, ApiResponse<ProductVolumeDto>.ErrorResponse("Hacim seçeneği getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürünün varsayılan hacim seçeneğini getirir
    /// </summary>
    [HttpGet("product/{productId}/default")]
    public async Task<ActionResult<ApiResponse<ProductVolumeDto>>> GetDefaultVolume(Guid productId)
    {
        try
        {
            var result = await _productVolumeService.GetDefaultVolumeAsync(productId);
            if (result == null)
            {
                return NotFound(ApiResponse<ProductVolumeDto>.NotFoundResponse("Varsayılan hacim seçeneği bulunamadı."));
            }
            return Ok(ApiResponse<ProductVolumeDto>.SuccessResponse(result, "Varsayılan hacim seçeneği başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting default volume for product {ProductId}", productId);
            return StatusCode(500, ApiResponse<ProductVolumeDto>.ErrorResponse("Varsayılan hacim seçeneği getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Hacim seçeneğinin stok durumunu kontrol eder
    /// </summary>
    [HttpGet("{volumeId}/stock")]
    public async Task<ActionResult<ApiResponse<bool>>> CheckVolumeStock(Guid volumeId)
    {
        try
        {
            var result = await _productVolumeService.IsVolumeInStockAsync(volumeId);
            return Ok(ApiResponse<bool>.SuccessResponse(result, "Hacim stok durumu başarıyla kontrol edildi"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking stock for volume {VolumeId}", volumeId);
            return StatusCode(500, ApiResponse<bool>.ErrorResponse("Hacim stok durumu kontrol edilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Hacim seçeneğinin fiyatını getirir (GetVolumePrice)
    /// </summary>
    [HttpGet("{volumeId}/price")]
    public async Task<ActionResult<ApiResponse<decimal>>> GetVolumePrice(Guid volumeId)
    {
        try
        {
            var volume = await _productVolumeService.GetByIdAsync(volumeId);
            if (volume == null)
            {
                return NotFound(ApiResponse<decimal>.NotFoundResponse("Hacim seçeneği bulunamadı."));
            }
            return Ok(ApiResponse<decimal>.SuccessResponse(volume.Price, "Hacim fiyatı başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting price for volume {VolumeId}", volumeId);
            return StatusCode(500, ApiResponse<decimal>.ErrorResponse("Hacim fiyatı getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }
}
