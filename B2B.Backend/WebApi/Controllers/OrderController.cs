using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/orders")]
[EnableCors("AllowCustomerFrontend")]
[Authorize]
public class OrderController : ControllerBase
{
    private readonly IOrderService _orderService;
    private readonly ICurrentUserService _currentUser;

    public OrderController(IOrderService orderService, ICurrentUserService currentUser)
    {
        _orderService = orderService;
        _currentUser = currentUser;
    }

    [HttpGet("{id:guid}")]
    public async Task<ActionResult<ApiResponse<OrderDto>>> GetById(Guid id)
    {
        try
        {
            var userId = _currentUser.UserId;
            if (userId == null)
                return Unauthorized(ApiResponse<OrderDto>.ErrorResponse("Oturum bulunamadı.", 401));

            var order = await _orderService.GetByIdAsync(id);
            if (order == null)
                return NotFound(ApiResponse<OrderDto>.NotFoundResponse("Sipariş bulunamadı."));

            if (order.CustomerId != userId.Value)
                return Forbid();

            return Ok(ApiResponse<OrderDto>.SuccessResponse(order, "Sipariş getirildi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<OrderDto>.ErrorResponse("Sipariş getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }
}

