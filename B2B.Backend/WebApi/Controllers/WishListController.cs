using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;
[ApiController]
[Route("/api/wish-list")]
public class WishListController : ControllerBase
{
    private readonly ICustomerWishListService _customerWishListService;

    public WishListController(ICustomerWishListService customerWishListService)
    {
        _customerWishListService = customerWishListService;
    }

    public async Task<ActionResult<ApiResponse<WishListDto>>> GetWishListItems(Guid customerId)
    {
        var result = await _customerWishListService.GetWishListItems(customerId);

        return Ok(ApiResponse<WishListDto>.SuccessResponse(result, "Kullanıcı Favori Listesi Başarıyla Alındı"));

    }
}

