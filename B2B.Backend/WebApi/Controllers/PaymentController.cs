using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;
using WebApi.Models;
using Payments.Abstractions;
using System.Text.Json;
using Microsoft.AspNetCore.WebUtilities;
using Core.Enums;
using Core.Events;
using MassTransit;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PaymentController : ControllerBase
{
    private readonly IPaymentService _paymentService;
    private readonly IOrderService _orderService;
    private readonly IPaymentServiceFactory _paymentFactory;
    private readonly IPaymentProviderService _paymentProviderService;
    private readonly ICartService _cartService;
    private readonly IAddressService _addressService;
    private readonly WebApi.Services.IPaymentSessionStore _paymentSessionStore;
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly ICustomerService _customerService;
    private readonly IPricingCalculationService _pricingCalculationService;

    public PaymentController(
        IPaymentService paymentService,
        IOrderService orderService,
        IPaymentServiceFactory paymentFactory,
        IPaymentProviderService paymentProviderService,
        ICartService cartService,
        IAddressService addressService,
        WebApi.Services.IPaymentSessionStore paymentSessionStore,
        IPublishEndpoint publishEndpoint,
        ICustomerService customerService,
        IPricingCalculationService pricingCalculationService)
    {
        _paymentService = paymentService;
        _orderService = orderService;
        _paymentFactory = paymentFactory;
        _paymentProviderService = paymentProviderService;
        _cartService = cartService;
        _addressService = addressService;
        _paymentSessionStore = paymentSessionStore;
        _publishEndpoint = publishEndpoint;
        _customerService = customerService;
        _pricingCalculationService = pricingCalculationService;
    }

    [HttpPost("iyzico/initialize")]
    public async Task<ActionResult<ApiResponse<CheckoutInitResult>>> InitializeIyzicoPayment([FromBody] PaymentRequest request)
    {
        try
        {
            var ip = HttpContext.Connection.RemoteIpAddress?.MapToIPv4().ToString() == "::1" ? "127.0.0.1" : HttpContext.Connection.RemoteIpAddress?.MapToIPv4().ToString() ?? "127.0.0.1";

            // Map WebApi model -> abstract DTO
            var data = new CheckoutInitData
            {
                BasketId = request.BasketId ?? Guid.NewGuid().ToString(),
                TotalPrice = request.TotalPrice,
                Buyer = new Person
                {
                    Id = (request.CustomerId ?? Guid.NewGuid()).ToString(),
                    Name = request.ShippingInfo.FirstName,
                    Surname = request.ShippingInfo.LastName,
                    Email = request.ShippingInfo.Email,
                    Phone = request.ShippingInfo.Phone,
                    IdentityNumber = request.ShippingInfo.IdentityNumber
                },
                Ship = new Address
                {
                    ContactName = $"{request.ShippingInfo.FirstName} {request.ShippingInfo.LastName}".Trim(),
                    AddressText = request.ShippingInfo.Address,
                    City = request.ShippingInfo.City,
                    Country = "Turkey",
                    PostalCode = request.ShippingAddress?.PostalCode ?? request.ShippingInfo.ZipCode
                },
                Bill = request.BillingAddress != null ? new Address
                {
                    ContactName = request.BillingAddress.Name,
                    AddressText = $"{request.BillingAddress.Line1} {request.BillingAddress.Line2}".Trim(),
                    City = request.BillingAddress.City,
                    Country = request.BillingAddress.Country?.Length > 0 ? request.BillingAddress.Country : "Turkey",
                    PostalCode = request.BillingAddress.PostalCode
                } : new Address
                {
                    ContactName = $"{request.ShippingInfo.FirstName} {request.ShippingInfo.LastName}".Trim(),
                    AddressText = request.ShippingInfo.Address,
                    City = request.ShippingInfo.City,
                    Country = "Turkey",
                    PostalCode = request.ShippingAddress?.PostalCode ?? request.ShippingInfo.ZipCode
                },
                Items = request.CartProducts.Select(p => new Item
                {
                    Id = p.Id.ToString(),
                    Name = p.Name,
                    Category1 = "general",
                    ItemType = "PHYSICAL",
                    Price = p.Price * p.Quantity
                }).ToList(),
                CallbackUrl = Environment.GetEnvironmentVariable("NEXT_PUBLIC_API_GATEWAY_URL") +"/web-api/Payment/CallbackOkFail",
                CallbackOkUrl = Environment.GetEnvironmentVariable("NEXT_PUBLIC_API_GATEWAY_URL") + "/web-api/Payment/CallbackOk",
                CallbackFailUrl =  Environment.GetEnvironmentVariable("NEXT_PUBLIC_API_GATEWAY_URL") + "/web-api/Payment/CallbackFail"
            };

            // Provider ayarlarını DB'den çek
            var providerDto = await _paymentProviderService.GetByShortCodeAsync("IYZICO");
            var settings = providerDto != null ? await _paymentProviderService.GetSettingsAsync(providerDto.Id) : null;

            var provider = _paymentFactory.GetProvider("IYZICO", settings);
            if (provider == null)
                return BadRequest(ApiResponse<CheckoutInitResult>.ErrorResponse("Ödeme sağlayıcısı bulunamadı", 400));

            var result = await provider.InitializeCheckoutAsync(data, ip);

            // Map token -> (customerId, cartId) for callback correlation
            try
            {
                var customerId = request.CustomerId ?? Guid.Empty;
                Guid cartId = Guid.Empty;
                if (customerId != Guid.Empty)
                {
                    var customerCart = await _cartService.GetCustomerCartAsync(customerId);
                    if (customerCart != null)
                        cartId = customerCart.Id;
                }
                if (!string.IsNullOrWhiteSpace(result.Token))
                {
                    var addrSnap = default(WebApi.Services.AddressSnapshot);
                    if (request.ShippingAddress != null)
                    {
                        addrSnap = new WebApi.Services.AddressSnapshot
                        {
                            AddressId = request.ShippingAddress.Id == Guid.Empty ? null : request.ShippingAddress.Id,
                            Name = request.ShippingAddress.Name,
                            Line1 = request.ShippingAddress.Line1,
                            Line2 = request.ShippingAddress.Line2,
                            City = request.ShippingAddress.City,
                            District = request.ShippingAddress.District,
                            Country = string.IsNullOrWhiteSpace(request.ShippingAddress.Country) ? "Türkiye" : request.ShippingAddress.Country,
                            PostalCode = request.ShippingAddress.PostalCode,
                        };
                    }
                    else if (request.ShippingInfo != null)
                    {
                        addrSnap = new WebApi.Services.AddressSnapshot
                        {
                            AddressId = null,
                            Name = (request.ShippingInfo.FirstName + " " + request.ShippingInfo.LastName).Trim(),
                            Line1 = request.ShippingInfo.Address,
                            Line2 = string.Empty,
                            City = request.ShippingInfo.City,
                            District = string.Empty,
                            Country = "Türkiye",
                            PostalCode = request.ShippingAddress?.PostalCode,
                        };
                    }

                    _paymentSessionStore.Save(result.Token, customerId, cartId, addrSnap);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[PAYMENT] Warning: could not store payment session mapping: {ex.Message}");
            }

            return Ok(ApiResponse<CheckoutInitResult>.SuccessResponse(result, "Ödeme başlatma isteği hazırlandı"));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[PAYMENT] Error initializing payment: {ex.ToString()}");
            return BadRequest(ApiResponse<CheckoutInitResult>.ErrorResponse("Ödeme başlatma sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    // Tek callback alan entegrasyonlar (Iyzico)
    [HttpPost("CallbackOkFail")]
    public async Task<IActionResult> CallbackOkFail([FromForm] string token)
    {
        var providerDto = await _paymentProviderService.GetByShortCodeAsync("IYZICO");
            var settings = providerDto != null ? await _paymentProviderService.GetSettingsAsync(providerDto.Id) : null;

            var provider = _paymentFactory.GetProvider("IYZICO", settings);
        if (provider == null)
            return RedirectToAction(nameof(CallbackFail));

        var result = await provider.ValidateCallbackAsync(token);
        Console.WriteLine($"[PAYMENT] Callback result: {JsonSerializer.Serialize(result)}");

        var frontendBase = Environment.GetEnvironmentVariable("B2C_FRONTEND_BASE_URL") ?? "http://localhost:3001";
        var thankYouPath = "/odeme-durumu"; // themes/vineta/app/(sepet)/odeme-durumu/page

        if (result.IsSuccess && _paymentSessionStore.TryGet(token, out var ps))
        {
            try
            {
                // Build Order from cart
                var cart = await _cartService.GetByCustomerIdAsync(ps.CustomerId);
                if (cart != null)
                {
                    // Resolve shipping address -> AddressId (prefer request snapshot; create if needed)
                    Guid? addressId = null;
                    if (ps.ShippingAddress != null)
                    {
                        if (ps.ShippingAddress.AddressId.HasValue)
                        {
                            addressId = ps.ShippingAddress.AddressId.Value;
                        }
                        else
                        {
                            try
                            {
                                var addrCreate = new AddressCreateDto
                                {
                                    AddressType = Core.Enums.AddressType.Shipping,
                                    Name = ps.ShippingAddress.Name ?? "Teslimat Adresi",
                                    Line1 = ps.ShippingAddress.Line1 ?? string.Empty,
                                    Line2 = ps.ShippingAddress.Line2,
                                    City = ps.ShippingAddress.City ?? string.Empty,
                                    District = ps.ShippingAddress.District ?? string.Empty,
                                    Country = ps.ShippingAddress.Country ?? "Türkiye",
                                    PostalCode = ps.ShippingAddress.PostalCode,
                                    IsDefault = false,
                                    CustomerId = cart.CustomerId
                                };
                                addressId = await _addressService.CreateAsync(addrCreate);
                            }
                            catch { /* swallow - address optional */ }
                        }
                    }

                    // Merkezi fiyatlandırma servisi ile hesaplamalar yap
                    var pricingResults = new List<Application.Contracts.DTOs.Pricing.PricingCalculationResultDto>();

                    foreach (var item in cart.Items)
                    {
                        var pricingResult = _pricingCalculationService.CalculateItemPricing(
                            item.UnitPrice, // Orijinal fiyat
                            item.DiscountedPrice, // İndirimli fiyat (varsa)
                            (int)item.Quantity
                        );
                        pricingResults.Add(pricingResult);
                    }

                    // Sipariş özeti hesapla (kargo ve kampanya indirimi olmadan)
                    var orderSummary = _pricingCalculationService.CalculateOrderPricing(pricingResults, 0, 0);

                    var orderCreate = new OrderCreateDto
                    {
                        CustomerId = cart.CustomerId,
                        AddressId = addressId,
                        GrossTotalAmount = orderSummary.TotalOriginalAmount, // Brüt tutar (indirim öncesi)
                        TotalAmount = orderSummary.TotalAmount, // Net tutar (indirim sonrası)
                        DiscountAmount = orderSummary.TotalDiscountAmount, // İndirim tutarı
                        ShippingAmount = 0,
                        TaxAmount = orderSummary.TotalTaxAmount, // Vergi tutarı
                        Notes = "Iyzico ödeme sonrası oluşturuldu",
                        OrderRows = cart.Items.Select(i => new OrderRowCreateDto
                        {
                            ProductId = i.ProductId,
                            Quantity = (int)i.Quantity,
                            Price = i.UnitPrice, // Orijinal fiyat
                            DiscountedPrice = i.DiscountedPrice ?? i.UnitPrice // İndirimli fiyat (varsa)
                        }).ToList()
                    };


                    var orderId = await _orderService.CreateAsync(orderCreate);
                    var paymentCreate = new PaymentCreateDto
                    {
                        OrderId = orderId,
                        Amount = orderSummary.FinalAmount, // İndirimli final tutar
                        Description = "Iyzico ödeme sonrası oluşturuldu",
                        PaymentMethod = "Iyzico",
                        MaskedCardNumber = "**** **** **** "+ result.LastFourDigits  ?? string.Empty,
                        Status = PaymentStatus.Completed,
                        PaymentResponse = JsonSerializer.Serialize(result),
                        PaymentResponseCode = "00",
                        PaymentResponseMessage = "İşlem başarılı",
                        PaymentResponseTransactionId = result.PaymentId ?? string.Empty
                    };
                    var paymentId = await _paymentService.CreateAsync(paymentCreate);

                    // Sipariş onay maili gönder
                    await SendOrderConfirmationEmail(orderId, orderCreate, cart, ps.ShippingAddress);

                    // Clear cart
                    await _cartService.ClearCustomerCartAsync(cart.CustomerId);

                    // Redirect with orderId (GUID)
                    var okUrl = QueryHelpers.AddQueryString($"{frontendBase}{thankYouPath}", new Dictionary<string, string?>
                    {
                        ["success"] = "true",
                        ["orderId"] = orderId.ToString()
                    });

                    _paymentSessionStore.Remove(token);
                    return Redirect(okUrl);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[PAYMENT] Error while creating order from cart: {ex.Message}");
            }
        }

        // Fallback fail redirect
        var failUrl = QueryHelpers.AddQueryString($"{frontendBase}{thankYouPath}", new Dictionary<string, string?>
        {
            ["success"] = "false",
            ["message"] = result.Message
        });
        return Redirect(failUrl);
    }

    // Başarılı dönüş sayfası/endpoint
    [HttpGet("CallbackOk")]
    public IActionResult CallbackOk()
    {
        return Ok(new { status = "OK" });
    }

    // Başarısız dönüş sayfası/endpoint
    [HttpGet("CallbackFail")]
    public IActionResult CallbackFail()
    {
        return BadRequest(new { status = "FAIL" });
    }

    /// <summary>
    /// Sipariş onay maili gönderir
    /// </summary>
    private async Task SendOrderConfirmationEmail(Guid orderId, OrderCreateDto orderCreate, CartDto cart, WebApi.Services.AddressSnapshot? shippingAddress)
    {
        try
        {
            // Müşteri bilgilerini al
            var customer = await _customerService.GetByIdAsync(cart.CustomerId);
            if (customer == null)
            {
                Console.WriteLine($"[PAYMENT] Customer not found for order: {orderId}");
                return;
            }

            var customerName = customer.NameSurname ?? "Değerli Müşterimiz";
            var customerEmail = customer.Email ?? string.Empty;
            var customerPhone = customer.PhoneNumber ?? string.Empty;

            if (string.IsNullOrEmpty(customerEmail))
            {
                Console.WriteLine($"[PAYMENT] Customer email not found for order: {orderId}");
                return;
            }

            // Adres bilgilerini hazırla
            var deliveryAddress = "";
            var deliveryCity = "";
            var deliveryCounty = "";
            var deliveryCountry = "Türkiye";

            if (shippingAddress != null)
            {
                deliveryAddress = $"{shippingAddress.Line1} {shippingAddress.Line2}".Trim();
                deliveryCity = shippingAddress.City ?? "";
                deliveryCounty = shippingAddress.District ?? "";
                deliveryCountry = shippingAddress.Country ?? "Türkiye";
            }

            // Merkezi fiyatlandırma servisi ile hesaplamalar yap
            var pricingResults = new List<Application.Contracts.DTOs.Pricing.PricingCalculationResultDto>();

            foreach (var item in cart.Items)
            {
                // Cart'ta DiscountedPrice bilgisi var, onu kullan
                var pricingResult = _pricingCalculationService.CalculateItemPricing(
                    item.UnitPrice, // Orijinal fiyat
                    item.DiscountedPrice, // İndirimli fiyat (varsa)
                    (int)item.Quantity
                );
                pricingResults.Add(pricingResult);
            }

            // Sipariş özeti hesapla (kargo ve kampanya indirimi olmadan)
            var orderSummary = _pricingCalculationService.CalculateOrderPricing(pricingResults, 0, 0);

            // Sipariş ürünlerini cart'tan hazırla
            var orderItems = cart.Items.Select(item => new OrderItemForMail
            {
                ProductName = item.ProductName,
                ProductImageUrl = item.ProductImage ?? "/images/no-image.jpg",
                Quantity = (int)item.Quantity,
                UnitPrice = item.UnitPrice, // Orijinal fiyat
                DiscountedPrice = item.DiscountedPrice ?? item.UnitPrice, // İndirimli fiyat varsa onu, yoksa orijinal fiyatı kullan
                VariantInfo = "" // Cart'ta varyant bilgisi yok
            }).ToList();

            // Sipariş numarası oluştur (basit format)
            var orderNumber = $"ORD-{DateTime.Now:yyyyMMdd}-{orderId.ToString()[..8].ToUpper()}";

            // Event'i gönder
            await _publishEndpoint.Publish(new OrderConfirmationNotificationRequested
            {
                OrderId = orderId,
                CustomerEmail = customerEmail,
                CustomerName = customerName,
                CustomerPhone = customerPhone,
                OrderNumber = orderNumber,
                OrderDate = DateTime.UtcNow,
                OrderTotalAmount = orderSummary.FinalAmount,
                OrderSubtotal = orderSummary.TotalNetAmount, // Net tutar (vergi hariç)
                DiscountAmount = orderSummary.TotalDiscountAmount,
                ShippingAmount = orderSummary.ShippingAmount,
                TaxAmount = orderSummary.TotalTaxAmount,
                DeliveryAddress = deliveryAddress,
                DeliveryCity = deliveryCity,
                DeliveryCounty = deliveryCounty,
                DeliveryCountry = deliveryCountry,
                BillingAddress = deliveryAddress, // Aynı adres kullanılıyor
                BillingCity = deliveryCity,
                BillingCounty = deliveryCounty,
                BillingCountry = deliveryCountry,
                OrderDetailUrl = $"/orders/{orderId}",
                OrderItems = orderItems
            });

            Console.WriteLine($"[PAYMENT] Order confirmation email event sent for order: {orderId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[PAYMENT] Error sending order confirmation email for order {orderId}: {ex.Message}");
        }
    }


}