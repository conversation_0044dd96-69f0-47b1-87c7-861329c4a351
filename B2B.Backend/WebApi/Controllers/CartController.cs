using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/cart")]
[EnableCors("AllowCustomerFrontend")]
[Authorize]
public class CartController : ControllerBase
{
    private readonly ICartService _cartService;
    private readonly ICurrentUserService _currentUser;

    public CartController(ICartService cartService, ICurrentUserService currentUser)
    {
        _cartService = cartService;
        _currentUser = currentUser;
    }

    private Guid RequireCustomerId()
    {
        var userId = _currentUser.UserId;
        if (userId == null || userId == Guid.Empty)
            throw new UnauthorizedAccessException("Oturum bulunamadı.");
        return userId.Value;
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<CartDto>>> GetMyCart()
    {
        try
        {
            var customerId = RequireCustomerId();
            var cart = await _cartService.GetCustomerCartAsync(customerId);
            if (cart == null)
                return Ok(ApiResponse<CartDto>.SuccessResponse(new CartDto
                {
                    Id = Guid.Empty,
                    CustomerId = customerId,
                    CustomerName = string.Empty,
                    Items = new List<CartItemDto>(),
                    TotalItems = 0,
                    TotalAmount = 0,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }, "Sepet boş."));

            return Ok(ApiResponse<CartDto>.SuccessResponse(cart, "Sepet getirildi."));
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(ApiResponse<CartDto>.ErrorResponse(ex.Message, 401));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<CartDto>.ErrorResponse("Sepet getirilirken hata oluştu.", 500, ex.Message));
        }
    }

    [HttpPost("items")]
    public async Task<ActionResult<ApiResponse<object>>> AddItem([FromBody] CartItemCreateDto dto)
    {
        try
        {
            var customerId = RequireCustomerId();
            // Ensure dto.CartId equals customer's cart
            var cart = await _cartService.GetCustomerCartAsync(customerId);
            if (cart == null)
                return NotFound(ApiResponse<object>.NotFoundResponse("Müşteri sepeti bulunamadı."));

            if (dto.CartId == Guid.Empty) dto.CartId = cart.Id;
            if (dto.CartId != cart.Id)
                return BadRequest(ApiResponse<object>.BadRequestResponse("Geçersiz sepet bilgisi."));

            var cartItemId = await _cartService.AddItemAsync(dto);
            return Ok(ApiResponse<object>.SuccessResponse(new { cartItemId }, "Ürün sepete eklendi."));
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(ApiResponse<object>.ErrorResponse(ex.Message, 401));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResponse("Ürün sepete eklenirken hata oluştu.", 500, ex.Message));
        }
    }

    [HttpPut("items")]
    public async Task<ActionResult<ApiResponse<object>>> UpdateItem([FromBody] CartItemUpdateDto dto)
    {
        try
        {
            RequireCustomerId();
            await _cartService.UpdateItemAsync(dto);
            return Ok(ApiResponse<object>.SuccessResponse(new { dto.Id }, "Sepet ürünü güncellendi."));
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(ApiResponse<object>.ErrorResponse(ex.Message, 401));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResponse("Sepet ürünü güncellenirken hata oluştu.", 500, ex.Message));
        }
    }

    [HttpDelete("items/{cartItemId}")]
    public async Task<ActionResult<ApiResponse<object>>> RemoveItem(Guid cartItemId)
    {
        try
        {
            RequireCustomerId();
            await _cartService.RemoveItemAsync(cartItemId);
            return Ok(ApiResponse<object>.SuccessResponse(new { cartItemId }, "Ürün sepetten kaldırıldı."));
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(ApiResponse<object>.ErrorResponse(ex.Message, 401));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResponse("Ürün sepetten kaldırılırken hata oluştu.", 500, ex.Message));
        }
    }

    [HttpDelete]
    public async Task<ActionResult<ApiResponse<object>>> Clear()
    {
        try
        {
            var customerId = RequireCustomerId();
            var result = await _cartService.ClearCustomerCartAsync(customerId);
            if (!result)
                return NotFound(ApiResponse<object>.NotFoundResponse("Sepet bulunamadı."));

            return Ok(ApiResponse<object>.SuccessResponse(new { cleared = true }, "Sepet temizlendi."));
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(ApiResponse<object>.ErrorResponse(ex.Message, 401));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResponse("Sepet temizlenirken hata oluştu.", 500, ex.Message));
        }
    }
}

