using Application.Contracts.DTOs.Pricing;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

/// <summary>
/// Fiyatlandırma hesaplama API'leri
/// </summary>
[ApiController]
[Route("web-api/[controller]")]
public class PricingController : ControllerBase
{
    private readonly IPricingCalculationService _pricingCalculationService;
    private readonly ILogger<PricingController> _logger;

    public PricingController(
        IPricingCalculationService pricingCalculationService,
        ILogger<PricingController> logger)
    {
        _pricingCalculationService = pricingCalculationService;
        _logger = logger;
    }

    /// <summary>
    /// Tek ürün için fiyat hesaplama
    /// </summary>
    [HttpPost("calculate-item")]
    public ActionResult<PricingCalculationResultDto> CalculateItemPricing([FromBody] ItemPricingRequest request)
    {
        try
        {
            var result = _pricingCalculationService.CalculateItemPricing(
                request.Price,
                request.DiscountedPrice,
                request.Quantity
            );

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating item pricing");
            return BadRequest(new { message = "Fiyat hesaplama hatası" });
        }
    }

    /// <summary>
    /// Sepet için toplam fiyat hesaplama
    /// </summary>
    [HttpPost("calculate-cart")]
    public ActionResult<PricingCalculationSummaryDto> CalculateCartPricing([FromBody] CartPricingRequest request)
    {
        try
        {
            var itemResults = request.Items.Select(item => 
                _pricingCalculationService.CalculateItemPricing(
                    item.Price,
                    item.DiscountedPrice,
                    item.Quantity
                )).ToList();

            var result = _pricingCalculationService.CalculateCartPricing(itemResults);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating cart pricing");
            return BadRequest(new { message = "Sepet fiyat hesaplama hatası" });
        }
    }

    /// <summary>
    /// Sipariş için toplam fiyat hesaplama (kargo ve kampanya dahil)
    /// </summary>
    [HttpPost("calculate-order")]
    public ActionResult<PricingCalculationSummaryDto> CalculateOrderPricing([FromBody] OrderPricingRequest request)
    {
        try
        {
            var itemResults = request.Items.Select(item => 
                _pricingCalculationService.CalculateItemPricing(
                    item.Price,
                    item.DiscountedPrice,
                    item.Quantity
                )).ToList();

            var result = _pricingCalculationService.CalculateOrderPricing(
                itemResults,
                request.ShippingAmount,
                request.CampaignDiscountAmount
            );

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating order pricing");
            return BadRequest(new { message = "Sipariş fiyat hesaplama hatası" });
        }
    }

    /// <summary>
    /// Vergi dahil tutardan vergi miktarını hesapla
    /// </summary>
    [HttpPost("calculate-tax")]
    public ActionResult<decimal> CalculateTaxFromGrossAmount([FromBody] TaxCalculationRequest request)
    {
        try
        {
            var result = _pricingCalculationService.CalculateTaxFromGrossAmount(request.GrossAmount);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating tax");
            return BadRequest(new { message = "Vergi hesaplama hatası" });
        }
    }
}

/// <summary>
/// Tek ürün fiyat hesaplama isteği
/// </summary>
public class ItemPricingRequest
{
    public decimal Price { get; set; }
    public decimal? DiscountedPrice { get; set; }
    public int Quantity { get; set; } = 1;
}

/// <summary>
/// Sepet fiyat hesaplama isteği
/// </summary>
public class CartPricingRequest
{
    public List<ItemPricingRequest> Items { get; set; } = new();
}

/// <summary>
/// Sipariş fiyat hesaplama isteği
/// </summary>
public class OrderPricingRequest
{
    public List<ItemPricingRequest> Items { get; set; } = new();
    public decimal ShippingAmount { get; set; }
    public decimal CampaignDiscountAmount { get; set; }
}

/// <summary>
/// Vergi hesaplama isteği
/// </summary>
public class TaxCalculationRequest
{
    public decimal GrossAmount { get; set; }
}
