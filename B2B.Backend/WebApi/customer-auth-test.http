### Customer Authentication API Test Endpoints

### 1. Customer Register
POST http://localhost:33804/api/customer/register
Content-Type: application/json

{
  "nameSurname": "Test Customer",
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "phoneNumber": "+905551234567",
  "taxOrIdentityNumber": "12345678901",
  "taxOffice": "Test Vergi Dairesi"
}

### 2. Customer Login
POST http://localhost:33804/api/customer/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!"
}

### 3. Validate Customer Token
GET http://localhost:33804/api/customer/validate
Authorization: Bearer {{customerToken}}

### 4. Get Customer Profile
GET http://localhost:33804/api/customer/profile
Authorization: Bearer {{customerToken}}

### 5. Customer Register - Duplicate Email Test
POST http://localhost:33804/api/customer/register
Content-Type: application/json

{
  "nameSurname": "Another Customer",
  "email": "<EMAIL>",
  "password": "AnotherPassword123!",
  "phoneNumber": "+905559876543"
}

### 6. Customer Login - Wrong Password
POST http://localhost:33804/api/customer/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "WrongPassword123!"
}

### 7. Customer Login - Non-existent Email
POST http://localhost:33804/api/customer/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!"
}

### 8. Validate Invalid Token
GET http://localhost:33804/api/customer/validate
Authorization: Bearer invalid.token.here

### Variables for testing
# After successful login, copy the token from response and use it in subsequent requests
# Example: @customerToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
