using Core.Events;
using Modules.Mail.Abstraction.Models;
using MailAPI.Services;
using MassTransit;

namespace MailAPI.Consumers
{
    /// <summary>
    /// SendMailRequested event'ini alıp MailMessage'a dönüştürerek mail gönderen consumer
    /// </summary>
    public class SendMailRequestedConsumer : IConsumer<SendMailRequested>
    {
        private readonly MailService _mailService;
        private readonly ILogger<SendMailRequestedConsumer> _logger;

        public SendMailRequestedConsumer(MailService mailService, ILogger<SendMailRequestedConsumer> logger)
        {
            _mailService = mailService;
            _logger = logger;
        }

        public async Task Consume(ConsumeContext<SendMailRequested> context)
        {
            try
            {
                var request = context.Message;
                _logger.LogInformation($"SendMailRequested event alındı: {request.ToEmail} - Template: {request.TemplateShortCode}");

                // SendMailRequested'i MailMessage'a dönüştür
                var mailMessage = new MailMessage
                {
                    Title = request.CustomSubject ?? $"Mail - {request.TemplateShortCode}",
                    MailContent = request.CustomContent ?? "Mail içeriği template'den render edilecek",
                    To = new List<string> { request.ToEmail },
                    CC = request.CcEmails ?? new List<string>(),
                    BCC = request.BccEmails ?? new List<string>(),
                    Provider = "SMTP" // Varsayılan SMTP kullan
                };

                var result = await _mailService.SendMailAsync(mailMessage);
                
                if (result)
                {
                    _logger.LogInformation($"Mail başarıyla gönderildi: {request.ToEmail} - Template: {request.TemplateShortCode}");
                }
                else
                {
                    _logger.LogWarning($"Mail gönderilemedi: {request.ToEmail} - Template: {request.TemplateShortCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SendMailRequested event işlenirken hata oluştu");
                throw;
            }
        }
    }
}
