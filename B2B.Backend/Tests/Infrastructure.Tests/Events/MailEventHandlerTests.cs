using Moq;
using Microsoft.Extensions.Logging;
using MassTransit;
using Core.Events;
using PanelApi.Events;
using Application.Contracts.Interfaces;
using Application.Contracts.DTOs;

namespace Infrastructure.Tests.Events;

public class MailEventHandlerTests
{
    private readonly Mock<ILogger<CustomerShipmentNotificationHandler>> _mockCustomerLogger;
    private readonly Mock<IPublishEndpoint> _mockPublishEndpoint;
    private readonly Mock<IOrderService> _mockOrderService;
    private readonly Mock<IMailTemplateService> _mockMailTemplateService;
    private readonly Mock<IMailLinkService> _mockMailLinkService;

    public MailEventHandlerTests()
    {
        _mockCustomerLogger = new Mock<ILogger<CustomerShipmentNotificationHandler>>();
        _mockPublishEndpoint = new Mock<IPublishEndpoint>();
        _mockOrderService = new Mock<IOrderService>();
        _mockMailTemplateService = new Mock<IMailTemplateService>();
        _mockMailLinkService = new Mock<IMailLinkService>();
    }

    [Fact]
    public async Task CustomerShipmentNotificationHandler_WithKaanSaliEmail_PublishesSendMailRequested()
    {
        // Arrange
        var orderId = Guid.CreateVersion7();
        var mockOrder = new OrderDto
        {
            Id = orderId,
            OrderNumber = "ORD-12345",
            CreatedAt = DateTime.UtcNow,
            TotalAmount = 150.00m,
            TaxAmount = 20.00m,
            ShippingAmount = 10.00m,
            DiscountAmount = 5.00m,
            Customer = new CustomerDto
            {
                Id = Guid.CreateVersion7(),
                NameSurname = "Kaan Sali",
                PhoneNumber = "+90 ************",
                Email = "<EMAIL>"
            },
            Address = new AddressDto
            {
                Id = Guid.CreateVersion7(),
                Line1 = "Test Address Line 1",
                Line2 = "Test Address Line 2",
                City = "Istanbul",
                District = "Kadikoy",
                Country = "Türkiye"
            },
            OrderRows = new List<OrderRowDto>
            {
                new()
                {
                    Id = Guid.CreateVersion7(),
                    ProductId = Guid.CreateVersion7(),
                    Quantity = 2,
                    Price = 60.00m,
                    DiscountedPrice = 55.00m,
                    Product = new ProductDto
                    {
                        Id = Guid.CreateVersion7(),
                        Name = "Test Product",
                        Images = new List<ProductImageDto>
                        {
                            new()
                            {
                                Id = Guid.CreateVersion7(),
                                ThumbnailMediumPath = "/images/test-product.jpg",
                                AltText = "Test Product"
                            }
                        },
                        AttributeMappings = new List<ProductAttributeMappingDto>
                        {
                            new()
                            {
                                Id = Guid.CreateVersion7(),
                                Attribute = new ProductAttributeDto
                                {
                                    Id = Guid.CreateVersion7(),
                                    Name = "Renk"
                                },
                                AttributeValue = new ProductAttributeValueDto
                                {
                                    Id = Guid.CreateVersion7(),
                                    Value = "Kırmızı"
                                }
                            }
                        }
                    }
                }
            }
        };

        _mockOrderService.Setup(s => s.GetByIdAsync(orderId))
            .ReturnsAsync(mockOrder);

        _mockMailTemplateService.Setup(s => s.RenderTemplateWithLoopsAsync(
                "customer-shipment-notification",
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<Dictionary<string, List<Dictionary<string, string>>>>()))
            .ReturnsAsync(("Kargo Bildirim Maili", "<html>Rendered content</html>"));

        var handler = new CustomerShipmentNotificationHandler(
            _mockPublishEndpoint.Object,
            _mockOrderService.Object,
            _mockMailTemplateService.Object,
            _mockMailLinkService.Object,
            _mockCustomerLogger.Object);

        var notification = new CustomerShipmentNotificationRequested
        {
            ShipmentId = Guid.CreateVersion7(),
            OrderId = orderId, // Aynı order ID'yi kullan
            CustomerEmail = "<EMAIL>", // Test email
            CustomerName = "Kaan Sali",
            OrderNumber = "ORD-12345",
            CarrierName = "Yurtiçi Kargo",
            TrackingNumber = "YK123456789",
            TrackingUrl = "https://kargo.yurtici.com.tr/tracking/YK123456789",
            DeliveryAddress = "Test Address, Test City",
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(3)
        };

        var mockContext = new Mock<ConsumeContext<CustomerShipmentNotificationRequested>>();
        mockContext.Setup(c => c.Message).Returns(notification);

        // Act
        await handler.Consume(mockContext.Object);

        // Assert
        _mockPublishEndpoint.Verify(p => p.Publish(
            It.Is<SendMailRequested>(smr =>
                smr.TemplateShortCode == "customer-shipment-notification" &&
                smr.ToEmail == "<EMAIL>" &&
                smr.ToName == "Kaan Sali" &&
                smr.Variables.ContainsKey("customerName") &&
                smr.Variables["customerName"] == "Kaan Sali" &&
                smr.Variables.ContainsKey("orderNumber") &&
                smr.Variables["orderNumber"] == "ORD-12345" &&
                smr.Variables.ContainsKey("carrierName") &&
                smr.Variables["carrierName"] == "Yurtiçi Kargo" &&
                smr.Variables.ContainsKey("trackingNumber") &&
                smr.Variables["trackingNumber"] == "YK123456789" &&
                smr.CustomSubject == "Kargo Bildirim Maili" &&
                smr.CustomContent == "<html>Rendered content</html>"
            ),
            It.IsAny<CancellationToken>()
        ), Times.Once);

        // Verify that order service was called
        _mockOrderService.Verify(s => s.GetByIdAsync(orderId), Times.Once);

        // Verify that mail template service was called
        _mockMailTemplateService.Verify(s => s.RenderTemplateWithLoopsAsync(
            "customer-shipment-notification",
            It.IsAny<Dictionary<string, string>>(),
            It.IsAny<Dictionary<string, List<Dictionary<string, string>>>>()
        ), Times.Once);
    }

}
