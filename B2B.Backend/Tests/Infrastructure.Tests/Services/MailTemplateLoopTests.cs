using Infrastructure.Services;

namespace Infrastructure.Tests.Services;

/// <summary>
/// Mail template döngü işlemleri test sınıfı
/// </summary>
public class MailTemplateLoopTests
{
    [Fact]
    public void ProcessLoops_WithOrderItems_ReturnsRenderedContent()
    {
        // Arrange
        var content = @"
            <h1>Merhaba {{customerName}}</h1>
            <p>{{orderNumber}} numaralı siparişiniz alınmıştır.</p>

            <h2>Sipariş Detayları:</h2>
            <% items.forEach(function(item) { %>
            <div class='order-item'>
                <img src='{{orderRowImage}}' alt='{{orderRowName}}' />
                <h3>{{orderRowName}} x {{orderRowAmount}}</h3>
                <p>Varyant: {{orderRowVariant}}</p>
                <p>Fiyat: {{orderRowPrice}} TL</p>
                <p><PERSON><PERSON><PERSON><PERSON>: {{orderRowDiscount}} TL</p>
            </div>
            <% }); %>

            <p>Toplam: {{orderItemsKdvTotal}} TL</p>
        ";

        var variables = new Dictionary<string, string>
        {
            { "customerName", "Ahmet Yılmaz" },
            { "orderNumber", "ORD-12345" },
            { "orderItemsKdvTotal", "150.00" }
        };

        var loopData = new Dictionary<string, List<Dictionary<string, string>>>
        {
            {
                "items", new List<Dictionary<string, string>>
                {
                    new()
                    {
                        { "orderRowName", "iPhone 15" },
                        { "orderRowImage", "/images/iphone15.jpg" },
                        { "orderRowAmount", "1" },
                        { "orderRowPrice", "120.00" },
                        { "orderRowDiscount", "100.00" },
                        { "orderRowVariant", "128GB Siyah" }
                    },
                    new()
                    {
                        { "orderRowName", "iPhone Kılıfı" },
                        { "orderRowImage", "/images/case.jpg" },
                        { "orderRowAmount", "2" },
                        { "orderRowPrice", "30.00" },
                        { "orderRowDiscount", "25.00" },
                        { "orderRowVariant", "Şeffaf" }
                    }
                }
            }
        };

        // Act - Reflection kullanarak private metodu test et
        var serviceType = typeof(MailTemplateService);
        var method = serviceType.GetMethod("ReplaceVariablesAndLoops",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        var result = (string)method!.Invoke(null, new object[] { content, variables, loopData });

        // Assert
        Assert.Contains("Merhaba Ahmet Yılmaz", result);
        Assert.Contains("ORD-12345 numaralı siparişiniz", result);
        Assert.Contains("iPhone 15", result);
        Assert.Contains("iPhone Kılıfı", result);
        Assert.Contains("128GB Siyah", result);
        Assert.Contains("Şeffaf", result);
        Assert.Contains("100.00 TL", result);
        Assert.Contains("25.00 TL", result);
        Assert.Contains("Toplam: 150.00 TL", result);

        // Döngü yapısının kaldırıldığını kontrol et
        Assert.DoesNotContain("<% items.forEach", result);
        Assert.DoesNotContain("<% });", result);
    }

    [Fact]
    public void ProcessLoops_WithEmptyLoop_ReturnsContentWithoutLoop()
    {
        // Arrange
        var content = @"
            <h1>Başlık</h1>
            <% items.forEach(function(item) { %>
            <div>{{orderRowName}}</div>
            <% }); %>
            <p>Son</p>
        ";

        var variables = new Dictionary<string, string>();
        var loopData = new Dictionary<string, List<Dictionary<string, string>>>
        {
            { "items", new List<Dictionary<string, string>>() } // Boş liste
        };

        // Act - Reflection kullanarak private metodu test et
        var serviceType = typeof(MailTemplateService);
        var method = serviceType.GetMethod("ReplaceVariablesAndLoops",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        var result = (string)method!.Invoke(null, new object[] { content, variables, loopData });

        // Assert
        Assert.Contains("<h1>Başlık</h1>", result);
        Assert.Contains("<p>Son</p>", result);
        Assert.DoesNotContain("{{orderRowName}}", result); // Döngü içeriği render edilmemeli
        Assert.DoesNotContain("<% items.forEach", result);
    }

    [Fact]
    public void ProcessLoops_WithMultipleLoops_ReturnsRenderedContent()
    {
        // Arrange
        var content = @"
            <h1>Ürünler:</h1>
            <% products.forEach(function(item) { %>
            <div>{{productName}}</div>
            <% }); %>

            <h1>Kategoriler:</h1>
            <% categories.forEach(function(item) { %>
            <div>{{categoryName}}</div>
            <% }); %>
        ";

        var variables = new Dictionary<string, string>();
        var loopData = new Dictionary<string, List<Dictionary<string, string>>>
        {
            {
                "products", new List<Dictionary<string, string>>
                {
                    new() { { "productName", "Ürün 1" } },
                    new() { { "productName", "Ürün 2" } }
                }
            },
            {
                "categories", new List<Dictionary<string, string>>
                {
                    new() { { "categoryName", "Kategori A" } },
                    new() { { "categoryName", "Kategori B" } }
                }
            }
        };

        // Act - Reflection kullanarak private metodu test et
        var serviceType = typeof(MailTemplateService);
        var method = serviceType.GetMethod("ReplaceVariablesAndLoops",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        var result = (string)method!.Invoke(null, new object[] { content, variables, loopData });

        // Assert
        Assert.Contains("Ürün 1", result);
        Assert.Contains("Ürün 2", result);
        Assert.Contains("Kategori A", result);
        Assert.Contains("Kategori B", result);
        Assert.DoesNotContain("<% products.forEach", result);
        Assert.DoesNotContain("<% categories.forEach", result);
    }

}
