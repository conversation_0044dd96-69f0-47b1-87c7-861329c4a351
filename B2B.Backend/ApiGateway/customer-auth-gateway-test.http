### Customer Authentication API Test via ApiGateway

### 1. Customer Register via ApiGateway
POST http://localhost:33800/customer/register
Content-Type: application/json

{
  "nameSurname": "Gateway Test Customer",
  "email": "<EMAIL>",
  "password": "GatewayPassword123!",
  "phoneNumber": "+905551234567",
  "taxOrIdentityNumber": "12345678901",
  "taxOffice": "Gateway Test Vergi Dairesi"
}

### 2. Customer Login via ApiGateway
POST http://localhost:33800/customer/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "GatewayPassword123!"
}

### 3. Validate Customer Token via ApiGateway
GET http://localhost:33800/customer/validate
Authorization: Bearer {{customerToken}}

### 4. Get Customer Profile via ApiGateway
GET http://localhost:33800/customer/profile
Authorization: Bearer {{customerToken}}

### 5. Test CORS Preflight Request
OPTIONS http://localhost:33800/customer/login
Origin: http://localhost:3001
Access-Control-Request-Method: POST
Access-Control-Request-Headers: Content-Type

### 6. Customer Login - Wrong Password via ApiGateway
POST http://localhost:33800/customer/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "WrongPassword123!"
}

### 7. Customer Register - Duplicate Email via ApiGateway
POST http://localhost:33800/customer/register
Content-Type: application/json

{
  "nameSurname": "Another Gateway Customer",
  "email": "<EMAIL>",
  "password": "AnotherPassword123!",
  "phoneNumber": "+905559876543"
}

### 8. Validate Invalid Token via ApiGateway
GET http://localhost:33800/customer/validate
Authorization: Bearer invalid.token.here

### Variables for testing
# After successful login, copy the token from response and use it in subsequent requests
# Example: @customerToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### Test URLs:
# Direct WebApi: http://localhost:33804/api/customer/*
# Via ApiGateway: http://localhost:33800/customer/*
# Frontend (vineta): http://localhost:3001
