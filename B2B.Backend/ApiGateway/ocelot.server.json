{"Routes": [{"DownstreamPathTemplate": "/api/ProductImage/{imageId}/paths", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "panel-api", "Port": 5000}], "UpstreamPathTemplate": "/internal-api/ProductImage/{imageId}/paths", "UpstreamHttpMethod": ["PATCH"], "Priority": 0}, {"DownstreamPathTemplate": "/api/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "panel-api", "Port": 5000}], "UpstreamPathTemplate": "/admin-api/{catchAll}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "AuthenticationOptions": {"AuthenticationProviderKey": "PanelJw<PERSON><PERSON><PERSON><PERSON>", "AllowedScopes": []}, "Priority": 1}, {"DownstreamPathTemplate": "/api/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "web-api", "Port": 5000}], "UpstreamPathTemplate": "/web-api/{catchAll}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "Priority": 1, "EnableCors": true}, {"DownstreamPathTemplate": "/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "mail-api", "Port": 5000}], "UpstreamPathTemplate": "/mail-api/{catchAll}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"], "AuthenticationOptions": {"AuthenticationProviderKey": "PanelJw<PERSON><PERSON><PERSON><PERSON>", "AllowedScopes": []}}, {"DownstreamPathTemplate": "/api/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "media-api", "Port": 5000}], "UpstreamPathTemplate": "/media-api/{catchAll}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "AuthenticationOptions": {"AuthenticationProviderKey": "PanelJw<PERSON><PERSON><PERSON><PERSON>", "AllowedScopes": []}}, {"DownstreamPathTemplate": "/api/auth/login", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "panel-api", "Port": 5000}], "UpstreamPathTemplate": "/admin/login", "UpstreamHttpMethod": ["POST"], "Priority": 0}, {"DownstreamPathTemplate": "/api/auth/register", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "panel-api", "Port": 5000}], "UpstreamPathTemplate": "/admin/register", "UpstreamHttpMethod": ["POST"], "Priority": 0}, {"DownstreamPathTemplate": "/api/customer/register", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "web-api", "Port": 5000}], "UpstreamPathTemplate": "/customer/register", "UpstreamHttpMethod": ["POST"], "Priority": 0, "EnableCors": true}, {"DownstreamPathTemplate": "/api/customer/login", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "web-api", "Port": 5000}], "UpstreamPathTemplate": "/customer/login", "UpstreamHttpMethod": ["POST"], "Priority": 0, "EnableCors": true}, {"DownstreamPathTemplate": "/api/customer/validate", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "web-api", "Port": 5000}], "UpstreamPathTemplate": "/customer/validate", "UpstreamHttpMethod": ["GET"], "Priority": 0, "EnableCors": true}, {"DownstreamPathTemplate": "/api/customer/profile", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "web-api", "Port": 5000}], "UpstreamPathTemplate": "/customer/profile", "UpstreamHttpMethod": ["GET"], "Priority": 0, "EnableCors": true}, {"DownstreamPathTemplate": "/images/original/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "panel-api", "Port": 5000}], "UpstreamPathTemplate": "/images/original/{catchAll}", "UpstreamHttpMethod": ["GET"], "Priority": 1}, {"DownstreamPathTemplate": "/images/webp/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "media-api", "Port": 5000}], "UpstreamPathTemplate": "/images/webp/{catchAll}", "UpstreamHttpMethod": ["GET"], "Priority": 1}, {"DownstreamPathTemplate": "/images/thumbnails/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "media-api", "Port": 5000}], "UpstreamPathTemplate": "/images/thumbnails/{catchAll}", "UpstreamHttpMethod": ["GET"], "Priority": 1}], "GlobalConfiguration": {"BaseUrl": "https://futureapi.ukscreative.com"}}