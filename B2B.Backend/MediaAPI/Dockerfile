# Build aşaması
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["MediaAPI/MediaAPI.csproj", "MediaAPI/"]
COPY ["Core/Core.csproj", "Core/"]
COPY ["Infrastructure/Infrastructure.csproj", "Infrastructure/"]
COPY ["Application.Contracts/Application.Contracts.csproj", "Application.Contracts/"]

RUN dotnet restore "MediaAPI/MediaAPI.csproj"

# Copy everything else and build
COPY . .
WORKDIR "/src/MediaAPI"
RUN dotnet build "MediaAPI.csproj" -c Release -o /app/build

# Publish aşaması
FROM build AS publish
RUN dotnet publish "MediaAPI.csproj" -c Release -o /app/publish

# Runtime aşaması
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app

# Create wwwroot directory for media files
RUN mkdir -p /app/wwwroot/images

COPY --from=publish /app/publish .

EXPOSE 5000
ENTRYPOINT ["dotnet", "MediaAPI.dll"]
