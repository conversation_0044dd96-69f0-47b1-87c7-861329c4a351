using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;

[Table("ProductVolumes")]
public partial class ProductVolume : BaseEntity
{
    /// <summary>
    /// Ürün ID'si
    /// </summary>
    [Required]
    public Guid ProductId { get; set; }

    /// <summary>
    /// Hacim değeri (50, 100, 250 vb.)
    /// </summary>
    [Required]
    public decimal Volume { get; set; }

    /// <summary>
    /// Hacim etiketi (50ml, 100ml, 250ml vb.)
    /// </summary>
    [Required, MaxLength(50)]
    public string Label { get; set; } = null!;

    /// <summary>
    /// Hacim birimi (ml, gr, kg, adet vb.)
    /// </summary>
    [Required, MaxLength(10)]
    public string Unit { get; set; } = null!;

    /// <summary>
    /// Bu hacim için özel fiyat
    /// </summary>
    [Required]
    public decimal Price { get; set; }

    /// <summary>
    /// Bu hacim için stok miktarı
    /// </summary>
    public int? StockQuantity { get; set; }

    /// <summary>
    /// Sıralama değeri (küçükten büyüğe)
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Bu hacim varsayılan seçili mi?
    /// </summary>
    public bool IsDefault { get; set; } = false;

    // Navigation Properties
    public Product Product { get; set; } = null!;

    #region Fluent Configuration
    public static void Configure(EntityTypeBuilder<ProductVolume> builder)
    {
        builder.Property(pv => pv.Price)
            .HasPrecision(18, 2);

        builder.Property(pv => pv.Volume)
            .HasPrecision(18, 4);

        builder.HasOne(pv => pv.Product)
            .WithMany(p => p.Volumes)
            .HasForeignKey(pv => pv.ProductId)
            .OnDelete(DeleteBehavior.Cascade);

        // Aynı ürün için aynı hacim değeri olamaz
        builder.HasIndex(pv => new { pv.ProductId, pv.Volume }).IsUnique();

        // Aynı ürün için aynı label olamaz
        builder.HasIndex(pv => new { pv.ProductId, pv.Label }).IsUnique();
    }
    #endregion
}

[Table("ProductVolumesHistory")]
public class ProductVolumeHistory : HistoryBaseEntity
{
    // Entity properties
    public Guid ProductId { get; set; }
    public decimal Volume { get; set; }
    public string Label { get; set; } = null!;
    public string Unit { get; set; } = null!;
    public decimal Price { get; set; }
    public int? StockQuantity { get; set; }
    public int SortOrder { get; set; }
    public bool IsDefault { get; set; }
}
