using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;

[Table("PaymentProviders")]
public class PaymentProvider : BaseEntity
{
    public int StaticId { get; set; }

    [Required, MaxLength(100)]
    public string Name { get; set; } = null!;

    [Required, MaxLength(20)]
    public string ShortCode { get; set; } = null!;

    [MaxLength(500)]
    public string? Description { get; set; }

    public bool IsImplemented { get; set; } = false;

    [MaxLength(500)]
    public string? ApiUrl { get; set; }

    [MaxLength(500)]
    public string? ApiKey { get; set; }

    [MaxLength(500)]
    public string? SecretKey { get; set; }

    [MaxLength(500)]
    public string? LogoUrl { get; set; }

    public int SortOrder { get; set; } = 0;

    public static void Configure(EntityTypeBuilder<PaymentProvider> builder)
    {
        builder.HasIndex(p => p.ShortCode).IsUnique();
        builder.Property(p => p.SortOrder).HasDefaultValue(0);
        builder.Property(p => p.IsImplemented).HasDefaultValue(false);
    }
}

[Table("PaymentProvidersHistory")]
public class PaymentProviderHistory : HistoryBaseEntity
{
    public int StaticId { get; set; }
    public string Name { get; set; } = null!;
    public string ShortCode { get; set; } = null!;
    public string? Description { get; set; }
    public bool IsImplemented { get; set; } = false;
    public string? ApiUrl { get; set; }
    public string? ApiKey { get; set; }
    public string? SecretKey { get; set; }
    public string? LogoUrl { get; set; }
    public int SortOrder { get; set; } = 0;
}

