namespace Core.Events;

/// <summary>
/// <PERSON><PERSON>re sıfırlama maili gönderme event'i
/// Ş<PERSON>re sıfırlama talebinde tetiklenir
/// </summary>
public class PasswordResetNotificationRequested
{
    /// <summary>
    /// Müşteri ID'si
    /// </summary>
    public Guid CustomerId { get; set; }
    
    /// <summary>
    /// Müşteri email adresi
    /// </summary>
    public string CustomerEmail { get; set; } = null!;
    
    /// <summary>
    /// Müşteri adı
    /// </summary>
    public string CustomerName { get; set; } = null!;
    
    /// <summary>
    /// Şifre sıfırlama token'ı
    /// </summary>
    public string ResetToken { get; set; } = null!;
}
