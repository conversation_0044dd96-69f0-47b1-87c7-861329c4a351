namespace Core.Events;

/// <summary>
/// Sepette kalan ürün hatırlatma maili gönderme event'i
/// Sepet terk edildiğinde tetiklenir
/// </summary>
public class BasketProductNotificationRequested
{
    /// <summary>
    /// Müşteri ID'si
    /// </summary>
    public Guid CustomerId { get; set; }
    
    /// <summary>
    /// Müşteri email adresi
    /// </summary>
    public string CustomerEmail { get; set; } = null!;
    
    /// <summary>
    /// Müşteri adı
    /// </summary>
    public string CustomerName { get; set; } = null!;
    
    /// <summary>
    /// Sepet ID'si
    /// </summary>
    public Guid CartId { get; set; }
    
    /// <summary>
    /// Kupon kodu (varsa)
    /// </summary>
    public string? CouponCode { get; set; }
}
