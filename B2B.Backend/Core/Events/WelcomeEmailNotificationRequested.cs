namespace Core.Events;

/// <summary>
/// Hoş geldin maili gönderme event'i
/// Yeni müşteri kaydında tetiklenir
/// </summary>
public class WelcomeEmailNotificationRequested
{
    /// <summary>
    /// Müşteri ID'si
    /// </summary>
    public Guid CustomerId { get; set; }
    
    /// <summary>
    /// Müşteri email adresi
    /// </summary>
    public string CustomerEmail { get; set; } = null!;
    
    /// <summary>
    /// Müşteri adı
    /// </summary>
    public string CustomerName { get; set; } = null!;
}
