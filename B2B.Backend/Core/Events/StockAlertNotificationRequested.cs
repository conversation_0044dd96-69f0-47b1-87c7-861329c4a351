namespace Core.Events;

/// <summary>
/// Stok uyarısı maili gönderme event'i
/// Ür<PERSON>n stoğa geldiğinde tetiklenir
/// </summary>
public class StockAlertNotificationRequested
{
    /// <summary>
    /// Müşteri ID'si
    /// </summary>
    public Guid CustomerId { get; set; }
    
    /// <summary>
    /// Müşteri email adresi
    /// </summary>
    public string CustomerEmail { get; set; } = null!;
    
    /// <summary>
    /// Müşteri adı
    /// </summary>
    public string CustomerName { get; set; } = null!;
    
    /// <summary>
    /// Ürün ID'si
    /// </summary>
    public Guid ProductId { get; set; }
    
    /// <summary>
    /// Ürün adı
    /// </summary>
    public string ProductName { get; set; } = null!;
    
    /// <summary>
    /// Ürün fiyatı
    /// </summary>
    public decimal ProductPrice { get; set; }
    
    /// <summary>
    /// Ürün görseli URL'i
    /// </summary>
    public string ProductImageUrl { get; set; } = null!;
    
    /// <summary>
    /// Ürün slug'ı
    /// </summary>
    public string ProductSlug { get; set; } = null!;
}
