using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Events;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class CartController : ControllerBase
{
    private readonly ICartService _cartService;
    private readonly IPublishEndpoint _publishEndpoint;

    public CartController(ICartService cartService, IPublishEndpoint publishEndpoint)
    {
        _cartService = cartService;
        _publishEndpoint = publishEndpoint;
    }

    [HttpGet]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<List<CartListDto>>> GetList([FromQuery] int? page, [FromQuery] int? pageSize)
    {
        try
        {
            var carts = await _cartService.GetListAsync(page, pageSize);
            return Ok(carts);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<CartDto>> GetById(Guid id)
    {
        try
        {
            var cart = await _cartService.GetByIdAsync(id);
            if (cart == null)
                return NotFound(new { message = "Sepet bulunamadı" });

            return Ok(cart);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}/summary")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<CartSummaryDto>> GetCartSummary(Guid id)
    {
        try
        {
            var summary = await _cartService.GetCartSummaryAsync(id);
            if (summary == null)
                return NotFound(new { message = "Sepet bulunamadı" });

            return Ok(summary);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("customer/{customerId}")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<CartDto>> GetByCustomerId(Guid customerId)
    {
        try
        {
            var cart = await _cartService.GetByCustomerIdAsync(customerId);
            if (cart == null)
                return NotFound(new { message = "Müşteri sepeti bulunamadı" });

            return Ok(cart);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("items")]
    [RequirePermission("cart", "create")]
    public async Task<ActionResult> AddItem([FromBody] CartItemCreateDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            var cartItemId = await _cartService.AddItemAsync(dto);
            return Ok(new { message = "Ürün sepete eklendi.", cartItemId = cartItemId });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPut("items/{id}")]
    [RequirePermission("cart", "update")]
    public async Task<ActionResult> UpdateItem(Guid id, [FromBody] CartItemUpdateDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        if (id != dto.Id)
            return BadRequest(new { message = "ID uyuşmazlığı" });

        try
        {
            await _cartService.UpdateItemAsync(dto);
            return Ok(new { message = "Sepet ürünü güncellendi." });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("items/{id}")]
    [RequirePermission("cart", "delete")]
    public async Task<ActionResult> RemoveItem(Guid id)
    {
        try
        {
            await _cartService.RemoveItemAsync(id);
            return Ok(new { message = "Ürün sepetten kaldırıldı." });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/clear")]
    [RequirePermission("cart", "delete")]
    public async Task<ActionResult> ClearCart(Guid id)
    {
        try
        {
            var result = await _cartService.ClearCartAsync(id);
            if (!result)
                return NotFound(new { message = "Sepet bulunamadı" });

            return Ok(new { message = "Sepet temizlendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("items/bulk-delete")]
    [RequirePermission("cart", "delete")]
    public async Task<ActionResult> BulkRemoveItems([FromBody] BulkCartItemDeleteDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            await _cartService.BulkRemoveItemsAsync(dto);
            return Ok(new { message = "Seçili ürünler sepetten kaldırıldı." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}/is-empty")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult> IsCartEmpty(Guid id)
    {
        try
        {
            var isEmpty = await _cartService.IsCartEmptyAsync(id);
            return Ok(new { isEmpty = isEmpty });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}/item-count")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult> GetCartItemCount(Guid id)
    {
        try
        {
            var count = await _cartService.GetCartItemCountAsync(id);
            return Ok(new { count = count });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}/total")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult> GetCartTotal(Guid id)
    {
        try
        {
            var total = await _cartService.GetCartTotalAsync(id);
            return Ok(new { total = total });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("customer/{customerId}/clear")]
    [RequirePermission("cart", "delete")]
    public async Task<ActionResult> ClearCustomerCart(Guid customerId)
    {
        try
        {
            var result = await _cartService.ClearCustomerCartAsync(customerId);
            if (!result)
                return NotFound(new { message = "Müşteri sepeti bulunamadı" });

            return Ok(new { message = "Müşteri sepeti temizlendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("analytics")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<CartAnalyticsDto>> GetAnalytics()
    {
        try
        {
            var analytics = await _cartService.GetAnalyticsAsync();
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("top-products")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<List<CartTopProductsDto>>> GetTopProducts([FromQuery] int count = 10)
    {
        try
        {
            var topProducts = await _cartService.GetTopProductsInCartsAsync(count);
            return Ok(topProducts);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("abandoned")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<List<CartListDto>>> GetAbandonedCarts([FromQuery] int daysOld = 7)
    {
        try
        {
            var abandonedCarts = await _cartService.GetAbandonedCartsAsync(daysOld);
            return Ok(abandonedCarts);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("send-abandoned-cart-emails")]
    [RequirePermission("cart", "update")]
    public async Task<ActionResult> SendAbandonedCartEmails([FromQuery] int daysOld = 1)
    {
        try
        {
            var abandonedCarts = await _cartService.GetAbandonedCartsAsync(daysOld);
            int emailsSent = 0;

            foreach (var cart in abandonedCarts)
            {
                if (!string.IsNullOrEmpty(cart.CustomerEmail))
                {
                    // BasketProductNotificationRequested event'ini tetikle
                    await _publishEndpoint.Publish(new BasketProductNotificationRequested
                    {
                        CustomerId = cart.CustomerId,
                        CustomerEmail = cart.CustomerEmail,
                        CustomerName = cart.CustomerName,
                        CartId = cart.Id,
                        CouponCode = null // Şimdilik kupon kodu yok
                    });
                    emailsSent++;
                }
            }

            return Ok(new {
                message = $"{emailsSent} abandoned cart email sent successfully",
                totalAbandonedCarts = abandonedCarts.Count,
                emailsSent = emailsSent
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("search")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<List<CartListDto>>> SearchCarts([FromBody] string searchTerm)
    {
        try
        {
            var carts = await _cartService.SearchCartsAsync(searchTerm);
            return Ok(carts);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("active")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<List<CartListDto>>> GetActiveCarts()
    {
        try
        {
            var carts = await _cartService.GetActiveCartsAsync();
            return Ok(carts);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("empty")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<List<CartListDto>>> GetEmptyCarts()
    {
        try
        {
            var carts = await _cartService.GetEmptyCartsAsync();
            return Ok(carts);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}/out-of-stock")]
    [RequirePermission("cart", "read")]
    public async Task<ActionResult<List<CartItemDto>>> GetOutOfStockItems(Guid id)
    {
        try
        {
            var items = await _cartService.GetOutOfStockItemsAsync(id);
            return Ok(items);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/update-prices")]
    [RequirePermission("cart", "update")]
    public async Task<ActionResult> UpdateCartItemPrices(Guid id)
    {
        try
        {
            var hasUpdates = await _cartService.UpdateCartItemPricesAsync(id);
            return Ok(new { 
                message = hasUpdates ? "Sepet fiyatları güncellendi." : "Güncellenecek fiyat bulunamadı.",
                hasUpdates = hasUpdates 
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}
