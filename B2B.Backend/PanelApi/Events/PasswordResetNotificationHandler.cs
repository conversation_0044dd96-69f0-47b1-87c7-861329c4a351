using Application.Contracts.Interfaces;
using Core.Events;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace PanelApi.Events;

/// <summary>
/// Şifre sıfırlama maili event handler'ı
/// MailAPI'ye SendMailRequested event'i gönderir
/// </summary>
public class PasswordResetNotificationHandler : IConsumer<PasswordResetNotificationRequested>
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IMailTemplateService _mailTemplateService;
    private readonly IMailLinkService _mailLinkService;
    private readonly ILogger<PasswordResetNotificationHandler> _logger;

    public PasswordResetNotificationHandler(
        IPublishEndpoint publishEndpoint,
        IMailTemplateService mailTemplateService,
        IMailLinkService mailLinkService,
        ILogger<PasswordResetNotificationHandler> logger)
    {
        _publishEndpoint = publishEndpoint;
        _mailTemplateService = mailTemplateService;
        _mailLinkService = mailLinkService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<PasswordResetNotificationRequested> context)
    {
        try
        {
            var notification = context.Message;
            _logger.LogInformation("Processing password reset notification for customer: {CustomerEmail}", 
                notification.CustomerEmail);

            // Şifre sıfırlama linki oluştur
            var resetLink = _mailLinkService.GetPasswordResetLink(notification.ResetToken);

            // Template değişkenleri
            var variables = new Dictionary<string, string>
            {
                { "customerName", notification.CustomerName },
                { "customerEmail", notification.CustomerEmail },
                { "sendLinkAddress", resetLink }
            };

            // Template'i render et (döngü yok, sadece değişken değiştirme)
            var (subject, content) = await _mailTemplateService.RenderTemplateWithLoopsAsync(
                "password-reset", 
                variables, 
                new Dictionary<string, List<Dictionary<string, string>>>() // Boş döngü verisi
            );

            // MailAPI'ye SendMailRequested event'i gönder
            await _publishEndpoint.Publish(new SendMailRequested
            {
                TemplateShortCode = "password-reset",
                ToEmail = notification.CustomerEmail,
                ToName = notification.CustomerName,
                Variables = variables,
                Priority = 1, // Yüksek öncelik (güvenlik)
                RelatedEntityId = notification.CustomerId,
                RelatedEntityType = "Customer",
                // Render edilmiş içeriği de gönder
                CustomSubject = subject,
                CustomContent = content
            });

            _logger.LogInformation("SendMailRequested event published for password reset: {CustomerEmail}",
                notification.CustomerEmail);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing password reset notification for: {CustomerEmail}",
                context.Message.CustomerEmail);
            throw;
        }
    }
}
