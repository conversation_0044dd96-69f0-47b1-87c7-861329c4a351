using Application.Contracts.Interfaces;
using Core.Events;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace PanelApi.Events;

/// <summary>
/// Sepette kalan ürün hatırlatma maili event handler'ı
/// MailAPI'ye SendMailRequested event'i gönderir
/// </summary>
public class BasketProductNotificationHandler : IConsumer<BasketProductNotificationRequested>
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IMailTemplateService _mailTemplateService;
    private readonly IMailLinkService _mailLinkService;
    private readonly ICartService _cartService;
    private readonly ILogger<BasketProductNotificationHandler> _logger;

    public BasketProductNotificationHandler(
        IPublishEndpoint publishEndpoint,
        IMailTemplateService mailTemplateService,
        IMailLinkService mailLinkService,
        ICartService cartService,
        ILogger<BasketProductNotificationHandler> logger)
    {
        _publishEndpoint = publishEndpoint;
        _mailTemplateService = mailTemplateService;
        _mailLinkService = mailLinkService;
        _cartService = cartService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<BasketProductNotificationRequested> context)
    {
        try
        {
            var notification = context.Message;
            _logger.LogInformation("Processing basket product notification for customer: {CustomerEmail}", 
                notification.CustomerEmail);

            // Sepet bilgilerini al
            var cart = await _cartService.GetByIdAsync(notification.CartId);
            if (cart == null || !cart.Items.Any())
            {
                _logger.LogWarning("Cart not found or empty for basket notification: {CartId}", notification.CartId);
                return;
            }

            // Sepet linki oluştur
            var cartLink = _mailLinkService.GetCartLink();
            
            // Template değişkenleri
            var variables = new Dictionary<string, string>
            {
                { "customerName", notification.CustomerName },
                { "customerEmail", notification.CustomerEmail },
                { "sendLinkAddress", cartLink },
                { "couponCode", notification.CouponCode ?? "" }
            };

            // Döngü verileri - sepet ürünleri
            var loopData = new Dictionary<string, List<Dictionary<string, string>>>
            {
                {
                    "items", cart.Items.Select(item => new Dictionary<string, string>
                    {
                        { "orderRowName", item.ProductName },
                        { "orderRowImage", item.ProductImage ?? "/images/no-image.jpg" },
                        { "orderRowAmount", item.Quantity.ToString() },
                        { "orderRowPrice", item.UnitPrice.ToString("F2") },
                        { "orderRowDiscount", (item.DiscountedPrice ?? item.UnitPrice).ToString("F2") },
                        { "orderRowVariant", item.ProductSku ?? "" }
                    }).ToList()
                }
            };

            // Template'i döngü desteği ile render et
            var (subject, content) = await _mailTemplateService.RenderTemplateWithLoopsAsync(
                "basket-product-natification", 
                variables, 
                loopData
            );

            // MailAPI'ye SendMailRequested event'i gönder
            await _publishEndpoint.Publish(new SendMailRequested
            {
                TemplateShortCode = "basket-product-natification",
                ToEmail = notification.CustomerEmail,
                ToName = notification.CustomerName,
                Variables = variables,
                Priority = 3, // Normal öncelik
                RelatedEntityId = notification.CartId,
                RelatedEntityType = "Cart",
                // Render edilmiş içeriği de gönder
                CustomSubject = subject,
                CustomContent = content
            });

            _logger.LogInformation("SendMailRequested event published for basket notification: {CustomerEmail}",
                notification.CustomerEmail);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing basket product notification for: {CustomerEmail}",
                context.Message.CustomerEmail);
            throw;
        }
    }
}
