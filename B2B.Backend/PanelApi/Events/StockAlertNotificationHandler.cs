using Application.Contracts.Interfaces;
using Core.Events;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace PanelApi.Events;

/// <summary>
/// Stok uyarısı maili event handler'ı
/// MailAPI'ye SendMailRequested event'i gönderir
/// </summary>
public class StockAlertNotificationHandler : IConsumer<StockAlertNotificationRequested>
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IMailTemplateService _mailTemplateService;
    private readonly IMailLinkService _mailLinkService;
    private readonly ILogger<StockAlertNotificationHandler> _logger;

    public StockAlertNotificationHandler(
        IPublishEndpoint publishEndpoint,
        IMailTemplateService mailTemplateService,
        IMailLinkService mailLinkService,
        ILogger<StockAlertNotificationHandler> logger)
    {
        _publishEndpoint = publishEndpoint;
        _mailTemplateService = mailTemplateService;
        _mailLinkService = mailLinkService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<StockAlertNotificationRequested> context)
    {
        try
        {
            var notification = context.Message;
            _logger.LogInformation("Processing stock alert notification for customer: {CustomerEmail}", 
                notification.CustomerEmail);

            // Ürün detay linki oluştur
            var productLink = _mailLinkService.GetProductDetailLink(notification.ProductSlug);
            
            // Template değişkenleri
            var variables = new Dictionary<string, string>
            {
                { "customerName", notification.CustomerName },
                { "customerEmail", notification.CustomerEmail },
                { "ProductName", notification.ProductName },
                { "ProductPrice", notification.ProductPrice.ToString("F2") },
                { "ProductImageUrl", notification.ProductImageUrl },
                { "ActionLink", productLink }
            };

            // Template'i render et (döngü yok, sadece değişken değiştirme)
            var (subject, content) = await _mailTemplateService.RenderTemplateWithLoopsAsync(
                "stock-alert", 
                variables, 
                new Dictionary<string, List<Dictionary<string, string>>>() // Boş döngü verisi
            );

            // MailAPI'ye SendMailRequested event'i gönder
            await _publishEndpoint.Publish(new SendMailRequested
            {
                TemplateShortCode = "stock-alert",
                ToEmail = notification.CustomerEmail,
                ToName = notification.CustomerName,
                Variables = variables,
                Priority = 2, // Orta öncelik
                RelatedEntityId = notification.ProductId,
                RelatedEntityType = "Product",
                // Render edilmiş içeriği de gönder
                CustomSubject = subject,
                CustomContent = content
            });

            _logger.LogInformation("SendMailRequested event published for stock alert: {CustomerEmail}",
                notification.CustomerEmail);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing stock alert notification for: {CustomerEmail}",
                context.Message.CustomerEmail);
            throw;
        }
    }
}
