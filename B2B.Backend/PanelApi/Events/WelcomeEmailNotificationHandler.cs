using Application.Contracts.Interfaces;
using Core.Events;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace PanelApi.Events;

/// <summary>
/// Hoş geldin maili event handler'ı
/// MailAPI'ye SendMailRequested event'i gönderir
/// </summary>
public class WelcomeEmailNotificationHandler : IConsumer<WelcomeEmailNotificationRequested>
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly IMailTemplateService _mailTemplateService;
    private readonly IMailLinkService _mailLinkService;
    private readonly ILogger<WelcomeEmailNotificationHandler> _logger;

    public WelcomeEmailNotificationHandler(
        IPublishEndpoint publishEndpoint,
        IMailTemplateService mailTemplateService,
        IMailLinkService mailLinkService,
        ILogger<WelcomeEmailNotificationHandler> logger)
    {
        _publishEndpoint = publishEndpoint;
        _mailTemplateService = mailTemplateService;
        _mailLinkService = mailLinkService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<WelcomeEmailNotificationRequested> context)
    {
        try
        {
            var notification = context.Message;
            _logger.LogInformation("Processing welcome email notification for customer: {CustomerEmail}", 
                notification.CustomerEmail);

            // Hoş geldin linki oluştur
            var welcomeLink = _mailLinkService.GetWelcomeLink();

            // Template değişkenleri
            var variables = new Dictionary<string, string>
            {
                { "customerName", notification.CustomerName },
                { "customerEmail", notification.CustomerEmail },
                { "sendLinkAddress", welcomeLink }
            };

            // Template'i render et (döngü yok, sadece değişken değiştirme)
            var (subject, content) = await _mailTemplateService.RenderTemplateWithLoopsAsync(
                "welcome-email", 
                variables, 
                new Dictionary<string, List<Dictionary<string, string>>>() // Boş döngü verisi
            );

            // MailAPI'ye SendMailRequested event'i gönder
            await _publishEndpoint.Publish(new SendMailRequested
            {
                TemplateShortCode = "welcome-email",
                ToEmail = notification.CustomerEmail,
                ToName = notification.CustomerName,
                Variables = variables,
                Priority = 3, // Normal öncelik
                RelatedEntityId = notification.CustomerId,
                RelatedEntityType = "Customer",
                // Render edilmiş içeriği de gönder
                CustomSubject = subject,
                CustomContent = content
            });

            _logger.LogInformation("SendMailRequested event published for welcome email: {CustomerEmail}",
                notification.CustomerEmail);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing welcome email notification for: {CustomerEmail}",
                context.Message.CustomerEmail);
            throw;
        }
    }
}
