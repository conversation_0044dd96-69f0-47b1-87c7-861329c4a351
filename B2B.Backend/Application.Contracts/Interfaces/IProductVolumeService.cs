using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

public interface IProductVolumeService
{
    /// <summary>
    /// Ürünün tüm hacim seçeneklerini getirir
    /// </summary>
    Task<List<ProductVolumeDto>> GetByProductIdAsync(Guid productId);

    /// <summary>
    /// Müşteri için ürünün hacim seçeneklerini getirir (frontend)
    /// </summary>
    Task<List<CustomerVolumeOptionDto>> GetCustomerVolumeOptionsAsync(Guid productId);

    /// <summary>
    /// Belirli bir hacim seçeneğini getirir
    /// </summary>
    Task<ProductVolumeDto?> GetByIdAsync(Guid id);

    /// <summary>
    /// Ürün ve hacim değerine göre hacim seçeneğini getirir
    /// </summary>
    Task<ProductVolumeDto?> GetByProductAndVolumeAsync(Guid productId, decimal volume);

    /// <summary>
    /// Yeni hacim seçeneği oluşturur
    /// </summary>
    Task<Guid> CreateAsync(ProductVolumeCreateDto dto);

    /// <summary>
    /// Toplu hacim seçeneği oluşturur
    /// </summary>
    Task CreateBulkAsync(Guid productId, List<ProductVolumeCreateDto> dtos);

    /// <summary>
    /// Hacim seçeneğini günceller
    /// </summary>
    Task UpdateAsync(ProductVolumeUpdateDto dto);

    /// <summary>
    /// Hacim seçeneğini siler
    /// </summary>
    Task DeleteAsync(Guid id);

    /// <summary>
    /// Ürünün tüm hacim seçeneklerini siler
    /// </summary>
    Task DeleteByProductIdAsync(Guid productId);

    /// <summary>
    /// Hacim seçeneğinin stok miktarını günceller
    /// </summary>
    Task UpdateVolumeStockAsync(Guid volumeId, int stockQuantity);

    /// <summary>
    /// Hacim seçeneğinin stokta olup olmadığını kontrol eder
    /// </summary>
    Task<bool> IsVolumeInStockAsync(Guid volumeId);

    /// <summary>
    /// Ürünün varsayılan hacim seçeneğini getirir
    /// </summary>
    Task<ProductVolumeDto?> GetDefaultVolumeAsync(Guid productId);

    /// <summary>
    /// Ürünün varsayılan hacim seçeneğini değiştirir
    /// </summary>
    Task SetDefaultVolumeAsync(Guid productId, Guid volumeId);

    /// <summary>
    /// Hacim seçeneğinin fiyatını günceller
    /// </summary>
    Task UpdateVolumePriceAsync(Guid volumeId, decimal price);
}
