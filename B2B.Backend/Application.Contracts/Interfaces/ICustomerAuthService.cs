using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

public interface ICustomerAuthService
{
    /// <summary>
    /// Customer login with email and password
    /// </summary>
    /// <param name="dto">Login credentials</param>
    /// <returns>Authentication response with JWT token</returns>
    Task<CustomerAuthResponseDto> LoginAsync(CustomerLoginDto dto);

    /// <summary>
    /// Customer registration
    /// </summary>
    /// <param name="dto">Registration data</param>
    /// <returns>Authentication response with JWT token</returns>
    Task<CustomerAuthResponseDto> RegisterAsync(CustomerRegisterDto dto);

    /// <summary>
    /// Validate customer JWT token
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>Customer information if token is valid</returns>
    Task<CustomerDto?> ValidateTokenAsync(string token);

    /// <summary>
    /// Get customer by email for authentication
    /// </summary>
    /// <param name="email">Customer email</param>
    /// <returns>Customer data if exists</returns>
    Task<CustomerDto?> GetCustomerByEmailAsync(string email);

    /// <summary>
    /// Verify customer password
    /// </summary>
    /// <param name="customerId">Customer ID</param>
    /// <param name="password">Password to verify</param>
    /// <returns>True if password is correct</returns>
    Task<bool> VerifyPasswordAsync(Guid customerId, string password);

    /// <summary>
    /// Request password reset for customer
    /// </summary>
    /// <param name="email">Customer email</param>
    /// <returns>Response indicating success</returns>
    Task<CustomerAuthResponseDto> RequestPasswordResetAsync(string email);

    /// <summary>
    /// Reset customer password using token
    /// </summary>
    /// <param name="token">Reset token</param>
    /// <param name="newPassword">New password</param>
    /// <returns>Response indicating success</returns>
    Task<CustomerAuthResponseDto> ResetPasswordAsync(string token, string newPassword);
}
