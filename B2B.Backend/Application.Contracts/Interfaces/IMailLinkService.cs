namespace Application.Contracts.Interfaces;

/// <summary>
/// Mail template'lerinde kullanılacak linkleri oluşturan servis
/// </summary>
public interface IMailLinkService
{
    /// <summary>
    /// Ho<PERSON> geldin maili için ana sayfa linki
    /// </summary>
    string GetWelcomeLink();
    
    /// <summary>
    /// Şifre sıfırlama linki
    /// </summary>
    /// <param name="resetToken">Şifre sıfırlama token'ı</param>
    string GetPasswordResetLink(string resetToken);
    
    /// <summary>
    /// Sipariş detay linki
    /// </summary>
    /// <param name="orderId">Sipariş ID'si</param>
    string GetOrderDetailLink(Guid orderId);
    
    /// <summary>
    /// Ürün detay linki
    /// </summary>
    /// <param name="productSlug">Ürün slug'ı</param>
    string GetProductDetailLink(string productSlug);
    
    /// <summary>
    /// Sepet linki
    /// </summary>
    string GetCartLink();
    
    /// <summary>
    /// Kargo takip linki
    /// </summary>
    /// <param name="trackingNumber">Takip numarası</param>
    /// <param name="carrierName">Kargo firması adı</param>
    string GetTrackingLink(string trackingNumber, string carrierName);
    
    /// <summary>
    /// Hesap aktivasyon linki
    /// </summary>
    /// <param name="activationToken">Aktivasyon token'ı</param>
    string GetAccountActivationLink(string activationToken);
}
