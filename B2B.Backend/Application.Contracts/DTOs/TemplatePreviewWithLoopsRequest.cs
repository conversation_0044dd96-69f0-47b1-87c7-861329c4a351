namespace Application.Contracts.DTOs;

/// <summary>
/// Template önizleme isteği (döngülerle birlikte)
/// </summary>
public class TemplatePreviewWithLoopsRequest
{
    /// <summary>
    /// Ba<PERSON><PERSON> ({{variableName}} formatında)
    /// </summary>
    public Dictionary<string, string>? Variables { get; set; }

    /// <summary>
    /// Döng<PERSON> verileri (döngü adı -> liste)
    /// Örnek: "items" -> [{"orderRowName": "Ürün 1", "orderRowAmount": "2"}, ...]
    /// </summary>
    public Dictionary<string, List<Dictionary<string, string>>>? LoopData { get; set; }
}
