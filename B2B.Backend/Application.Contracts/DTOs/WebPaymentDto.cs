namespace Application.Contracts.DTOs;

public class WebPaymentDto
{
    public Guid OrderId { get; set; }
    public decimal Amount { get; set; }
    public string Description { get; set; } = null!;
    public string PaymentMethod { get; set; } = null!;
    public string PaymentResponse { get; set; } = null!;
    public string PaymentResponseCode { get; set; } = null!;
    public string PaymentResponseMessage { get; set; } = null!;
    public string PaymentResponseTransactionId { get; set; } = null!;
}

public class WebPaymentCreateDto
{
    public Guid OrderId { get; set; }
    public decimal Amount { get; set; }
    public string Description { get; set; } = null!;
    public string PaymentMethod { get; set; } = null!;
    public string PaymentResponse { get; set; } = null!;
    public string PaymentResponseCode { get; set; } = null!;
    public string PaymentResponseMessage { get; set; } = null!;
}