namespace Application.Contracts.DTOs.Pricing;

/// <summary>
/// Sepet/Sipariş fiyatlandırma özeti
/// </summary>
public class PricingCalculationSummaryDto
{
    /// <summary>
    /// Toplam orijinal tutar (indirim öncesi)
    /// </summary>
    public decimal TotalOriginalAmount { get; set; }

    /// <summary>
    /// Toplam ürün indirim tutarı
    /// </summary>
    public decimal TotalDiscountAmount { get; set; }

    /// <summary>
    /// Toplam vergi tutarı
    /// </summary>
    public decimal TotalTaxAmount { get; set; }

    /// <summary>
    /// Toplam net tutar (vergi hariç)
    /// </summary>
    public decimal TotalNetAmount { get; set; }

    /// <summary>
    /// Toplam tutar (vergi dahil, kargo ve kampanya hariç)
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Kargo tutarı
    /// </summary>
    public decimal ShippingAmount { get; set; }

    /// <summary>
    /// Kampanya indirim tutarı
    /// </summary>
    public decimal CampaignDiscountAmount { get; set; }

    /// <summary>
    /// Final tutar (kargo dahil, kampanya indirimi düşülmüş)
    /// </summary>
    public decimal FinalAmount { get; set; }

    /// <summary>
    /// Ürün sayısı
    /// </summary>
    public int ItemCount { get; set; }

    /// <summary>
    /// Toplam miktar
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// Herhangi bir üründe indirim var mı?
    /// </summary>
    public bool HasAnyDiscount { get; set; }

    /// <summary>
    /// İndirimli ürün sayısı
    /// </summary>
    public int DiscountedItemCount { get; set; }

    /// <summary>
    /// Ara toplam (ürünler toplamı, kargo hariç)
    /// </summary>
    public decimal SubTotal => TotalAmount;

    /// <summary>
    /// Toplam tasarruf (ürün indirimleri + kampanya indirimleri)
    /// </summary>
    public decimal TotalSavings => TotalDiscountAmount + CampaignDiscountAmount;
}
