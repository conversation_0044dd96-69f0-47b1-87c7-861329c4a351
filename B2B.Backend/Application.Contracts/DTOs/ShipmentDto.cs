using Core.Enums;

namespace Application.Contracts.DTOs;

public class ShipmentDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string TrackingNumber { get; set; } = null!;
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = null!;
    public string CarrierShortCode { get; set; } = null!;
    public ShipmentStatus Status { get; set; }
    public DateTime ShippedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
}
