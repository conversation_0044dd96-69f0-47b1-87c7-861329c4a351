namespace Application.Contracts.DTOs;

public class PaymentProviderDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string ShortCode { get; set; } = null!;
    public string? Description { get; set; }
    public bool IsImplemented { get; set; }
    public string? ApiUrl { get; set; }
    public string? ApiKey { get; set; }
    public string? SecretKey { get; set; }
    public string? LogoUrl { get; set; }
    public int SortOrder { get; set; }
}

