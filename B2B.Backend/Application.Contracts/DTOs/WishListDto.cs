namespace Application.Contracts.DTOs;

public class WishListDto
{
    public WishListDto()
    {
        Products = new List<ProductDto>();
    }
    public Guid CustomerId { get; set; }
    public List<ProductDto> Products { get; set; }
}

public class AddWishListDto
{
    public Guid CustomerId { get; set; }
    public Guid ProductId { get; set; }
}

public class RemoveWishListDto
{
    public Guid CustomerId { get; set; }
    public Guid ProductId { get; set; }
}
