namespace Application.Contracts.DTOs;

public class ProductVolumeDto
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public decimal Volume { get; set; }
    public string Label { get; set; } = null!;
    public string Unit { get; set; } = null!;
    public decimal Price { get; set; }
    public int? StockQuantity { get; set; }
    public int SortOrder { get; set; }
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class ProductVolumeCreateDto
{
    public Guid? ProductId { get; set; } // Opsiyonel - ürün oluşturulurken set edilecek
    public decimal Volume { get; set; }
    public string Label { get; set; } = null!;
    public string Unit { get; set; } = null!;
    public decimal Price { get; set; }
    public int? StockQuantity { get; set; }
    public int SortOrder { get; set; } = 0;
    public bool IsDefault { get; set; } = false;
}

public class ProductVolumeUpdateDto
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public decimal Volume { get; set; }
    public string Label { get; set; } = null!;
    public string Unit { get; set; } = null!;
    public decimal Price { get; set; }
    public int? StockQuantity { get; set; }
    public int SortOrder { get; set; }
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Frontend için basitleştirilmiş volume DTO
/// </summary>
public class CustomerVolumeOptionDto
{
    public Guid Id { get; set; }
    public decimal Volume { get; set; }
    public string Label { get; set; } = null!;
    public string Unit { get; set; } = null!;
    public decimal Price { get; set; }
    public bool InStock { get; set; }
    public int? StockQuantity { get; set; }
    public bool IsDefault { get; set; }
    public int SortOrder { get; set; }
}
