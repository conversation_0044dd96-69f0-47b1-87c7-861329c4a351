using System.ComponentModel.DataAnnotations;

namespace Application.Contracts.DTOs;

/// <summary>
/// Customer password reset DTO
/// </summary>
public class CustomerPasswordResetDto
{
    /// <summary>
    /// Password reset token
    /// </summary>
    [Required(ErrorMessage = "Token gerekli")]
    public string Token { get; set; } = null!;

    /// <summary>
    /// New password
    /// </summary>
    [Required(ErrorMessage = "Yeni şifre gerekli")]
    [MinLength(6, ErrorMessage = "Şifre en az 6 karakter olmalı")]
    public string NewPassword { get; set; } = null!;

    /// <summary>
    /// Confirm new password
    /// </summary>
    [Required(ErrorMessage = "Ş<PERSON>re onayı gerekli")]
    [Compare("NewPassword", ErrorMessage = "Şifreler eşleşmiyor")]
    public string ConfirmPassword { get; set; } = null!;
}
