using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Interfaces;

namespace Infrastructure.Services;

public class CustomerWishListService : ICustomerWishListService
{
    private readonly IGenericRepository<Favourite> _favouriteRepository;
    public CustomerWishListService(IGenericRepository<Favourite> favouriteRepository)
    {
        _favouriteRepository = favouriteRepository;
    }
    public async Task<WishListDto> GetWishListItems(Guid customerId)
    {
        return MapToWishListDto(await _favouriteRepository.FirstOrDefaultAsync(x => x.CustomerId == customerId));
    }

    WishListDto MapToWishListDto(Favourite? favourite)
    {
        if (favourite == null)
        {
            return new WishListDto();
        }
        return new WishListDto
        {
            Products = favourite.Products.Select(x => new ProductDto
            {
                Name = x.Name,
                Price = x.Price,
                Images = x.Images
                .Where(pi => pi.IsMainImage)
                .Select(pi => new ProductImageDto
                {
                    OriginalImagePath = pi.OriginalPath,
                    ThumbnailMediumPath = pi.ThumbnailMdPath,
                    ThumbnailSmallPath = pi.ThumbnailSmPath
                }).ToList(),
            }).ToList()
        };
    }
}
