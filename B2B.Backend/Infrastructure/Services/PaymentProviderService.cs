using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Application.Contracts.Repositories;

namespace Infrastructure.Services;

public class PaymentProviderService : IPaymentProviderService
{
    private readonly IPaymentProviderRepository _repo;

    public PaymentProviderService(IPaymentProviderRepository repo)
    {
        _repo = repo;
    }

    public async Task<PaymentProviderDto?> GetByShortCodeAsync(string shortCode)
    {
        var entity = await _repo.GetByShortCodeAsync(shortCode);
        if (entity == null) return null;
        return new PaymentProviderDto
        {
            Id = entity.Id,
            Name = entity.Name,
            ShortCode = entity.ShortCode,
            Description = entity.Description,
            IsImplemented = entity.IsImplemented,
            ApiUrl = entity.ApiUrl,
            ApiKey = entity.ApiKey,
            SecretKey = entity.SecretKey,
            LogoUrl = entity.LogoUrl,
            SortOrder = entity.SortOrder
        };
    }

    public async Task<Dictionary<string, string>?> GetSettingsAsync(Guid id)
    {
        var entity = await _repo.GetByIdAsync(id);
        if (entity == null) return null;

        var dict = new Dictionary<string, string>();
        if (!string.IsNullOrWhiteSpace(entity.ApiUrl)) dict["BaseUrl"] = entity.ApiUrl!;
        if (!string.IsNullOrWhiteSpace(entity.ApiKey)) dict["ApiKey"] = entity.ApiKey!;
        if (!string.IsNullOrWhiteSpace(entity.SecretKey)) dict["SecretKey"] = entity.SecretKey!;
        return dict;
    }
}

