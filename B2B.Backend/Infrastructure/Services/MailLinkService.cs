using Application.Contracts.Interfaces;
using Microsoft.Extensions.Configuration;

namespace Infrastructure.Services;

/// <summary>
/// Mail template'lerinde kullanılacak linkleri oluşturan servis
/// </summary>
public class MailLinkService : IMailLinkService
{
    private readonly IConfiguration _configuration;
    private readonly string _baseUrl;

    public MailLinkService(IConfiguration configuration)
    {
        _configuration = configuration;
        _baseUrl = _configuration["B2C_FRONTEND_BASE_URL"] ?? "http://localhost:3000";
    }

    /// <summary>
    /// Hoş geldin maili için ana sayfa linki
    /// </summary>
    public string GetWelcomeLink()
    {
        return _baseUrl;
    }

    /// <summary>
    /// Şifre sıfırlama linki
    /// </summary>
    /// <param name="resetToken"><PERSON><PERSON>re sıfırlama token'ı</param>
    public string GetPasswordResetLink(string resetToken)
    {
        return $"{_baseUrl}/auth/reset-password?token={resetToken}";
    }

    /// <summary>
    /// Sipariş detay linki
    /// </summary>
    /// <param name="orderId">Sipariş ID'si</param>
    public string GetOrderDetailLink(Guid orderId)
    {
        return $"{_baseUrl}/hesabim/siparislerim/{orderId}";
    }

    /// <summary>
    /// Ürün detay linki
    /// </summary>
    /// <param name="productSlug">Ürün slug'ı</param>
    public string GetProductDetailLink(string productSlug)
    {
        return $"{_baseUrl}/urun/{productSlug}";
    }

    /// <summary>
    /// Sepet linki
    /// </summary>
    public string GetCartLink()
    {
        return $"{_baseUrl}/sepet";
    }

    /// <summary>
    /// Kargo takip linki
    /// </summary>
    /// <param name="trackingNumber">Takip numarası</param>
    /// <param name="carrierName">Kargo firması adı</param>
    public string GetTrackingLink(string trackingNumber, string carrierName)
    {
        // Kargo firmasına göre farklı linkler
        return carrierName.ToLower() switch
        {
            "yurtiçi kargo" or "yurtici kargo" => $"https://kargo.yurtici.com.tr/takip/{trackingNumber}",
            "mng kargo" => $"https://www.mngkargo.com.tr/takip/{trackingNumber}",
            "aras kargo" => $"https://www.araskargo.com.tr/takip/{trackingNumber}",
            "ptt kargo" => $"https://gonderitakip.ptt.gov.tr/Track/Verify?q={trackingNumber}",
            _ => $"{_baseUrl}/kargo-takip?no={trackingNumber}" // Varsayılan kendi sayfamız
        };
    }

    /// <summary>
    /// Hesap aktivasyon linki
    /// </summary>
    /// <param name="activationToken">Aktivasyon token'ı</param>
    public string GetAccountActivationLink(string activationToken)
    {
        return $"{_baseUrl}/auth/activate?token={activationToken}";
    }
}
