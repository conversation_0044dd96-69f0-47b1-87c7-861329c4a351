using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class ProductVolumeService : IProductVolumeService
{
    private readonly IGenericRepository<ProductVolume> _repository;
    private readonly IGenericRepository<Product> _productRepository;

    public ProductVolumeService(
        IGenericRepository<ProductVolume> repository,
        IGenericRepository<Product> productRepository)
    {
        _repository = repository;
        _productRepository = productRepository;
    }

    public async Task<List<ProductVolumeDto>> GetByProductIdAsync(Guid productId)
    {
        try
        {
            var volumes = await _repository.Query()
                .Where(v => v.ProductId == productId)
                .OrderBy(v => v.SortOrder)
                .ThenBy(v => v.Volume)
                .ToListAsync();

            return volumes.Select(v => new ProductVolumeDto
            {
                Id = v.Id,
                ProductId = v.ProductId,
                Volume = v.Volume,
                Label = v.Label,
                Unit = v.Unit,
                Price = v.Price,
                StockQuantity = v.StockQuantity,
                SortOrder = v.SortOrder,
                IsDefault = v.IsDefault,
                IsActive = v.IsActive,
                IsDeleted = v.IsDeleted,
                CreatedAt = v.CreatedAt,
                UpdatedAt = v.UpdatedAt
            }).ToList();
        }
        catch
        {
            return [];
        }
    }

    public async Task<List<CustomerVolumeOptionDto>> GetCustomerVolumeOptionsAsync(Guid productId)
    {
        try
        {
            var volumes = await _repository.Query()
                .Where(v => v.ProductId == productId && v.IsActive && !v.IsDeleted)
                .OrderBy(v => v.SortOrder)
                .ThenBy(v => v.Volume)
                .ToListAsync();

            return volumes.Select(v => new CustomerVolumeOptionDto
            {
                Id = v.Id,
                Volume = v.Volume,
                Label = v.Label,
                Unit = v.Unit,
                Price = v.Price,
                InStock = v.StockQuantity.HasValue && v.StockQuantity > 0,
                StockQuantity = v.StockQuantity,
                IsDefault = v.IsDefault,
                SortOrder = v.SortOrder
            }).ToList();
        }
        catch
        {
            return [];
        }
    }

    public async Task<ProductVolumeDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var volume = await _repository.GetByIdAsync(id);
            if (volume == null) return null;

            return new ProductVolumeDto
            {
                Id = volume.Id,
                ProductId = volume.ProductId,
                Volume = volume.Volume,
                Label = volume.Label,
                Unit = volume.Unit,
                Price = volume.Price,
                StockQuantity = volume.StockQuantity,
                SortOrder = volume.SortOrder,
                IsDefault = volume.IsDefault,
                IsActive = volume.IsActive,
                IsDeleted = volume.IsDeleted,
                CreatedAt = volume.CreatedAt,
                UpdatedAt = volume.UpdatedAt
            };
        }
        catch
        {
            return null;
        }
    }

    public async Task<ProductVolumeDto?> GetByProductAndVolumeAsync(Guid productId, decimal volume)
    {
        try
        {
            var productVolume = await _repository.Query()
                .FirstOrDefaultAsync(v => v.ProductId == productId && v.Volume == volume);
            
            if (productVolume == null) return null;

            return new ProductVolumeDto
            {
                Id = productVolume.Id,
                ProductId = productVolume.ProductId,
                Volume = productVolume.Volume,
                Label = productVolume.Label,
                Unit = productVolume.Unit,
                Price = productVolume.Price,
                StockQuantity = productVolume.StockQuantity,
                SortOrder = productVolume.SortOrder,
                IsDefault = productVolume.IsDefault,
                IsActive = productVolume.IsActive,
                IsDeleted = productVolume.IsDeleted,
                CreatedAt = productVolume.CreatedAt,
                UpdatedAt = productVolume.UpdatedAt
            };
        }
        catch
        {
            return null;
        }
    }

    public async Task<Guid> CreateAsync(ProductVolumeCreateDto dto)
    {
        try
        {
            var volume = new ProductVolume
            {
                ProductId = dto.ProductId ?? Guid.Empty,
                Volume = dto.Volume,
                Label = dto.Label,
                Unit = dto.Unit,
                Price = dto.Price,
                StockQuantity = dto.StockQuantity,
                SortOrder = dto.SortOrder,
                IsDefault = dto.IsDefault
            };

            await _repository.AddAsync(volume);
            await _repository.SaveChangesAsync();

            return volume.Id;
        }
        catch
        {
            return Guid.Empty;
        }
    }

    public async Task CreateBulkAsync(Guid productId, List<ProductVolumeCreateDto> dtos)
    {
        try
        {
            var volumes = dtos.Select(dto => new ProductVolume
            {
                ProductId = productId,
                Volume = dto.Volume,
                Label = dto.Label,
                Unit = dto.Unit,
                Price = dto.Price,
                StockQuantity = dto.StockQuantity,
                SortOrder = dto.SortOrder,
                IsDefault = dto.IsDefault
            }).ToList();

            await _repository.AddRangeAsync(volumes);
            await _repository.SaveChangesAsync();
        }
        catch
        {
            // Log error
        }
    }

    public async Task UpdateAsync(ProductVolumeUpdateDto dto)
    {
        try
        {
            var volume = await _repository.GetByIdAsync(dto.Id);
            if (volume == null) return;

            volume.Volume = dto.Volume;
            volume.Label = dto.Label;
            volume.Unit = dto.Unit;
            volume.Price = dto.Price;
            volume.StockQuantity = dto.StockQuantity;
            volume.SortOrder = dto.SortOrder;
            volume.IsDefault = dto.IsDefault;
            volume.IsActive = dto.IsActive;

            _repository.Update(volume);
            await _repository.SaveChangesAsync();
        }
        catch
        {
            // Log error
        }
    }

    public async Task DeleteAsync(Guid id)
    {
        try
        {
            var volume = await _repository.GetByIdAsync(id);
            if (volume == null) return;

            volume.IsDeleted = true;
            volume.IsActive = false;

            _repository.Update(volume);
            await _repository.SaveChangesAsync();
        }
        catch
        {
            // Log error
        }
    }

    public async Task DeleteByProductIdAsync(Guid productId)
    {
        try
        {
            var volumes = await _repository.Query()
                .Where(v => v.ProductId == productId)
                .ToListAsync();

            foreach (var volume in volumes)
            {
                volume.IsDeleted = true;
                volume.IsActive = false;
            }

            _repository.UpdateRange(volumes);
            await _repository.SaveChangesAsync();
        }
        catch
        {
            // Log error
        }
    }

    public async Task UpdateVolumeStockAsync(Guid volumeId, int stockQuantity)
    {
        try
        {
            var volume = await _repository.GetByIdAsync(volumeId);
            if (volume == null) return;

            volume.StockQuantity = stockQuantity;
            _repository.Update(volume);
            await _repository.SaveChangesAsync();
        }
        catch
        {
            // Log error
        }
    }

    public async Task<bool> IsVolumeInStockAsync(Guid volumeId)
    {
        try
        {
            var volume = await _repository.GetByIdAsync(volumeId);
            return volume != null && volume.StockQuantity.HasValue && volume.StockQuantity > 0;
        }
        catch
        {
            return false;
        }
    }

    public async Task<ProductVolumeDto?> GetDefaultVolumeAsync(Guid productId)
    {
        try
        {
            var volume = await _repository.Query()
                .Where(v => v.ProductId == productId && v.IsDefault && v.IsActive && !v.IsDeleted)
                .FirstOrDefaultAsync();

            if (volume == null)
            {
                // Varsayılan yoksa ilk hacmi döndür
                volume = await _repository.Query()
                    .Where(v => v.ProductId == productId && v.IsActive && !v.IsDeleted)
                    .OrderBy(v => v.SortOrder)
                    .ThenBy(v => v.Volume)
                    .FirstOrDefaultAsync();
            }

            if (volume == null) return null;

            return new ProductVolumeDto
            {
                Id = volume.Id,
                ProductId = volume.ProductId,
                Volume = volume.Volume,
                Label = volume.Label,
                Unit = volume.Unit,
                Price = volume.Price,
                StockQuantity = volume.StockQuantity,
                SortOrder = volume.SortOrder,
                IsDefault = volume.IsDefault,
                IsActive = volume.IsActive,
                IsDeleted = volume.IsDeleted,
                CreatedAt = volume.CreatedAt,
                UpdatedAt = volume.UpdatedAt
            };
        }
        catch
        {
            return null;
        }
    }

    public async Task SetDefaultVolumeAsync(Guid productId, Guid volumeId)
    {
        try
        {
            // Önce tüm hacim seçeneklerini varsayılan olmayan yap
            var allVolumes = await _repository.Query()
                .Where(v => v.ProductId == productId)
                .ToListAsync();

            foreach (var vol in allVolumes)
            {
                vol.IsDefault = vol.Id == volumeId;
            }

            _repository.UpdateRange(allVolumes);
            await _repository.SaveChangesAsync();
        }
        catch
        {
            // Log error
        }
    }

    public async Task UpdateVolumePriceAsync(Guid volumeId, decimal price)
    {
        try
        {
            var volume = await _repository.GetByIdAsync(volumeId);
            if (volume == null) return;

            volume.Price = price;
            _repository.Update(volume);
            await _repository.SaveChangesAsync();
        }
        catch
        {
            // Log error
        }
    }
}
