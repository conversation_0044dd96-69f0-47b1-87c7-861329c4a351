using Application.Contracts.DTOs.Pricing;
using Application.Contracts.Interfaces;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Services;

/// <summary>
/// Merkezi fiyatlandırma hesaplama servisi
/// Product.Price ve Product.DiscountedPrice kullanarak indirim ve vergi hesaplamaları yapar
/// </summary>
public class PricingCalculationService : IPricingCalculationService
{
    private readonly ILogger<PricingCalculationService> _logger;
    
    // Vergi oranı %20 (1.2 ile çarpım/bölüm için)
    private const decimal TAX_RATE = 1.2m;

    public PricingCalculationService(ILogger<PricingCalculationService> logger)
    {
        _logger = logger;
    }

    public PricingCalculationResultDto CalculateItemPricing(decimal price, decimal? discountedPrice, int quantity = 1)
    {
        try
        {
            var result = new PricingCalculationResultDto();

            // Temel fiyat bilgileri
            result.OriginalPrice = price;
            result.DiscountedPrice = discountedPrice;
            result.Quantity = quantity;

            // Eğer indirimli fiyat varsa ve 0'dan bü<PERSON><PERSON>kse, onu kullan; yoksa normal fiyatı kullan
            var effectivePrice = (discountedPrice.HasValue && discountedPrice.Value > 0) ? discountedPrice.Value : price;

            // İndirim tutarı hesaplama - indirimli fiyat varsa, 0'dan büyükse ve normal fiyattan küçükse indirim var
            if (discountedPrice.HasValue && discountedPrice.Value > 0 && discountedPrice.Value < price)
            {
                result.DiscountAmount = (price - discountedPrice.Value) * quantity;
                result.HasDiscount = true;
            }
            else
            {
                result.DiscountAmount = 0;
                result.HasDiscount = false;
            }

            // Vergi hesaplama
            // DiscountedPrice - DiscountedPrice / 1.2 = Vergi Tutarı
            // Eğer DiscountedPrice yoksa, Price - Price / 1.2 hesaplaması kullanılır
            var taxBaseAmount = effectivePrice * quantity;
            result.TaxAmount = taxBaseAmount - (taxBaseAmount / TAX_RATE);
            
            // Net tutar (vergi hariç)
            result.NetAmount = taxBaseAmount / TAX_RATE;
            
            // Toplam tutar (vergi dahil)
            result.TotalAmount = taxBaseAmount;

            _logger.LogDebug("Pricing calculated for item: Price={Price}, DiscountedPrice={DiscountedPrice}, Quantity={Quantity}, " +
                           "DiscountAmount={DiscountAmount}, TaxAmount={TaxAmount}, TotalAmount={TotalAmount}",
                           price, discountedPrice, quantity, result.DiscountAmount, result.TaxAmount, result.TotalAmount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating item pricing: Price={Price}, DiscountedPrice={DiscountedPrice}, Quantity={Quantity}",
                           price, discountedPrice, quantity);
            throw;
        }
    }

    public PricingCalculationSummaryDto CalculateCartPricing(List<PricingCalculationResultDto> items)
    {
        try
        {
            var summary = new PricingCalculationSummaryDto();

            if (items == null || items.Count == 0)
            {
                return summary;
            }

            // Toplam hesaplamalar
            summary.TotalOriginalAmount = items.Sum(i => i.OriginalPrice * i.Quantity);
            summary.TotalDiscountAmount = items.Sum(i => i.DiscountAmount);
            summary.TotalTaxAmount = items.Sum(i => i.TaxAmount);
            summary.TotalNetAmount = items.Sum(i => i.NetAmount);
            summary.TotalAmount = items.Sum(i => i.TotalAmount);
            
            // İstatistikler
            summary.ItemCount = items.Count;
            summary.TotalQuantity = items.Sum(i => i.Quantity);
            summary.HasAnyDiscount = items.Any(i => i.HasDiscount);
            summary.DiscountedItemCount = items.Count(i => i.HasDiscount);

            // Kargo ve kampanya tutarları (başlangıçta 0, dışarıdan set edilecek)
            summary.ShippingAmount = 0;
            summary.CampaignDiscountAmount = 0;

            // Final tutar hesaplama (kargo dahil, kampanya indirimi hariç)
            summary.FinalAmount = summary.TotalAmount + summary.ShippingAmount - summary.CampaignDiscountAmount;

            _logger.LogDebug("Cart pricing calculated: ItemCount={ItemCount}, TotalAmount={TotalAmount}, " +
                           "TotalDiscountAmount={TotalDiscountAmount}, TotalTaxAmount={TotalTaxAmount}",
                           summary.ItemCount, summary.TotalAmount, summary.TotalDiscountAmount, summary.TotalTaxAmount);

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating cart pricing summary");
            throw;
        }
    }

    public PricingCalculationSummaryDto CalculateOrderPricing(List<PricingCalculationResultDto> items, 
        decimal shippingAmount = 0, decimal campaignDiscountAmount = 0)
    {
        try
        {
            var summary = CalculateCartPricing(items);
            
            // Kargo ve kampanya tutarlarını ekle
            summary.ShippingAmount = shippingAmount;
            summary.CampaignDiscountAmount = campaignDiscountAmount;
            
            // Final tutar yeniden hesapla
            summary.FinalAmount = summary.TotalAmount + summary.ShippingAmount - summary.CampaignDiscountAmount;

            _logger.LogDebug("Order pricing calculated: TotalAmount={TotalAmount}, ShippingAmount={ShippingAmount}, " +
                           "CampaignDiscountAmount={CampaignDiscountAmount}, FinalAmount={FinalAmount}",
                           summary.TotalAmount, summary.ShippingAmount, summary.CampaignDiscountAmount, summary.FinalAmount);

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating order pricing");
            throw;
        }
    }

    public decimal CalculateTaxFromGrossAmount(decimal grossAmount)
    {
        try
        {
            // Vergi dahil tutardan vergi miktarını hesapla
            // GrossAmount - GrossAmount / 1.2 = Tax Amount
            var taxAmount = grossAmount - (grossAmount / TAX_RATE);
            
            _logger.LogDebug("Tax calculated from gross amount: GrossAmount={GrossAmount}, TaxAmount={TaxAmount}",
                           grossAmount, taxAmount);
            
            return taxAmount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating tax from gross amount: {GrossAmount}", grossAmount);
            throw;
        }
    }

    public decimal CalculateNetFromGrossAmount(decimal grossAmount)
    {
        try
        {
            // Vergi dahil tutardan net tutarı hesapla
            // GrossAmount / 1.2 = Net Amount
            var netAmount = grossAmount / TAX_RATE;
            
            _logger.LogDebug("Net amount calculated from gross amount: GrossAmount={GrossAmount}, NetAmount={NetAmount}",
                           grossAmount, netAmount);
            
            return netAmount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating net from gross amount: {GrossAmount}", grossAmount);
            throw;
        }
    }

    public decimal CalculateDiscountAmount(decimal originalPrice, decimal discountedPrice, int quantity = 1)
    {
        try
        {
            if (discountedPrice >= originalPrice)
            {
                return 0;
            }

            var discountAmount = (originalPrice - discountedPrice) * quantity;
            
            _logger.LogDebug("Discount calculated: OriginalPrice={OriginalPrice}, DiscountedPrice={DiscountedPrice}, " +
                           "Quantity={Quantity}, DiscountAmount={DiscountAmount}",
                           originalPrice, discountedPrice, quantity, discountAmount);
            
            return discountAmount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating discount amount");
            throw;
        }
    }
}
