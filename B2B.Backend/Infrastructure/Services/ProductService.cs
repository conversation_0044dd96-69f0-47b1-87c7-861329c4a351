using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Events;
using Core.Interfaces;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class ProductService : IProductService
{
    private readonly IGenericRepository<Product> _repository;
    private readonly IGenericRepository<ProductAttributeMapping> _mappingRepository;
    private readonly IGenericRepository<ProductImage> _imageRepository;
    private readonly IGenericRepository<ProductFaq> _faqRepository;
    private readonly IGenericRepository<ProductSeo> _seoRepository;
    private readonly IPublishEndpoint _publishEndpoint;

    public ProductService(
        IGenericRepository<Product> repository,
        IGenericRepository<ProductAttributeMapping> mappingRepository,
        IGenericRepository<ProductImage> imageRepository,
        IGenericRepository<ProductFaq> faqRepository,
        IGenericRepository<ProductSeo> seoRepository,
        IPublishEndpoint publishEndpoint)
    {
        _repository = repository;
        _mappingRepository = mappingRepository;
        _imageRepository = imageRepository;
        _faqRepository = faqRepository;
        _seoRepository = seoRepository;
        _publishEndpoint = publishEndpoint;
    }

    public async Task<List<ProductDto>> GetListAsync(int? page, int? pageSize)
    {
        try
        {
            var query = _repository.Query();
            query = query.Where(p => p.ParentProductId == null);
            if (page.HasValue && pageSize.HasValue)
            {
                query = query.Skip((page.Value - 1) * pageSize.Value).Take(pageSize.Value);
            }

            var products = await query
                .Include(p => p.Category)
                .Include(p => p.Variants)
                .Include(p => p.Brand)
                .Include(p => p.Images)
                .Include(p => p.Faqs)
                .Include(p => p.Seo)
                .Include(p => p.AttributeMappings)
                    .ThenInclude(am => am.Attribute)
                .Include(p => p.AttributeMappings)
                    .ThenInclude(am => am.AttributeValue)
                .ToListAsync();

            return products.Select(p => new ProductDto
            {
                Id = p.Id,
                Name = p.Name,
                Slug = p.Slug,
                Description = p.Description,
                IsActive = p.IsActive,
                IsDeleted = p.IsDeleted,
                ProductType = p.ProductType,
                Seo = p.Seo != null ? new ProductSeoDto
                {
                    Id = p.Seo.Id,
                    ProductId = p.Seo.ProductId,
                    MetaTitle = p.Seo.MetaTitle,
                    MetaDescription = p.Seo.MetaDescription,
                    MetaKeywords = p.Seo.MetaKeywords,
                    OgTitle = p.Seo.OgTitle,
                    OgDescription = p.Seo.OgDescription,
                    OgImage = p.Seo.OgImage,
                    StructuredData = p.Seo.StructuredData,
                    CanonicalUrl = p.Seo.CanonicalUrl,
                    NoIndex = p.Seo.NoIndex,
                    NoFollow = p.Seo.NoFollow
                } : null,
                Category = p.Category != null ? new ProductCategoryDto
                {
                    Id = p.Category.Id,
                    Name = p.Category.Name,
                    Slug = p.Category.Slug,
                    ParentId = p.Category.ParentId
                } : null,
                Brand = p.Brand != null ? new ProductBrandDto
                {
                    Id = p.Brand.Id,
                    Name = p.Brand.Name,
                    Description = p.Brand.Description
                } : null,
                Sku = p.Sku,
                Barcode = p.Barcode,
                Price = p.Price,
                DiscountedPrice = p.DiscountedPrice,
                StockQuantity = p.StockQuantity,
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt,
                Variants = p.Variants.Select(v => new ProductDto
                {
                    Id = v.Id,
                    Name = v.Name,
                    Slug = v.Slug,
                    Description = v.Description,
                    ProductType = v.ProductType,
                    Sku = v.Sku,
                    Barcode = v.Barcode,
                    Price = v.Price,
                    StockQuantity = v.StockQuantity,
                    CreatedAt = v.CreatedAt,
                    UpdatedAt = v.UpdatedAt
                }).ToList(),
                Images = p.Images.Select(img => new ProductImageDto
                {
                    Id = img.Id,
                    ProductId = img.ProductId,
                    OriginalImagePath = img.OriginalPath,
                    ThumbnailSmallPath = img.ThumbnailSmPath,
                    ThumbnailMediumPath = img.ThumbnailMdPath,
                    FileName = img.Name,
                    AltText = img.AltText,
                    OriginalWidth = img.OriginalWidth,
                    OriginalHeight = img.OriginalHeight,
                    FileSizeBytes = img.FileSizeBytes,
                    IsMainImage = img.IsMainImage,
                    SortOrder = img.SortOrder
                }).ToList(),
                Faqs = p.Faqs.Select(faq => new ProductFaqDto
                {
                    Id = faq.Id,
                    ProductId = faq.ProductId,
                    Question = faq.Question,
                    Answer = faq.Answer,
                    SortOrder = faq.SortOrder
                }).ToList(),
                AttributeMappings = p.AttributeMappings.Select(am => new ProductAttributeMappingDto
                {
                    Id = am.Id,
                    ProductId = am.ProductId,
                    AttributeId = am.AttributeId,
                    AttributeValueId = am.AttributeValueId,
                    Attribute = am.Attribute != null ? new ProductAttributeDto
                    {
                        Id = am.Attribute.Id,
                        Name = am.Attribute.Name,
                        ShortName = am.Attribute.ShortName,
                        IsVariantAttribute = am.Attribute.IsVariantAttribute,
                        IsListAttribute = am.Attribute.IsListAttribute
                    } : null,
                    AttributeValue = am.AttributeValue != null ? new ProductAttributeValueDto
                    {
                        Id = am.AttributeValue.Id,
                        AttributeId = am.AttributeValue.AttributeId,
                        Value = am.AttributeValue.Value
                    } : null
                }).ToList()
            }).ToList();
        }
        catch
        {
            return new List<ProductDto>();
        }
    }

    public async Task<ProductDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var product = await _repository.Query()
                .Include(p => p.Variants)
                    .ThenInclude(v => v.AttributeMappings)
                        .ThenInclude(am => am.Attribute)
                .Include(p => p.Variants)
                    .ThenInclude(v => v.AttributeMappings)
                        .ThenInclude(am => am.AttributeValue)
                .Include(p => p.Parent)
                .Include(p => p.Category)
                .Include(p => p.Brand)
                .Include(p => p.Faqs)
                .Include(p => p.Images)
                .Include(p => p.Seo)
                .Include(p => p.AttributeMappings)
                    .ThenInclude(am => am.Attribute)
                .Include(p => p.AttributeMappings)
                    .ThenInclude(am => am.AttributeValue)
                .FirstOrDefaultAsync(p => p.Id == id);
            if (product == null) return null;

            // Eğer bu bir varyant ise, ana ürünü ve tüm varyantları getir
            List<Product> allVariants = new List<Product>();
            Product mainProduct = product;

            if (product.ParentProductId.HasValue)
            {
                // Bu bir varyant, ana ürünü getir
                mainProduct = await _repository.Query()
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.AttributeMappings)
                            .ThenInclude(am => am.Attribute)
                    .Include(p => p.Variants)
                        .ThenInclude(v => v.AttributeMappings)
                            .ThenInclude(am => am.AttributeValue)
                    .Include(p => p.Category)
                    .Include(p => p.Brand)
                    .Include(p => p.Faqs)
                    .Include(p => p.Images)
                    .Include(p => p.Seo)
                    .Include(p => p.AttributeMappings)
                        .ThenInclude(am => am.Attribute)
                    .Include(p => p.AttributeMappings)
                        .ThenInclude(am => am.AttributeValue)
                    .FirstOrDefaultAsync(p => p.Id == product.ParentProductId.Value);

                if (mainProduct != null)
                {
                    allVariants = mainProduct.Variants.ToList();
                }
            }
            else
            {
                // Bu ana ürün, varyantları al
                allVariants = product.Variants.ToList();
            }

            return new ProductDto
            {
                Id = product.Id,
                Name = product.Name,
                Slug = product.Slug,
                Description = product.Description,
                IsActive = product.IsActive,
                IsDeleted = product.IsDeleted,
                ProductType = product.ProductType,
                Category = product.Category != null ? new ProductCategoryDto
                {
                    Id = product.Category.Id,
                    Name = product.Category.Name,
                    Slug = product.Category.Slug,
                    ParentId = product.Category.ParentId
                } : null,
                Brand = product.Brand != null ? new ProductBrandDto
                {
                    Id = product.Brand.Id,
                    Name = product.Brand.Name,
                    Description = product.Brand.Description
                } : null,
                Sku = product.Sku,
                Barcode = product.Barcode,
                Price = product.Price,
                DiscountedPrice = product.DiscountedPrice,
                StockQuantity = product.StockQuantity,
                CreatedAt = product.CreatedAt,
                UpdatedAt = product.UpdatedAt,
                Variants = allVariants.Select(v => new ProductDto
                {
                    Id = v.Id,
                    Name = v.Name,
                    Slug = v.Slug,
                    Description = v.Description,
                    IsActive = v.IsActive,
                    IsDeleted = v.IsDeleted,
                    ProductType = v.ProductType,
                    Sku = v.Sku,
                    Barcode = v.Barcode,
                    Price = v.Price,
                    StockQuantity = v.StockQuantity,
                    CreatedAt = v.CreatedAt,
                    UpdatedAt = v.UpdatedAt,
                    AttributeMappings = v.AttributeMappings.Select(am => new ProductAttributeMappingDto
                    {
                        Id = am.Id,
                        ProductId = am.ProductId,
                        AttributeId = am.AttributeId,
                        AttributeValueId = am.AttributeValueId,
                        Attribute = am.Attribute != null ? new ProductAttributeDto
                        {
                            Id = am.Attribute.Id,
                            Name = am.Attribute.Name,
                            ShortName = am.Attribute.ShortName,
                            IsVariantAttribute = am.Attribute.IsVariantAttribute,
                            IsListAttribute = am.Attribute.IsListAttribute
                        } : null,
                        AttributeValue = am.AttributeValue != null ? new ProductAttributeValueDto
                        {
                            Id = am.AttributeValue.Id,
                            AttributeId = am.AttributeValue.AttributeId,
                            Value = am.AttributeValue.Value
                        } : null
                    }).ToList()
                }).ToList(),
                Images = product.Images.Select(img => new ProductImageDto
                {
                    Id = img.Id,
                    ProductId = img.ProductId,
                    OriginalImagePath = img.OriginalPath,
                    ThumbnailSmallPath = img.ThumbnailSmPath,
                    ThumbnailMediumPath = img.ThumbnailMdPath,
                    FileName = img.Name,
                    AltText = img.AltText,
                    OriginalWidth = img.OriginalWidth,
                    OriginalHeight = img.OriginalHeight,
                    FileSizeBytes = img.FileSizeBytes,
                    IsMainImage = img.IsMainImage,
                    SortOrder = img.SortOrder
                }).ToList(),
                Faqs = product.Faqs.Select(faq => new ProductFaqDto
                {
                    Id = faq.Id,
                    ProductId = faq.ProductId,
                    Question = faq.Question,
                    Answer = faq.Answer,
                    SortOrder = faq.SortOrder
                }).ToList(),
                Seo = product.Seo != null ? new ProductSeoDto
                {
                    Id = product.Seo.Id,
                    ProductId = product.Seo.ProductId,
                    MetaTitle = product.Seo.MetaTitle,
                    MetaDescription = product.Seo.MetaDescription,
                    MetaKeywords = product.Seo.MetaKeywords,
                    OgTitle = product.Seo.OgTitle,
                    OgDescription = product.Seo.OgDescription,
                    OgImage = product.Seo.OgImage,
                    StructuredData = product.Seo.StructuredData,
                    CanonicalUrl = product.Seo.CanonicalUrl,
                    NoIndex = product.Seo.NoIndex,
                    NoFollow = product.Seo.NoFollow
                } : null,
                AttributeMappings = product.AttributeMappings.Select(am => new ProductAttributeMappingDto
                {
                    Id = am.Id,
                    ProductId = am.ProductId,
                    AttributeId = am.AttributeId,
                    AttributeValueId = am.AttributeValueId,
                    Attribute = am.Attribute != null ? new ProductAttributeDto
                    {
                        Id = am.Attribute.Id,
                        Name = am.Attribute.Name,
                        ShortName = am.Attribute.ShortName,
                        IsVariantAttribute = am.Attribute.IsVariantAttribute,
                        IsListAttribute = am.Attribute.IsListAttribute
                    } : null,
                    AttributeValue = am.AttributeValue != null ? new ProductAttributeValueDto
                    {
                        Id = am.AttributeValue.Id,
                        AttributeId = am.AttributeValue.AttributeId,
                        Value = am.AttributeValue.Value
                    } : null
                }).ToList()
            };
        }
        catch
        {
            return null;
        }
    }

    public async Task<Guid> CreateAsync(ProductCreateDto dto)
    {
        try
        {
            // Ana ürünü oluştur
            var product = new Product
            {
                Name = dto.Name,
                Slug = await GenerateSlugAsync(dto.Name),
                Description = dto.Description,
                ProductType = dto.ProductType,
                CategoryId = dto.CategoryId,
                BrandId = dto.BrandId,
                Sku = dto.Sku,
                Barcode = dto.Barcode,
                Price = dto.Price,
                DiscountedPrice = dto.DiscountedPrice,
                StockQuantity = dto.StockQuantity
            };

            await _repository.AddAsync(product);
            await _repository.SaveChangesAsync();

            Console.WriteLine($"Product created with ID: {product.Id}");

            // AttributeMappings'i ekle
            if (dto.AttributeMappings != null && dto.AttributeMappings.Any())
            {
                Console.WriteLine($"Adding {dto.AttributeMappings.Count} attribute mappings");
                foreach (var mappingDto in dto.AttributeMappings)
                {
                    var mapping = new ProductAttributeMapping
                    {
                        ProductId = product.Id,
                        AttributeId = mappingDto.AttributeId,
                        AttributeValueId = mappingDto.AttributeValueId
                    };
                    await _mappingRepository.AddAsync(mapping);
                }
                await _mappingRepository.SaveChangesAsync();
                Console.WriteLine("Attribute mappings saved");
            }
            else
            {
                Console.WriteLine("No attribute mappings to add");
            }

            // Images'ları ekle
            if (dto.Images != null && dto.Images.Any())
            {
                Console.WriteLine($"Adding {dto.Images.Count} images");
                foreach (var imageDto in dto.Images)
                {
                    var image = new ProductImage
                    {
                        ProductId = product.Id,
                        OriginalPath = imageDto.OriginalImagePath,
                        ThumbnailSmPath = imageDto.ThumbnailSmallPath,
                        ThumbnailMdPath = imageDto.ThumbnailMediumPath,
                        Name = imageDto.FileName,
                        AltText = imageDto.AltText,
                        OriginalWidth = imageDto.OriginalWidth,
                        OriginalHeight = imageDto.OriginalHeight,
                        FileSizeBytes = imageDto.FileSizeBytes,
                        IsMainImage = imageDto.IsMainImage,
                        SortOrder = imageDto.SortOrder
                    };
                    await _imageRepository.AddAsync(image);
                }
                await _imageRepository.SaveChangesAsync();
                Console.WriteLine("Images saved");
            }
            else
            {
                Console.WriteLine("No images to add");
            }

            // FAQs'ları ekle
            if (dto.Faqs != null && dto.Faqs.Any())
            {
                Console.WriteLine($"Adding {dto.Faqs.Count} FAQs");
                foreach (var faqDto in dto.Faqs)
                {
                    var faq = new ProductFaq
                    {
                        ProductId = product.Id,
                        Question = faqDto.Question,
                        Answer = faqDto.Answer,
                        SortOrder = faqDto.SortOrder
                    };
                    await _faqRepository.AddAsync(faq);
                }
                await _faqRepository.SaveChangesAsync();
                Console.WriteLine("FAQs saved");
            }
            else
            {
                Console.WriteLine("No FAQs to add");
            }

            // SEO'yu ekle
            if (dto.Seo != null && (!string.IsNullOrEmpty(dto.Seo.MetaTitle) || !string.IsNullOrEmpty(dto.Seo.MetaDescription)))
            {
                Console.WriteLine("Adding SEO data");
                var seo = new ProductSeo
                {
                    ProductId = product.Id,
                    MetaTitle = dto.Seo.MetaTitle,
                    MetaDescription = dto.Seo.MetaDescription,
                    MetaKeywords = dto.Seo.MetaKeywords,
                    OgTitle = dto.Seo.OgTitle,
                    OgDescription = dto.Seo.OgDescription,
                    OgImage = dto.Seo.OgImage,
                    StructuredData = dto.Seo.StructuredData,
                    CanonicalUrl = dto.Seo.CanonicalUrl,
                    NoIndex = dto.Seo.NoIndex,
                    NoFollow = dto.Seo.NoFollow
                };
                product.Seo = seo;
                _repository.Update(product);
                await _repository.SaveChangesAsync();
                Console.WriteLine("SEO saved");
            }
            else
            {
                Console.WriteLine("No SEO data to add");
            }

            Console.WriteLine($"Product creation completed for ID: {product.Id}");
            return product.Id;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating product: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            throw new Exception($"Ürün oluşturulurken bir hata oluştu: {ex.Message}");
        }
    }

    public async Task UpdateAsync(ProductUpdateDto dto)
    {
        try
        {
            var product = await _repository.Query()
                .Include(p => p.Seo)
                .FirstOrDefaultAsync(p => p.Id == dto.Id);
            if (product == null)
                throw new Exception("Ürün bulunamadı.");

            // Stok kontrolü - eğer stok 0'dan büyük bir değere çıkıyorsa stok uyarısı gönder
            bool wasOutOfStock = product.StockQuantity <= 0;
            bool willBeInStock = dto.StockQuantity > 0;

            product.Name = dto.Name;
            product.Slug = await GenerateSlugAsync(dto.Name);
            product.Description = dto.Description;
            product.ProductType = dto.ProductType;
            product.CategoryId = dto.CategoryId;
            product.BrandId = dto.BrandId;
            product.Sku = dto.Sku;
            product.Barcode = dto.Barcode;
            product.Price = dto.Price;
            product.DiscountedPrice = dto.DiscountedPrice;
            product.StockQuantity = dto.StockQuantity;
            product.IsActive = dto.IsActive;

            // SEO güncelleme
            if (dto.Seo != null && (!string.IsNullOrEmpty(dto.Seo.MetaTitle) || !string.IsNullOrEmpty(dto.Seo.MetaDescription)))
            {
                if (product.Seo == null)
                {
                    // Yeni SEO oluştur
                    product.Seo = new ProductSeo
                    {
                        ProductId = product.Id,
                        MetaTitle = dto.Seo.MetaTitle,
                        MetaDescription = dto.Seo.MetaDescription,
                        MetaKeywords = dto.Seo.MetaKeywords,
                        OgTitle = dto.Seo.OgTitle,
                        OgDescription = dto.Seo.OgDescription,
                        OgImage = dto.Seo.OgImage,
                        StructuredData = dto.Seo.StructuredData,
                        CanonicalUrl = dto.Seo.CanonicalUrl,
                        NoIndex = dto.Seo.NoIndex,
                        NoFollow = dto.Seo.NoFollow
                    };
                }
                else
                {
                    // Mevcut SEO'yu güncelle
                    product.Seo.MetaTitle = dto.Seo.MetaTitle;
                    product.Seo.MetaDescription = dto.Seo.MetaDescription;
                    product.Seo.MetaKeywords = dto.Seo.MetaKeywords;
                    product.Seo.OgTitle = dto.Seo.OgTitle;
                    product.Seo.OgDescription = dto.Seo.OgDescription;
                    product.Seo.OgImage = dto.Seo.OgImage;
                    product.Seo.StructuredData = dto.Seo.StructuredData;
                    product.Seo.CanonicalUrl = dto.Seo.CanonicalUrl;
                    product.Seo.NoIndex = dto.Seo.NoIndex;
                    product.Seo.NoFollow = dto.Seo.NoFollow;
                }
            }

            _repository.Update(product);
            await _repository.SaveChangesAsync();

            // Eğer ürün stokta yokken stoğa geldiyse stok uyarısı gönder
            if (wasOutOfStock && willBeInStock && product.IsActive)
            {
                try
                {
                    // Basit demo için - gerçek uygulamada müşteri stok takip listesi olmalı
                    // Şimdilik sadece log atalım
                    Console.WriteLine($"Product {product.Name} is back in stock. Stock quantity: {product.StockQuantity}");

                    // Demo amaçlı - gerçek uygulamada müşteri listesi olmalı
                    // await _publishEndpoint.Publish(new StockAlertNotificationRequested
                    // {
                    //     CustomerId = customerId,
                    //     CustomerEmail = customerEmail,
                    //     CustomerName = customerName,
                    //     ProductId = product.Id,
                    //     ProductName = product.Name,
                    //     ProductPrice = product.Price ?? 0,
                    //     ProductSlug = product.Slug,
                    //     ProductImageUrl = product.Images?.FirstOrDefault()?.ThumbnailMediumPath ?? "/images/no-image.jpg"
                    // });
                }
                catch (Exception ex)
                {
                    // Stok uyarısı gönderiminde hata olsa bile ürün güncellemesi başarılı olmalı
                    Console.WriteLine($"Error sending stock alert for product {product.Id}: {ex.Message}");
                }
            }
        }
        catch
        {
            throw new Exception("Ürün güncellenirken bir hata oluştu.");
        }
    }

    public async Task DeleteAsync(Guid id)
    {
        try
        {
            var product = await _repository.GetByIdAsync(id);
            if (product == null)
                throw new Exception("Ürün bulunamadı.");

            _repository.Delete(product);
            await _repository.SaveChangesAsync();
        }
        catch
        {
            throw new Exception("Ürün silinirken bir hata oluştu.");
        }
    }

    private async Task<string> GenerateSlugAsync(string name)
    {
        var baseSlug = name.ToLowerInvariant()
            .Replace(" ", "-")
            .Replace("ç", "c")
            .Replace("ğ", "g")
            .Replace("ı", "i")
            .Replace("ö", "o")
            .Replace("ş", "s")
            .Replace("ü", "u");

        // Benzersiz slug oluşturmak için kontrol et
        var slug = baseSlug;
        var counter = 1;

        while (await _repository.Query().AnyAsync(p => p.Slug == slug))
        {
            slug = $"{baseSlug}-{counter}";
            counter++;
        }

        return slug;
    }

    public async Task CreateVariantsAsync(Guid parentProductId, List<ProductVariantCreateDto> variants)
    {
        try
        {
            Console.WriteLine($"Creating {variants.Count} variants for product {parentProductId}");

            // Ana ürünün tüm bilgilerini al
            var parentProduct = await _repository.GetByIdAsync(parentProductId);
            if (parentProduct == null)
            {
                throw new Exception($"Ana ürün bulunamadı: {parentProductId}");
            }

            // Ana ürünün ilişkili verilerini al
            var parentImages = await _imageRepository.FindAsync(i => i.ProductId == parentProductId);
            var parentAttributeMappings = await _mappingRepository.FindAsync(m => m.ProductId == parentProductId);
            var parentSeo = await _seoRepository.FirstOrDefaultAsync(s => s.ProductId == parentProductId);
            var parentFaqs = await _faqRepository.FindAsync(f => f.ProductId == parentProductId);

            foreach (var variantDto in variants)
            {
                // Varyant ürünü oluştur - ana ürünün tüm özelliklerini kopyala
                var variant = new Product
                {
                    Name = variantDto.Name,
                    Slug = await GenerateSlugAsync(variantDto.Name),
                    Sku = variantDto.Sku,
                    Barcode = variantDto.Barcode,
                    Price = variantDto.Price,
                    DiscountedPrice = variantDto.DiscountedPrice,
                    StockQuantity = variantDto.StockQuantity,
                    ProductType = Core.Enums.ProductType.Simple, // Varyantlar Simple olur
                    ParentProductId = parentProductId, // Ana ürüne bağla

                    // Ana üründen kopyalanan alanlar
                    Description = parentProduct.Description,
                    CategoryId = parentProduct.CategoryId,
                    BrandId = parentProduct.BrandId,

                    IsActive = false,
                    IsDeleted = false
                };

                await _repository.AddAsync(variant);
                await _repository.SaveChangesAsync();

                Console.WriteLine($"Created variant: {variant.Name} with ID: {variant.Id}");

                // 1. Ana ürünün fotoğraflarını kopyala
                if (parentImages.Any())
                {
                    var variantImages = parentImages.Select(img => new ProductImage
                    {
                        ProductId = variant.Id,
                        OriginalPath = img.OriginalPath,
                        ThumbnailSmPath = img.ThumbnailSmPath,
                        ThumbnailMdPath = img.ThumbnailMdPath,
                        Name = img.Name,
                        AltText = img.AltText,
                        OriginalWidth = img.OriginalWidth,
                        OriginalHeight = img.OriginalHeight,
                        FileSizeBytes = img.FileSizeBytes,
                        IsMainImage = img.IsMainImage,
                        SortOrder = img.SortOrder
                    }).ToList();

                    await _imageRepository.AddRangeAsync(variantImages);
                    Console.WriteLine($"Copied {variantImages.Count} images for variant {variant.Id}");
                }

                // 2. Ana ürünün SEO bilgilerini kopyala
                if (parentSeo != null)
                {
                    var variantSeo = new ProductSeo
                    {
                        ProductId = variant.Id,
                        MetaTitle = parentSeo.MetaTitle,
                        MetaDescription = parentSeo.MetaDescription,
                        MetaKeywords = parentSeo.MetaKeywords,
                        OgTitle = parentSeo.OgTitle,
                        OgDescription = parentSeo.OgDescription,
                        OgImage = parentSeo.OgImage,
                        StructuredData = parentSeo.StructuredData
                    };

                    await _seoRepository.AddAsync(variantSeo);
                    Console.WriteLine($"Copied SEO data for variant {variant.Id}");
                }

                // 3. Ana ürünün SSS'lerini kopyala
                if (parentFaqs.Any())
                {
                    var variantFaqs = parentFaqs.Select(faq => new ProductFaq
                    {
                        ProductId = variant.Id,
                        Question = faq.Question,
                        Answer = faq.Answer,
                        SortOrder = faq.SortOrder
                    }).ToList();

                    await _faqRepository.AddRangeAsync(variantFaqs);
                    Console.WriteLine($"Copied {variantFaqs.Count} FAQs for variant {variant.Id}");
                }

                // 4. Ana ürünün niteliklerini kopyala (varyant olmayan nitelikler)
                var nonVariantMappings = parentAttributeMappings.Where(m =>
                    !variantDto.AttributeMappings.Any(vm => vm.AttributeId == m.AttributeId)).ToList();

                if (nonVariantMappings.Any())
                {
                    var variantNonVariantMappings = nonVariantMappings.Select(m => new ProductAttributeMapping
                    {
                        ProductId = variant.Id,
                        AttributeId = m.AttributeId,
                        AttributeValueId = m.AttributeValueId
                    }).ToList();

                    await _mappingRepository.AddRangeAsync(variantNonVariantMappings);
                    Console.WriteLine($"Copied {variantNonVariantMappings.Count} non-variant attribute mappings for variant {variant.Id}");
                }

                // 5. Varyant özel attribute mappings'lerini ekle
                if (variantDto.AttributeMappings.Count > 0)
                {
                    var variantSpecificMappings = variantDto.AttributeMappings.Select(am => new ProductAttributeMapping
                    {
                        ProductId = variant.Id,
                        AttributeId = am.AttributeId,
                        AttributeValueId = am.AttributeValueId
                    }).ToList();

                    await _mappingRepository.AddRangeAsync(variantSpecificMappings);
                    Console.WriteLine($"Added {variantSpecificMappings.Count} variant-specific attribute mappings for variant {variant.Id}");
                }
            }

            // Tüm değişiklikleri kaydet
            await _imageRepository.SaveChangesAsync();
            await _seoRepository.SaveChangesAsync();
            await _faqRepository.SaveChangesAsync();
            await _mappingRepository.SaveChangesAsync();

            Console.WriteLine("All variants created successfully with copied data");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating variants: {ex.Message}");
            throw;
        }
    }

    public async Task UpdateVariantsAsync(Guid parentProductId, List<ProductVariantCreateDto> variants)
    {
        try
        {
            Console.WriteLine($"Updating variants for product {parentProductId}");

            // Mevcut varyantları sil
            var existingVariants = await _repository.Query()
                .Where(p => p.ParentProductId == parentProductId)
                .ToListAsync();

            if (existingVariants.Any())
            {
                _repository.DeleteRange(existingVariants);
                await _repository.SaveChangesAsync();
                Console.WriteLine($"Deleted {existingVariants.Count} existing variants");
            }

            // Yeni varyantları oluştur
            if (variants.Count > 0)
            {
                await CreateVariantsAsync(parentProductId, variants);
            }

            Console.WriteLine("Variants updated successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating variants: {ex.Message}");
            throw;
        }
    }
}
