using Infrastructure.Services;

namespace Infrastructure.Extensions;

public static class EncryptionExtensions
{
    private static readonly EncryptionService _encryptionService = new();

    public static string Encrypt(this string plainText)
        => _encryptionService.Encrypt(plainText);

    public static string Decrypt(this string encryptedText)
        => _encryptionService.Decrypt(encryptedText);

    public static string? EncryptIfNotEmpty(this string? plainText)
        => _encryptionService.EncryptIfNotEmpty(plainText);

    public static string? DecryptIfNotEmpty(this string? encryptedText)
        => _encryptionService.DecryptIfNotEmpty(encryptedText);

    public static string GenerateLookupHash(this string? plainText)
        => _encryptionService.GenerateLookupHash(plainText);
}

