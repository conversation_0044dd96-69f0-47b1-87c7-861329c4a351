﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MassTransit" Version="8.5.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application.Contracts\Application.Contracts.csproj" />
    <ProjectReference Include="..\Core\Core.csproj" />
    <ProjectReference Include="..\Modules\Shipping.Abstraction\Shipping.Abstraction.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.Extensions.Configuration">
      <HintPath>
        ..\..\..\..\..\..\usr\local\share\dotnet\shared\Microsoft.AspNetCore.App\9.0.4\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>