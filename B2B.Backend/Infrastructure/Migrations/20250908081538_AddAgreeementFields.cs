﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAgreeementFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "AcceptedKvkk",
                table: "CustomersHistory",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "AcceptedMembershipAgreement",
                table: "CustomersHistory",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "KvkkAcceptedAt",
                table: "CustomersHistory",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "MembershipAgreementAcceptedAt",
                table: "CustomersHistory",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "AcceptedKvkk",
                table: "Customers",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "AcceptedMembershipAgreement",
                table: "Customers",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "KvkkAcceptedAt",
                table: "Customers",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "MembershipAgreementAcceptedAt",
                table: "Customers",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AcceptedKvkk",
                table: "CustomersHistory");

            migrationBuilder.DropColumn(
                name: "AcceptedMembershipAgreement",
                table: "CustomersHistory");

            migrationBuilder.DropColumn(
                name: "KvkkAcceptedAt",
                table: "CustomersHistory");

            migrationBuilder.DropColumn(
                name: "MembershipAgreementAcceptedAt",
                table: "CustomersHistory");

            migrationBuilder.DropColumn(
                name: "AcceptedKvkk",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "AcceptedMembershipAgreement",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "KvkkAcceptedAt",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "MembershipAgreementAcceptedAt",
                table: "Customers");
        }
    }
}
