﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ChangeFavoriteEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Favourites_Products_ProductId",
                table: "Favourites");

            migrationBuilder.DropIndex(
                name: "IX_Favourites_ProductId",
                table: "Favourites");

            migrationBuilder.DropColumn(
                name: "ProductId",
                table: "Favourites");

            migrationBuilder.AddColumn<Guid>(
                name: "FavouriteId",
                table: "Products",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Products_FavouriteId",
                table: "Products",
                column: "FavouriteId");

            migrationBuilder.AddForeignKey(
                name: "FK_Products_Favourites_FavouriteId",
                table: "Products",
                column: "FavouriteId",
                principalTable: "Favourites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Products_Favourites_FavouriteId",
                table: "Products");

            migrationBuilder.DropIndex(
                name: "IX_Products_FavouriteId",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "FavouriteId",
                table: "Products");

            migrationBuilder.AddColumn<Guid>(
                name: "ProductId",
                table: "Favourites",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Favourites_ProductId",
                table: "Favourites",
                column: "ProductId");

            migrationBuilder.AddForeignKey(
                name: "FK_Favourites_Products_ProductId",
                table: "Favourites",
                column: "ProductId",
                principalTable: "Products",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
