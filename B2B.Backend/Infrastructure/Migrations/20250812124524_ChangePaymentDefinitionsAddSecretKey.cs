﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ChangePaymentDefinitionsAddSecretKey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Setting<PERSON>",
                table: "PaymentProviders");

            migrationBuilder.RenameColumn(
                name: "Setting<PERSON>",
                table: "PaymentProvidersHistory",
                newName: "SecretKey");

            migrationBuilder.AddColumn<string>(
                name: "SecretK<PERSON>",
                table: "PaymentProviders",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SecretK<PERSON>",
                table: "PaymentProviders");

            migrationBuilder.RenameColumn(
                name: "<PERSON><PERSON><PERSON>",
                table: "PaymentProvidersHistory",
                newName: "Settings");

            migrationBuilder.AddColumn<string>(
                name: "Setting<PERSON>",
                table: "PaymentProviders",
                type: "text",
                nullable: true);
        }
    }
}
