using Core.Entities;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Seeders;

/// <summary>
/// Payment provider seeder - IYZICO için seed işlemi
/// Kargo seeder mantı<PERSON><PERSON>na benzer şekilde mevcut kaydı günceller veya yoksa oluşturur
/// </summary>
public class PaymentProviderSeeder
{
    private readonly B2BDbContext _context;
    private readonly ILogger<PaymentProviderSeeder> _logger;

    public PaymentProviderSeeder(B2BDbContext context, ILogger<PaymentProviderSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        try
        {
            _logger.LogInformation("Starting payment provider seeding...");
            await SeedIyzicoAsync();
            await _context.SaveChangesAsync();
            _logger.LogInformation("Payment provider seeding completed.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during payment provider seeding");
            throw;
        }
    }

    private async Task SeedIyzicoAsync()
    {
        const string shortCode = "IYZICO";
        var existing = await _context.PaymentProviders.FirstOrDefaultAsync(p => p.ShortCode == shortCode && !p.IsDeleted);

        if (existing == null)
        {
            _logger.LogInformation("Creating payment provider: {ShortCode}", shortCode);
            var nextStaticId = (await _context.PaymentProviders.Where(x => !x.IsDeleted).MaxAsync(x => (int?)x.StaticId)) ?? 0;
            var nextSortOrder = (await _context.PaymentProviders.Where(x => !x.IsDeleted).MaxAsync(x => (int?)x.SortOrder)) ?? 0;

            var entity = new PaymentProvider
            {
                Id = Guid.CreateVersion7(),
                StaticId = (nextStaticId is int s ? s : 0) + 1,
                Name = "Iyzico",
                ShortCode = shortCode,
                Description = "Iyzico ödeme entegrasyonu (sandbox)",
                ApiUrl = "https://sandbox-api.iyzipay.com",
                ApiKey = "sandbox-********************************",
                SecretKey = "sandbox-********************************",
                IsImplemented = true,
                IsActive = true,
                LogoUrl = "/images/payments/iyzico-logo.png",
                SortOrder = (nextSortOrder is int so ? so : 0) + 10,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _context.PaymentProviders.AddAsync(entity);
        }
    }
}

