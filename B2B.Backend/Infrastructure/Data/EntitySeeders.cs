using Core.Entities;
using Core.Enums;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Infrastructure.Data;

public static class EntitySeeders
{
    public static async Task SeedProductCategories(B2BDbContext context)
    {
        if (await context.ProductCategories.AnyAsync())
            return;

        var categories = new List<ProductCategory>
        {
            new ProductCategory
            {
                Name = "Elektronik",
                Slug = "elektronik",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new ProductCategory
            {
                Name = "Bilgisayarlar",
                Slug = "bilgisayarlar",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new ProductCategory
            {
                Name = "Telefonlar",
                Slug = "telefonlar",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new ProductCategory
            {
                Name = "Ev Aletleri",
                Slug = "ev-aletleri",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new ProductCategory
            {
                Name = "Mobilya",
                Slug = "mobilya",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        // Alt kategoriler ekle
        var laptops = new ProductCategory
        {
            Name = "Dizüstü Bilgisayarlar",
            Slug = "dizustu-bilgisayarlar",
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var desktops = new ProductCategory
        {
            Name = "Masaüstü Bilgisayarlar",
            Slug = "masaustu-bilgisayarlar",
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var smartphones = new ProductCategory
        {
            Name = "Akıllı Telefonlar",
            Slug = "akilli-telefonlar",
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await context.ProductCategories.AddRangeAsync(categories);
        await context.SaveChangesAsync();

        // Ana kategorileri veritabanından al
        var elektronik = await context.ProductCategories.FirstOrDefaultAsync(c => c.Slug == "elektronik");
        var bilgisayarlar = await context.ProductCategories.FirstOrDefaultAsync(c => c.Slug == "bilgisayarlar");
        var telefonlar = await context.ProductCategories.FirstOrDefaultAsync(c => c.Slug == "telefonlar");

        // Alt kategorilere parent ID'leri ata
        if (bilgisayarlar != null)
        {
            laptops.ParentId = bilgisayarlar.Id;
            desktops.ParentId = bilgisayarlar.Id;
        }

        if (telefonlar != null)
        {
            smartphones.ParentId = telefonlar.Id;
        }

        await context.ProductCategories.AddRangeAsync(new[] { laptops, desktops, smartphones });
        await context.SaveChangesAsync();
    }

    public static async Task SeedProductAttributes(B2BDbContext context)
    {
        if (await context.Set<ProductAttribute>().AnyAsync())
            return;

        var attributes = new List<ProductAttribute>
        {
            new ProductAttribute
            {
                Name = "Renk",
                ShortName = "renk",
                IsVariantAttribute = true,
                IsListAttribute = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new ProductAttribute
            {
                Name = "Boyut",
                ShortName = "boyut",
                IsVariantAttribute = true,
                IsListAttribute = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new ProductAttribute
            {
                Name = "Malzeme",
                ShortName = "malzeme",
                IsVariantAttribute = false,
                IsListAttribute = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new ProductAttribute
            {
                Name = "Marka",
                ShortName = "marka",
                IsVariantAttribute = false,
                IsListAttribute = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.Set<ProductAttribute>().AddRangeAsync(attributes);
        await context.SaveChangesAsync();

        // Attribute değerleri ekle
        var renkAttribute = await context.Set<ProductAttribute>().FirstOrDefaultAsync(a => a.ShortName == "renk");
        var boyutAttribute = await context.Set<ProductAttribute>().FirstOrDefaultAsync(a => a.ShortName == "boyut");
        var malzemeAttribute = await context.Set<ProductAttribute>().FirstOrDefaultAsync(a => a.ShortName == "malzeme");
        var markaAttribute = await context.Set<ProductAttribute>().FirstOrDefaultAsync(a => a.ShortName == "marka");

        var attributeValues = new List<ProductAttributeValue>();

        if (renkAttribute != null)
        {
            attributeValues.AddRange(new[]
            {
                new ProductAttributeValue { AttributeId = renkAttribute.Id, Value = "Siyah", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = renkAttribute.Id, Value = "Beyaz", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = renkAttribute.Id, Value = "Kırmızı", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = renkAttribute.Id, Value = "Mavi", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = renkAttribute.Id, Value = "Yeşil", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
            });
        }

        if (boyutAttribute != null)
        {
            attributeValues.AddRange(new[]
            {
                new ProductAttributeValue { AttributeId = boyutAttribute.Id, Value = "XS", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = boyutAttribute.Id, Value = "S", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = boyutAttribute.Id, Value = "M", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = boyutAttribute.Id, Value = "L", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = boyutAttribute.Id, Value = "XL", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
            });
        }

        if (malzemeAttribute != null)
        {
            attributeValues.AddRange(new[]
            {
                new ProductAttributeValue { AttributeId = malzemeAttribute.Id, Value = "Ahşap", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = malzemeAttribute.Id, Value = "Metal", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = malzemeAttribute.Id, Value = "Plastik", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = malzemeAttribute.Id, Value = "Cam", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
            });
        }

        if (markaAttribute != null)
        {
            attributeValues.AddRange(new[]
            {
                new ProductAttributeValue { AttributeId = markaAttribute.Id, Value = "Apple", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = markaAttribute.Id, Value = "Samsung", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = markaAttribute.Id, Value = "Lenovo", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = markaAttribute.Id, Value = "HP", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ProductAttributeValue { AttributeId = markaAttribute.Id, Value = "Dell", IsActive = true, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
            });
        }

        await context.Set<ProductAttributeValue>().AddRangeAsync(attributeValues);
        await context.SaveChangesAsync();
    }

    public static async Task SeedDealers(B2BDbContext context)
    {
        if (await context.Dealers.AnyAsync())
            return;

        var dealers = new List<Dealer>
        {
            new Dealer
            {
                Name = "ABC Elektronik Ltd. Şti.",
                Description = "Elektronik ürünler için toptan satış bayisi",
                PhoneNumber = "+90 ************",
                Email = "<EMAIL>",
                Website = "https://www.abcelektronik.com",
                TaxNumber = "1234567890",
                TaxOffice = "İstanbul",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Dealer
            {
                Name = "XYZ Bilgisayar A.Ş.",
                Description = "Bilgisayar ve donanım ürünleri distribütörü",
                PhoneNumber = "+90 ************",
                Email = "<EMAIL>",
                Website = "https://www.xyzbilgisayar.com",
                TaxNumber = "9876543210",
                TaxOffice = "Ankara",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Dealer
            {
                Name = "Mobilya Dünyası",
                Description = "Ofis ve ev mobilyaları toptan satış",
                PhoneNumber = "+90 ************",
                Email = "<EMAIL>",
                Website = "https://www.mobilyadunyasi.com",
                TaxNumber = "5678901234",
                TaxOffice = "İzmir",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.Dealers.AddRangeAsync(dealers);
        await context.SaveChangesAsync();

        // Bayilere adres ekle
        var dealerAddresses = new List<Address>();
        var dealers_db = await context.Dealers.ToListAsync();

        foreach (var dealer in dealers_db)
        {
            dealerAddresses.Add(new Address
            {
                DealerId = dealer.Id,
                AddressType = AddressType.Billing,
                Name = $"{dealer.Name} Merkez",
                Line1 = "Örnek Mahallesi, Örnek Caddesi No:123",
                City = "İstanbul",
                District = "Kadıköy",
                Country = "Türkiye",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        await context.Addresses.AddRangeAsync(dealerAddresses);
        await context.SaveChangesAsync();
    }

    public static async Task SeedProducts(B2BDbContext context)
    {
        if (await context.Products.AnyAsync())
            return;

        // Kategorileri al
        var elektronikKategori = await context.ProductCategories.FirstOrDefaultAsync(c => c.Slug == "elektronik");
        var bilgisayarlarKategori = await context.ProductCategories.FirstOrDefaultAsync(c => c.Slug == "bilgisayarlar");
        var telefonlarKategori = await context.ProductCategories.FirstOrDefaultAsync(c => c.Slug == "telefonlar");
        var laptopsKategori = await context.ProductCategories.FirstOrDefaultAsync(c => c.Slug == "dizustu-bilgisayarlar");
        var smartphonesKategori = await context.ProductCategories.FirstOrDefaultAsync(c => c.Slug == "akilli-telefonlar");

        // Ürünleri oluştur
        var products = new List<Product>();

        if (laptopsKategori != null)
        {
            products.AddRange(new[]
            {
                new Product
                {
                    Name = "Lenovo ThinkPad X1 Carbon",
                    Slug = "lenovo-thinkpad-x1-carbon",
                    Description = "14 inç, Intel Core i7, 16GB RAM, 512GB SSD",
                    ProductType = ProductType.Simple,
                    CategoryId = laptopsKategori.Id,
                    Sku = "LNV-X1-001",
                    Barcode = "8901234567890",
                    Price = 24999.99m,
                    StockQuantity = 50,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Product
                {
                    Name = "MacBook Pro 16",
                    Slug = "macbook-pro-16",
                    Description = "16 inç, Apple M2 Pro, 32GB RAM, 1TB SSD",
                    ProductType = ProductType.Simple,
                    CategoryId = laptopsKategori.Id,
                    Sku = "APP-MBP-001",
                    Barcode = "8901234567891",
                    Price = 49999.99m,
                    StockQuantity = 25,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Product
                {
                    Name = "Dell XPS 15",
                    Slug = "dell-xps-15",
                    Description = "15.6 inç, Intel Core i9, 32GB RAM, 1TB SSD",
                    ProductType = ProductType.Simple,
                    CategoryId = laptopsKategori.Id,
                    Sku = "DEL-XPS-001",
                    Barcode = "8901234567892",
                    Price = 34999.99m,
                    StockQuantity = 30,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            });
        }

        if (smartphonesKategori != null)
        {
            products.AddRange(new[]
            {
                new Product
                {
                    Name = "iPhone 15 Pro",
                    Slug = "iphone-15-pro",
                    Description = "6.1 inç, A17 Pro, 8GB RAM, 256GB Depolama",
                    ProductType = ProductType.Simple,
                    CategoryId = smartphonesKategori.Id,
                    Sku = "APP-IP15-001",
                    Barcode = "8901234567893",
                    Price = 39999.99m,
                    StockQuantity = 100,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Product
                {
                    Name = "Samsung Galaxy S23 Ultra",
                    Slug = "samsung-galaxy-s23-ultra",
                    Description = "6.8 inç, Snapdragon 8 Gen 2, 12GB RAM, 512GB Depolama",
                    ProductType = ProductType.Simple,
                    CategoryId = smartphonesKategori.Id,
                    Sku = "SAM-S23U-001",
                    Barcode = "8901234567894",
                    Price = 34999.99m,
                    StockQuantity = 75,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            });
        }

        await context.Products.AddRangeAsync(products);
        await context.SaveChangesAsync();

        // Ürün stok bilgilerini ekle
        var stocks = new List<Stock>();
        var products_db = await context.Products.ToListAsync();

        foreach (var product in products_db)
        {
            stocks.Add(new Stock
            {
                ProductId = product.Id,
                Quantity = product.StockQuantity ?? 0,
                MinQuantity = 5,
                MaxQuantity = 1000,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        await context.Stocks.AddRangeAsync(stocks);
        await context.SaveChangesAsync();

        // Ürün görselleri ekle
        var images = new List<ProductImage>();
        foreach (var product in products_db)
        {
            // Slug'dan ürün adını çıkar ve boşlukları tire ile değiştir
            var productNameForImage = Regex.Replace(product.Slug, "-", "_");

            images.Add(new ProductImage
            {
                ProductId = product.Id,
                OriginalPath = $"/images/products/{productNameForImage}_main.webp",
                ThumbnailSmPath = $"/products/thumbnail/sm/{productNameForImage}_main.webp",
                ThumbnailMdPath = $"/products/thumbnail/md/{productNameForImage}_main.webp",
                Name = $"{productNameForImage}_main",
                AltText = product.Name,
                OriginalWidth = 1920,
                OriginalHeight = 1080,
                FileSizeBytes = 150000,
                IsMainImage = true,
                SortOrder = 0,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        await context.Set<ProductImage>().AddRangeAsync(images);
        await context.SaveChangesAsync();
    }

    public static async Task SeedUsers(B2BDbContext context, UserManager<B2BUser> userManager)
    {
        if (await userManager.Users.AnyAsync())
            return;

        // Demo kullanıcıları oluştur
        var users = new List<(B2BUser User, string Password, UserProfile Profile)>
        {
            (
                new B2BUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmailConfirmed = true,
                    PhoneNumber = "+************",
                    PhoneNumberConfirmed = true
                },
                "Demo123!",
                new UserProfile("Demo", "Kullanıcı")
            ),
            (
                new B2BUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmailConfirmed = true,
                    PhoneNumber = "+************",
                    PhoneNumberConfirmed = true
                },
                "Admin123!",
                new UserProfile("Admin", "Kullanıcı")
            ),
            (
                new B2BUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmailConfirmed = true,
                    PhoneNumber = "+************",
                    PhoneNumberConfirmed = true
                },
                "Dealer123!",
                new UserProfile("Bayi", "Temsilcisi")
            ),
            (
                new B2BUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmailConfirmed = true,
                    PhoneNumber = "+************",
                    PhoneNumberConfirmed = true
                },
                "Developer123!",
                new UserProfile("Developer", "Kullanıcı")
            )
        };

        foreach (var (user, password, profile) in users)
        {
            // Kullanıcı oluştur
            var result = await userManager.CreateAsync(user, password);

            if (result.Succeeded)
            {
                // Kullanıcı profili oluştur
                profile.UserId = user.Id;
                await context.UserProfiles.AddAsync(profile);


                // Admin kullanıcısına Admin rolü ata
                if (user.Email == "<EMAIL>")
                {
                    await userManager.AddToRoleAsync(user, "Admin");
                }
                // Demo kullanıcısına Viewer rolü ata
                else if (user.Email == "<EMAIL>")
                {
                    await userManager.AddToRoleAsync(user, "Ürün Yönetimi");
                }
                // Bayi kullanıcısına Manager rolü ata
                else if (user.Email == "<EMAIL>")
                {
                    await userManager.AddToRoleAsync(user, "Ürün Yönetimi");
                }
                else if (user.Email == "<EMAIL>")
                {
                    await userManager.AddToRoleAsync(user, "Developer");
                }
            }
        }

        await context.SaveChangesAsync();
    }

    public static async Task SeedCustomers(B2BDbContext context)
    {
        if (await context.Customers.AnyAsync())
            return;

        var customers = new List<Customer>
        {
            new Customer
            {
                Id = Guid.CreateVersion7(),
                NameSurname = "Ahmet Yılmaz",
                Password = "Customer123!", // Normalde hash'lenmiş olmalı
                PhoneNumber = "+************",
                Email = "<EMAIL>",
                TaxOrIdentityNumber = "12345678901",
                TaxOffice = "İstanbul",
                CartId = Guid.CreateVersion7(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Customer
            {
                Id = Guid.CreateVersion7(),
                NameSurname = "Fatma Demir",
                Password = "Customer123!", // Normalde hash'lenmiş olmalı
                PhoneNumber = "+************",
                Email = "<EMAIL>",
                TaxOrIdentityNumber = "98765432109",
                TaxOffice = "Ankara",
                CartId = Guid.CreateVersion7(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Customer
            {
                Id = Guid.CreateVersion7(),
                NameSurname = "Mehmet Kaya",
                Password = "Customer123!", // Normalde hash'lenmiş olmalı
                PhoneNumber = "+************",
                Email = "<EMAIL>",
                TaxOrIdentityNumber = "11223344556",
                TaxOffice = "İzmir",
                CartId = Guid.CreateVersion7(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.Customers.AddRangeAsync(customers);
        await context.SaveChangesAsync();

        // Customer'lara Cart oluştur
        var carts = new List<Cart>();
        foreach (var customer in customers)
        {
            carts.Add(new Cart
            {
                Id = customer.CartId,
                CustomerId = customer.Id,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        await context.Carts.AddRangeAsync(carts);
        await context.SaveChangesAsync();

        // Customer'lara adres ekle
        var customerAddresses = new List<Address>();
        var customers_db = await context.Customers.ToListAsync();

        foreach (var customer in customers_db)
        {
            // Fatura adresi
            customerAddresses.Add(new Address
            {
                Id = Guid.CreateVersion7(),
                CustomerId = customer.Id,
                AddressType = AddressType.Billing,
                Name = $"{customer.NameSurname} - Fatura Adresi",
                Line1 = "Örnek Mahallesi, Örnek Caddesi No:123",
                Line2 = "Daire 5",
                City = "İstanbul",
                District = "Kadıköy",
                Country = "Türkiye",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });

            // Teslimat adresi
            customerAddresses.Add(new Address
            {
                Id = Guid.CreateVersion7(),
                CustomerId = customer.Id,
                AddressType = AddressType.Shipping,
                Name = $"{customer.NameSurname} - Teslimat Adresi",
                Line1 = "Başka Mahallesi, Başka Caddesi No:456",
                Line2 = "Kat 3",
                City = "İstanbul",
                District = "Beşiktaş",
                Country = "Türkiye",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        await context.Addresses.AddRangeAsync(customerAddresses);
        await context.SaveChangesAsync();
    }

    public static async Task SeedOrders(B2BDbContext context)
    {
        if (await context.Orders.AnyAsync())
            return;

        // İlk customer ve ürünleri al
        var customer = await context.Customers.FirstOrDefaultAsync();
        var customerAddress = await context.Addresses
            .FirstOrDefaultAsync(a => a.CustomerId == customer!.Id && a.AddressType == AddressType.Shipping);
        var products = await context.Products.Take(2).ToListAsync();

        if (customer == null || products.Count < 2)
            return;

        var orderId = Guid.CreateVersion7();
        var orderNumber = $"ORD{DateTime.UtcNow:yyyyMMdd}001";

        // Sipariş oluştur
        var order = new Order
        {
            Id = orderId,
            CustomerId = customer.Id,
            AddressId = customerAddress?.Id,
            OrderNumber = orderNumber,
            Status = OrderStatus.Processing,
            TotalAmount = 59999.98m, // 2 ürünün toplam fiyatı + kargo + vergi
            DiscountAmount = 1000.00m,
            ShippingAmount = 50.00m,
            TaxAmount = 10799.99m,
            Notes = "Test siparişi - Hızlı teslimat talep edildi",
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await context.Orders.AddAsync(order);
        await context.SaveChangesAsync();

        // Sipariş ürünleri oluştur
        var orderRows = new List<OrderRow>
        {
            new OrderRow
            {
                Id = Guid.CreateVersion7(),
                OrderId = orderId,
                ProductId = products[0].Id,
                Quantity = 1,
                Price = products[0].Price ?? 0,
                DiscountedPrice = (products[0].Price ?? 0) * 0.95m, // %5 indirim
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new OrderRow
            {
                Id = Guid.CreateVersion7(),
                OrderId = orderId,
                ProductId = products[1].Id,
                Quantity = 1,
                Price = products[1].Price ?? 0,
                DiscountedPrice = (products[1].Price ?? 0) * 0.90m, // %10 indirim
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.OrderRows.AddRangeAsync(orderRows);
        await context.SaveChangesAsync();

        // Ödeme bilgisi oluştur
        var payment = new Payment
        {
            Id = Guid.CreateVersion7(),
            OrderId = orderId,
            Amount = order.TotalAmount,
            Description = "Kredi kartı ile ödeme",
            PaymentMethod = "Kredi Kartı",
            Status = PaymentStatus.Completed,
            PaymentResponse = "SUCCESS",
            PaymentResponseCode = "00",
            PaymentResponseMessage = "İşlem başarılı",
            PaymentResponseTransactionId = "TXN" + DateTime.UtcNow.Ticks,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await context.Payments.AddAsync(payment);
        await context.SaveChangesAsync();

        // İlk kargo firmasını al (Yurtiçi Kargo)
        var carrier = await context.ShippingCarriers.FirstOrDefaultAsync(c => c.ShortCode == "yurtici");
        if (carrier == null)
        {
            // Eğer kargo firması yoksa, varsayılan bir tane oluştur
            carrier = new ShippingCarrier
            {
                Id = Guid.CreateVersion7(),
                Name = "Yurtiçi Kargo",
                ShortCode = "yurtici",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            await context.ShippingCarriers.AddAsync(carrier);
            await context.SaveChangesAsync();
        }

        // Kargo bilgisi oluştur
        var shipment = new Shipment
        {
            Id = Guid.CreateVersion7(),
            OrderId = orderId,
            TrackingNumber = "TRK" + DateTime.UtcNow.Ticks.ToString().Substring(0, 10),
            CarrierId = carrier.Id,
            Status = ShipmentStatus.Shipped,
            ShippedAt = DateTime.UtcNow.AddHours(-2),
            Notes = "Hızlı kargo ile gönderildi",
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await context.Shipments.AddAsync(shipment);
        await context.SaveChangesAsync();
    }
}
