using Application.Contracts.Repositories;
using Core.Entities;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class PaymentProviderRepository : IPaymentProviderRepository
{
    private readonly B2BDbContext _context;

    public PaymentProviderRepository(B2BDbContext context)
    {
        _context = context;
    }

    public async Task<PaymentProvider?> GetByShortCodeAsync(string shortCode)
    {
        return await _context.Set<PaymentProvider>().FirstOrDefaultAsync(x => x.ShortCode == shortCode && !x.IsDeleted);
    }

    public async Task<PaymentProvider?> GetByIdAsync(Guid id)
    {
        return await _context.Set<PaymentProvider>().FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
    }
}

