// namespace Payments.Iyzico;
//
// public class IyzicoPaymentService
// {
//     public IyzicoPaymentService()
//     {
//     }
//     
//     public IResult<WebSecurePaymentConfigResponse> GetIyzicoSecureConfig(string installment, string cardNumber, string expMonth, string expYear, string secureNumber, string nameSurname)
//         {
//             var urlHost = _kameyaHelper.HttpContext.Request.Scheme + "://" + _kameyaHelper.HttpContext.Request.Host.Value;
//
//             string CallbackUrl = urlHost + "/Payment/IyzicoCallback";
//             string orderId = Guid.NewGuid().ToString().ToLower();
//
//             CreatePaymentRequest request = new CreatePaymentRequest();
//
//             request.Locale         = Locale.TR.ToString();
//             request.ConversationId = orderId;
//             var amount             = _salesHelper.GetPurchasePrice((_webCartManager.GetCart().Model.Total-_webCartManager.GetCart().Model.Discount)).ToString().Replace(",", ".").Replace("0000", "00");
//             var CargoLimit         = _salesHelper.GetCargoLimit();
//             var CargoPrice         = _salesHelper.GetPurchasePrice(_salesHelper.GetCargoPrice());
//             var decimalAmount      = System.Threading.Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName == "tr" ? decimal.Parse(amount.Replace(".", ",")) : decimal.Parse(amount);
//             if (CargoLimit > decimalAmount)
//             {
//                 amount = (decimalAmount + CargoPrice).ToString().Replace(",", ".");
//             }
//             request.Price          = amount;
//             request.PaidPrice      = amount;
//             request.Currency       = Currency.TRY.ToString();
//             request.Installment    = !string.IsNullOrEmpty(installment) ? int.Parse(installment) : 1;
//             request.BasketId       = orderId.ToUpper().ToString().Substring(0, 8);
//             request.PaymentChannel = PaymentChannel.WEB.ToString();
//             request.PaymentGroup   = PaymentGroup.PRODUCT.ToString();
//             request.CallbackUrl    = CallbackUrl;
//
//
//             PaymentCard paymentCard    = new PaymentCard();
//             paymentCard.CardHolderName = nameSurname;
//             paymentCard.CardNumber     = cardNumber;
//             paymentCard.ExpireMonth    = expMonth;
//             paymentCard.ExpireYear     = expYear;
//             paymentCard.Cvc            = secureNumber;
//             //paymentCard.CardNumber   = "****************";
//             //paymentCard.ExpireMonth  = "12";
//             //paymentCard.ExpireYear   = "2030";
//             //paymentCard.Cvc          = "123";
//             paymentCard.RegisterCard   = 0;
//             request.PaymentCard        = paymentCard;
//
//
//             Buyer buyer = new Buyer();
//
//             if (_kameyaHelper.IsUserLogin)
//             {
//                 buyer.Id                  = _kameyaHelper.User.UserId;
//                 buyer.IdentityNumber      = "111111111111111";
//                 buyer.Name                = _kameyaHelper.User.FirstName;
//                 buyer.Surname             = _kameyaHelper.User.SurName;
//                 buyer.Email               = _kameyaHelper.User.Email;
//                 buyer.RegistrationAddress = "-";
//
//
//                 Address shippingAddress     = new Address();
//                 shippingAddress.ContactName = _kameyaHelper.CurrentUserAddress.Shipment.Name + " " + _kameyaHelper.CurrentUserAddress.Shipment.LastName;
//                 shippingAddress.City        = _kameyaHelper.CurrentUserAddress.Shipment.City;
//                 shippingAddress.Country     = _kameyaHelper.CurrentUserAddress.Shipment.Country;
//                 shippingAddress.Description = _kameyaHelper.CurrentUserAddress.Shipment.Address;
//                 shippingAddress.ZipCode     = "33333";
//                 request.ShippingAddress     = shippingAddress;
//
//                 Address billingAddress     = new Address();
//                 billingAddress.ContactName = _kameyaHelper.CurrentUserAddress.Billing.Name + " " + _kameyaHelper.CurrentUserAddress.Billing.LastName;
//                 billingAddress.City        = _kameyaHelper.CurrentUserAddress.Billing.City;
//                 billingAddress.Country     = _kameyaHelper.CurrentUserAddress.Billing.Country;
//                 billingAddress.Description = _kameyaHelper.CurrentUserAddress.Billing.Address;
//                 billingAddress.ZipCode     = "33333";
//                 request.BillingAddress     = billingAddress;
//
//                 buyer.City    = _kameyaHelper.CurrentUserAddress.Shipment.City;
//                 buyer.Country = _kameyaHelper.CurrentUserAddress.Shipment.Country;
//                 buyer.ZipCode = "33333";
//             }
//             else
//             {
//                 buyer.Id                  = _kameyaHelper.GetCookieSessionId;
//                 buyer.IdentityNumber      = "111111111111111";
//                 buyer.Name                = _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.FirstName;
//                 buyer.Surname             = _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.LastName;
//                 buyer.Email               = _kameyaHelper.WebCartNonMembershipUserHelperModel.User.Mail;
//                 buyer.RegistrationAddress = "-";
//
//
//                 Address shippingAddress     = new Address();
//                 shippingAddress.ContactName = _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.Name + " " + _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.LastName;
//                 shippingAddress.City        = _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.City;
//                 shippingAddress.Country     = _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.Country;
//                 shippingAddress.Description = _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.Address;
//                 shippingAddress.ZipCode     = "33333";
//                 request.ShippingAddress     = shippingAddress;
//
//                 Address billingAddress     = new Address();
//                 billingAddress.ContactName = _kameyaHelper.WebCartNonMembershipUserHelperModel.Billing.Name + " " + _kameyaHelper.WebCartNonMembershipUserHelperModel.Billing.LastName;
//                 billingAddress.City        = _kameyaHelper.WebCartNonMembershipUserHelperModel.Billing.City;
//                 billingAddress.Country     = _kameyaHelper.WebCartNonMembershipUserHelperModel.Billing.Country;
//                 billingAddress.Description = _kameyaHelper.WebCartNonMembershipUserHelperModel.Billing.Address;
//                 billingAddress.ZipCode     = "33333";
//                 request.BillingAddress     = billingAddress;
//
//
//                 buyer.City    = _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.City;
//                 buyer.Country = _kameyaHelper.WebCartNonMembershipUserHelperModel.Shipment.Country;
//                 buyer.ZipCode = "33333";
//             }
//
//             buyer.Ip = _kameyaHelper.HttpContext.Connection.RemoteIpAddress.ToString() == "::1" ? "127.0.0.1" : _kameyaHelper.HttpContext.Connection.RemoteIpAddress.ToString();
//             request.Buyer = buyer;
//
//
//             List<BasketItem> basketItems = new List<BasketItem>();
//
//             var carts = _webCartManager.GetCart().Model.WebCartProducts;
//             foreach (var item in carts)
//             {
//                 BasketItem firstBasketItem = new BasketItem();
//                 firstBasketItem.Id = item.ProductId;
//                 firstBasketItem.Name = item.Name;
//                 firstBasketItem.Category1 = "Category";
//                 firstBasketItem.ItemType = BasketItemType.PHYSICAL.ToString();
//                 firstBasketItem.Price = (_salesHelper.GetPurchasePrice(item.Price) * item.Quantity).ToString().Replace(",", ".");
//                 basketItems.Add(firstBasketItem);
//             }
//
//             if (CargoLimit >= decimalAmount)
//             {
//                 basketItems.First().Price = System.Threading.Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName == "tr" ? (decimal.Parse(basketItems.First().Price.Replace(".", ",")) + CargoPrice).ToString().Replace(",",".") : (decimal.Parse(basketItems.First().Price) + CargoPrice).ToString().Replace(",", ".");
//             }
//             if(_webCartManager.GetCart().Model.Discount > 0.00m)
//             {
//                 basketItems.First().Price = System.Threading.Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName == "tr" ? (decimal.Parse(basketItems.First().Price.Replace(".", ",")) - _webCartManager.GetCart().Model.Discount).ToString().Replace(",", ".") : (decimal.Parse(basketItems.First().Price) - _webCartManager.GetCart().Model.Discount).ToString().Replace(",", ".");
//             }
//             request.BasketItems = basketItems;
//
//
//             var options = new Options();
//             options.ApiKey = _kameyaConfig.ServicePoints.Payments.Iyzico_ApiKey;
//             options.SecretKey = _kameyaConfig.ServicePoints.Payments.Iyzico_SecretKey;
//             options.BaseUrl = _kameyaConfig.ServicePoints.Payments.Iyzico_BaseUrl;
//             ThreedsInitialize threedsInitialize = ThreedsInitialize.Create(request, options);
//            
//             Dictionary<string, object> paymentParameters = new Dictionary<string, object>();
//             WebSecurePaymentConfigResponse RetVal = new WebSecurePaymentConfigResponse
//             {
//                 IsSuccess = threedsInitialize.Status == "success" ? true : false,
//                 Message = threedsInitialize.ErrorMessage,
//                 HtmlForm = threedsInitialize.HtmlContent,
//                 EntegrationName = "Iyzico",
//                 PaymentParameters = paymentParameters
//             };
//
//             if (RetVal.IsSuccess)
//             {
//                 CreatePaymentTemp(new PaymentTemp()
//                 {
//                     Amount = amount,
//                     PayId = threedsInitialize.ConversationId,
//                     Installment = !string.IsNullOrEmpty(installment) ? int.Parse(installment) : 1,
//                     BankType = BankType.Iyzico,
//                     CardNumber = cardNumber,
//                     Cvv = secureNumber,
//                     NameSurname = nameSurname,
//                     OrderId = orderId,
//                     Email = buyer.Email,
//                     UserId = buyer.Id,
//                     PaymentCart = _webCartManager.GetCart().Model
//
//             });
//             }
//             return new Result<WebSecurePaymentConfigResponse>(RetVal, RetVal.Message, RetVal.IsSuccess);
//         }
//         public WebPaymentInformationResponse GetPaymentIyzico(Dictionary<string, string> parameters)
//         {
//             WebPaymentInformationResponse Retval = new WebPaymentInformationResponse();
//             Iyzipay.Model.ThreedsPayment threedsPayment = new Iyzipay.Model.ThreedsPayment();
//             var paymentTemp = GetPaymentTemp(payid: parameters["conversationId"]);
//             string backStatus = parameters["status"];
//             if (backStatus == "success")
//             {
//                 CreateThreedsPaymentRequest request = new CreateThreedsPaymentRequest();
//                 request.Locale = Iyzipay.Model.Locale.TR.ToString();
//                 request.ConversationId = parameters["conversationId"];
//                 request.PaymentId = parameters["paymentId"];
//                 request.ConversationData = parameters["conversationData"];
//
//                 var options = new Options();
//
//                 options.ApiKey = _kameyaConfig.ServicePoints.Payments.Iyzico_ApiKey;
//                 options.SecretKey = _kameyaConfig.ServicePoints.Payments.Iyzico_SecretKey;
//                 options.BaseUrl = _kameyaConfig.ServicePoints.Payments.Iyzico_BaseUrl;
//                 threedsPayment = Iyzipay.Model.ThreedsPayment.Create(request, options);
//
//                 if (threedsPayment.Status == "success")
//                 {
//                     Retval.IsSuccess = true;
//                     Retval.ErrorCode = string.Empty;
//                     Retval.ErrorMessage = string.Empty;
//                 }
//                 else // işlem sonucu başarısız ise, istek sonucu dönen hata mesajı yükleniyor
//                 {
//                     Retval.IsSuccess = false;
//                     Retval.ErrorCode = threedsPayment.ErrorCode;
//                     Retval.ErrorMessage = threedsPayment.ErrorMessage.Trim();
//                 }
//             }
//             else
//             {
//                 Retval.IsSuccess = false;
//                 //threedsPayment.Installment = paymentTemp.Installment;
//
//                 string status = parameters["mdStatus"];
//                 Retval.ErrorCode = status;
//                 switch (status)
//                 {
//                     case "0":
//                         Retval.ErrorMessage = "3-D Secure imzası geçersiz veya doğrulama";
//                         break;
//                     case "2":
//                         Retval.ErrorMessage = "Kart sahibi veya bankası sisteme kayıtlı değil";
//                         break;
//                     case "3":
//                         Retval.ErrorMessage = "Kartın bankası sisteme kayıtlı değil.";
//                         break;
//                     case "4":
//                         Retval.ErrorMessage = "Doğrulama denemesi, kart sahibi sisteme daha sonra kayıt olmayı seçmiş.";
//                         break;
//                     case "5":
//                         Retval.ErrorMessage = "Doğrulama yapılamıyor";
//                         break;
//                     case "6":
//                         Retval.ErrorMessage = "	3-D Secure hatası";
//                         break;
//                     case "7":
//                         Retval.ErrorMessage = "Sistem hatası";
//                         break;
//                     case "8":
//                         Retval.ErrorMessage = "Bilinmeyen kart no";
//                         break;
//                     default:
//                         Retval.ErrorMessage = "";
//                         break;
//                 }
//             }
//
//             string clientIp = _kameyaHelper.HttpContext.Connection.RemoteIpAddress.ToString() == "::1" ? "127.0.0.1" : _kameyaHelper.HttpContext.Connection.RemoteIpAddress.ToString();
//
//             Retval.OrderPaymentId = parameters["conversationId"];
//             Retval.Amount = System.Threading.Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName == "tr" ? decimal.Parse(paymentTemp.Amount.Replace(".", ",")) : decimal.Parse(paymentTemp.Amount);
//             Retval.BankType = BankType.Iyzico;
//             Retval.ClientIpAddress = clientIp;
//             Retval.Installment = paymentTemp.Installment.ToString();
//             Retval.MaskedCardNumber = paymentTemp.CardNumber.Substring(0, 6) + "******" + paymentTemp.CardNumber.Substring(12, 4);
//             Retval.NameSurnameOnCard = paymentTemp.NameSurname;
//             Retval.PaymentDate = DateTime.Now;
//             Retval.UserId = paymentTemp.UserId;
//             Retval.Email = paymentTemp.Email;
//             Retval.IsNonMembershipOrder = !_kameyaHelper.IsUserLogin;
//             Retval.PaymentId = _logManager.AddPaymentLog(Retval, paymentTemp.Amount);
//             Retval.PaymentCart = paymentTemp.PaymentCart;
//             DeletePaymentTemp(payid: parameters["conversationId"]);
//             return Retval;
//         }
//         #endregion
//
// }