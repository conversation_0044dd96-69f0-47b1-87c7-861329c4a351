using Payments.Abstractions;

namespace Payments.Iyzico;

/// <summary>
/// İyzico ödeme tanımı
/// </summary>
public class IyzicoDefinition : PaymentDefinition
{
    public override string Name => "Iyzico";
    public override string ShortCode => "IYZICO";
    public override string Description => "İyzico ile kolay ve güvenli ödeme";
    public override bool RequiresApiKey => true;
    public override bool RequiresApiUrl => true;
    public override string? LogoUrl => "/images/payments/iyzico-logo.png";

    public override Dictionary<string, string> DefaultSettings => new()
    {
        { "BaseUrl", "https://api.iyzipay.com" },
        { "ApiKey", "" },
        { "SecretKey", "" },
        { "TestMode", "true" },
        { "Timeout", "30" }
    };
}
