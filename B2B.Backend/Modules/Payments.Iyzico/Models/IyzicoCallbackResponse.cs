namespace Payments.Iyzico.Models;
/// <summary>
/// CF Sorgulama adımı yanıtı modelidir.
/// </summary>
public class IyzicoCallbackResponse
{
    public string Status { get; set; } = null!;
    public string Locale { get; set; } = null!;
    public long SystemTime { get; set; }
    public string ConversationId { get; set; } = null!;
    public decimal Price { get; set; }
    public decimal PaidPrice { get; set; }
    public int Installment { get; set; }
    public string PaymentId { get; set; } = null!;
    /// <summary>
    /// -1,0,1 değerleri alır.
    /// -1 ise fraud risk skoru yüksektir ve işlem reddedilir.
    /// 0 ise işlem incelenecek olup, üye işyerinin bilgilendirme beklemesi gerekir.
    /// 1 ise işlem onaylanmıştır.
    /// Üye işyeri sadece 1 olan işlemleri kargoya vermelidir.
    /// </summary>
    /// <value></value>
    public int FraudStatus { get; set; }
    public decimal MerchantCommissionRate { get; set; }
    public decimal MerchantCommissionRateAmount { get; set; }
    public decimal IyziCommissionRateAmount { get; set; }
    public decimal IyziCommissionFee { get; set; }
    public string CardType { get; set; } = null!;
    public string CardAssociation { get; set; } = null!;
    public string CardFamily { get; set; } = null!;
    public string BinNumber { get; set; } = null!;
    public string LastFourDigits { get; set; } = null!;
    public string BasketId { get; set; } = null!;
    public string Currency { get; set; } = null!;
    public List<ItemTransaction> ItemTransactions { get; set; } = null!;
    public string AuthCode { get; set; } = null!;
    public string Phase { get; set; } = null!;
    public int MdStatus { get; set; }
    public string HostReference { get; set; } = null!;
    public string Token { get; set; } = null!;
    public string CallbackUrl { get; set; } = null!;
    public string PaymentStatus { get; set; } = null!;

}

public class ItemTransaction
{
    public string ItemId { get; set; } = null!;
    public string PaymentTransactionId { get; set; } = null!;
    public int TransactionStatus { get; set; }
    public decimal Price { get; set; }
    public decimal PaidPrice { get; set; }
    public decimal MerchantCommissionRate { get; set; }
    public decimal MerchantCommissionRateAmount { get; set; }
    public decimal IyziCommissionRateAmount { get; set; }
    public decimal IyziCommissionFee { get; set; }
    public decimal BlockageRate { get; set; }
    public decimal BlockageRateAmountMerchant { get; set; }
    public decimal BlockageRateAmountSubMerchant { get; set; }
    public string BlockageResolvedDate { get; set; } = null!;
    public decimal SubMerchantPrice { get; set; }
    public decimal SubMerchantPayoutRate { get; set; }
    public decimal SubMerchantPayoutAmount { get; set; }
    public decimal MerchantPayoutAmount { get; set; }
    public ConvertedPayout ConvertedPayout { get; set; } = null!;
}

public class ConvertedPayout
{
    public decimal PaidPrice { get; set; }
    public decimal IyziCommissionRateAmount { get; set; }
    public decimal IyziCommissionFee { get; set; }
    public decimal BlockageRateAmountMerchant { get; set; }
    public decimal BlockageRateAmountSubMerchant { get; set; }
    public decimal MerchantPayoutAmount { get; set; }
    public decimal SubMerchantPayoutAmount { get; set; }
    public decimal IyziConversionRate { get; set; }
    public decimal IyziConversionRateAmount { get; set; }
    public string Currency { get; set; } = null!;
}