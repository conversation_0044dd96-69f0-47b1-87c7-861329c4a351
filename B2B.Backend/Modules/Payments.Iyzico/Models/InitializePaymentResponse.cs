namespace Payments.Iyzico.Models;
/// <summary>
/// CF Başlatma için yanıt modelidir.
/// </summary>
public class InitializePaymentResponse
{
    /// <summary>
    /// Checkout formun açılması için gereken HTML kod.
    /// </summary>
    /// <value></value>
    public string CheckoutFormContent { get; set; } = null!;
    /// <summary>
    /// iyzico ortak ödeme sayfasına erişimi için gereken URL.
    /// </summary>
    /// <value></value>
    public string PaymentPageUrl { get; set; } = null!;
    /// <summary>
    /// Checkout form için oluşturulan tekil değer. Her istek için özel üretilir ve işyerine dönülür. 
    /// Ödemenin sonucunu öğrenmek için zorunlu bir alandır.
    /// </summary>
    /// <value></value>
    public string Token { get; set; } = null!;
    /// <summary>
    /// Checkout form için üretilmiş olan token değerinin geçerlilik süresi.
    /// </summary>
    /// <value></value>
    public int TokenExpireTime { get; set; }
    /// <summary>
    /// Yapılan isteğin sonucunu bildirir. İşlem başarılı ise success, hatalı ise failure döner.
    /// </summary>
    /// <value></value>
    public string Status { get; set; } = null!;
    /// <summary>
    /// İşlem hatalıysa, bu hataya dair belirtilen koddur.
    /// </summary>
    /// <value></value>
    public string? ErrorCode { get; set; }
    /// <summary>
    /// İşlem hatalıysa, bu hataya dair belirtilen mesajdır, locale parametresine göre dil desteği sunar.
    /// </summary>
    /// <value></value>
    public string? ErrorMessage { get; set; }
    /// <summary>
    /// İşlem hatalıysa, bu hataya dair belirtilen gruptur.
    /// </summary>
    /// <value></value>
    public string? ErrorGroup { get; set; }
    /// <summary>
    /// İstekte belirtilen locale değeri geri dönülür, varsayılan değeridir.
    /// </summary>
    /// <value>tr,en</value>
    public string Locale { get; set; } = null!;
    /// <summary>
    /// Dönen sonucun o anki unix timestamp değeridir.
    /// </summary>
    /// <value></value>
    public long SystemTime { get; set; }
    /// <summary>
    /// İstek esnasında gönderilmişse, sonuçta aynen geri iletilir.
    /// </summary>
    /// <value></value>
    public string? ConversationId { get; set; }
}