namespace Payments.Iyzico.Models;
/// <summary>
/// CF Başlatma için istek modelidir.
/// </summary>
/// 
public class InitializePaymentRequest
{
    /// <summary>
    /// Lokasyon
    /// </summary>
    /// <value>en,tr</value>
    public string Locale { get; set; } = null!;
    public string ConversationId { get; set; } = null!;
    public decimal Price { get; set; }
    public string BasketId { get; set; } = null!;
    public string PaymentGroup { get; set; } = null!;
    public Buyer Buyer { get; set; } = new Buyer();
    public ShippingAddress ShippingAddress { get; set; } = new ShippingAddress();
    public BillingAddress BillingAddress { get; set; } = new BillingAddress();
    public List<BasketItem> BasketItems { get; set; } = new List<BasketItem>();
    public string CallbackUrl { get; set; } = null!;
    public string Currency { get; set; } = null!;
    public decimal PaidPrice { get; set; }
    public string PaymentSource { get; set; } = null!;
    public List<int> EnabledInstallments { get; set; } = new List<int>(){1};

}

public class Buyer
{
    public string Id { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string Surname { get; set; } = null!;
    public string IdentityNumber { get; set; } = null!;
    public string Email { get; set; } = null!;
    public string GsmNumber { get; set; } = null!;
    public string RegistrationAddress { get; set; } = null!;
    public string City { get; set; } = null!;
    public string Country { get; set; } = null!;
    public string Ip { get; set; } = null!;
}

public class ShippingAddress
{
    public string Address { get; set; } = null!;
    public string ContactName { get; set; } = null!;
    public string City { get; set; } = null!;
    public string Country { get; set; } = null!;
}

public class BillingAddress
{
    public string Address { get; set; } = null!;
    public string ContactName { get; set; } = null!;
    public string City { get; set; } = null!;
    public string Country { get; set; } = null!;
}

public class BasketItem
{
    public string Id { get; set; } = null!;
    public decimal Price { get; set; }
    public string Name { get; set; } = null!;
    public string Category1 { get; set; } = null!;
    public string ItemType { get; set; } = null!;
}