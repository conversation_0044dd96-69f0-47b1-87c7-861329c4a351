using System.Reflection;
using System.IO;
using Microsoft.Extensions.Logging;
using Payments.Abstractions;

namespace Payments.Implementation;

public class PaymentServiceFactory : IPaymentServiceFactory
{
    private readonly ILogger<PaymentServiceFactory> _logger;
    private readonly Dictionary<string, Type> _providerTypes = new(StringComparer.OrdinalIgnoreCase);
    private readonly Dictionary<string, PaymentDefinition> _definitions = new(StringComparer.OrdinalIgnoreCase);

    public PaymentServiceFactory(ILogger<PaymentServiceFactory> logger)
    {
        _logger = logger;
        DiscoverImplementations();
    }

    public IPaymentProvider? GetProvider(string shortCode, Dictionary<string, string>? settings = null)
    {
        if (!_providerTypes.TryGetValue(shortCode, out var type))
            return null;

        try
        {
            var ctor = type.GetConstructors().FirstOrDefault(c => c.GetParameters().Any(p => p.ParameterType == typeof(Dictionary<string, string>)));
            if (ctor != null)
                return (IPaymentProvider)Activator.CreateInstance(type, settings ?? new Dictionary<string, string>())!;

            return (IPaymentProvider)Activator.CreateInstance(type)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment provider for {ShortCode}", shortCode);
            return null;
        }
    }

    public PaymentDefinition? GetDefinition(string shortCode)
    {
        _definitions.TryGetValue(shortCode, out var def);
        return def;
    }

    public IEnumerable<PaymentDefinition> GetAvailableDefinitions() => _definitions.Values;

    public void RefreshImplementations()
    {
        _providerTypes.Clear();
        _definitions.Clear();
        DiscoverImplementations();
    }

    private void DiscoverImplementations()
    {
        try
        {
            // Ensure Payments.* assemblies are loaded (similar to Shipping approach)
            TryLoadPaymentAssemblies();

            var assemblies = AppDomain.CurrentDomain.GetAssemblies().Where(a => !a.IsDynamic && a.FullName != null).ToList();
            foreach (var assembly in assemblies)
            {
                try
                {
                    var providers = assembly.GetTypes().Where(t => t.IsClass && !t.IsAbstract && typeof(IPaymentProvider).IsAssignableFrom(t)).ToList();
                    foreach (var prov in providers)
                    {
                        try
                        {
                            // Provider instance istenmiyor; Definition karşılığını bulmak yeterli
                            var defType = assembly.GetTypes().FirstOrDefault(t => t.IsClass && !t.IsAbstract && typeof(PaymentDefinition).IsAssignableFrom(t));
                            if (defType != null)
                            {
                                var def = (PaymentDefinition)Activator.CreateInstance(defType)!;
                                _definitions[def.ShortCode] = def;
                                _providerTypes[def.ShortCode] = prov;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to process provider {Type}", prov.Name);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to scan assembly: {Assembly}", assembly.FullName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during payment provider discovery");
        }
    }

    private void TryLoadPaymentAssemblies()
    {
        try
        {
            var baseDir = AppContext.BaseDirectory;
            var dlls = Directory.GetFiles(baseDir, "Payments.*.dll", SearchOption.TopDirectoryOnly);
            foreach (var dll in dlls)
            {
                try
                {
                    var asmName = AssemblyName.GetAssemblyName(dll);
                    if (AppDomain.CurrentDomain.GetAssemblies().All(a => a.GetName().Name != asmName.Name))
                    {
                        Assembly.LoadFrom(dll);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load payment assembly: {Dll}", dll);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to enumerate payment assemblies from base directory");
        }
    }
}

