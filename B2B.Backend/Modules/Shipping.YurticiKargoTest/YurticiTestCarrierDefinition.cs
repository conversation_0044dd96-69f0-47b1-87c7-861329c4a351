using Shipping.Abstraction;

namespace Shipping.YurticiKargoTest;

/// <summary>
/// Yurtiçi Kargo test ortamı firması tanımı
/// </summary>
public class YurticiTestCarrierDefinition : ShippingCarrierDefinition
{
    public override string Name => "Yurtiçi Kargo (Test)";
    
    public override string ShortCode => "YURTICITEST";
    
    public override string Description => "Yurtiçi Kargo test ortamı entegrasyonu - Test API'si ile güvenli test işlemleri. Gerçek kargo oluşturmaz, sadece test amaçlıdır.";
    
    public override bool RequiresApiKey => false; // Test modunda sabit kimlik bilgileri kullanılır
    
    public override bool RequiresApiUrl => true;
    
    public override string? LogoUrl => "/images/carriers/yurtici-test-logo.png";
    
    public override ShippingFeatures SupportedFeatures =>
        ShippingFeatures.CreateShipment |
        ShippingFeatures.TrackShipment |
        ShippingFeatures.CancelShipment |
        ShippingFeatures.GetShippingCost;

    public override Dictionary<string, string> DefaultSettings => new()
    {
        { "ApiUrl", "https://api.yurtici.com.tr" },
        { "WsUserName", "YKTEST" }, // Sabit test kullanıcı adı
        { "WsPassword", "YK" }, // Sabit test şifresi
        { "WsLanguage", "TR" },
        { "TestMode", "true" }, // Her zaman test modu
        { "Timeout", "30" },
        { "MaxRetries", "3" },
        { "DefaultServiceType", "Standard" },
        { "InsuranceEnabled", "false" },
        { "SmsNotification", "true" },
        { "EmailNotification", "true" },
        { "AutoCreateShipment", "false" },
        { "RequirePhoneValidation", "true" },
        { "MinWeight", "0.1" },
        { "MaxWeight", "30.0" },
        { "SupportedCountries", "TR" },
        { "BusinessDaysOnly", "true" },
        { "CutoffTime", "17:00" },
        { "IsTestEnvironment", "true" }, // Test ortamı işareti
        { "TestDataPrefix", "TEST_" } // Test verilerinde prefix
    };
}
