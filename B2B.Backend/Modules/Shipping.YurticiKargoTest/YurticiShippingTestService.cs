using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Shipping.Abstraction;
using Shipping.YurticiKargoTest.Models;
using Shipping.YurticiKargoTest.KOPSWebServices;

namespace Shipping.YurticiKargoTest;

/// <summary>
/// Yurtiçi Kargo test ortamı için özel service implementasyonu
/// KOPSWebServicesTest namespace'ini kullanır ve IShippingService interface'ini implement eder
/// </summary>
public class YurticiShippingTestService : IShippingService
{
    private readonly ILogger<YurticiShippingTestService>? _logger;
    private readonly IConfiguration _configuration;
    private readonly YurticiTestCarrierDefinition _definition;
    private readonly YurticiApiSettings _apiSettings;

    public YurticiShippingTestService(
        ILogger<YurticiShippingTestService>? logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _definition = new YurticiTestCarrierDefinition();
        _apiSettings = LoadTestApiSettings();
    }

    public ShippingCarrierDefinition Definition => _definition;

    /// <summary>
    /// Test ortamı için API ayarlarını yükle (sabit değerler)
    /// </summary>
    private YurticiApiSettings LoadTestApiSettings()
    {
        return new YurticiApiSettings
        {
            WsUserName = "YKTEST", // Sabit test kullanıcı adı
            WsPassword = "YK", // Sabit test şifresi
            WsLanguage = "TR",
            ApiUrl = _configuration["YURTICI_API_URL"] ??
                    _configuration["Shipping:Yurtici:ApiUrl"] ??
                    "https://api.yurtici.com.tr",
            TestMode = true, // Her zaman test modu
            Timeout = int.Parse(_configuration["YURTICI_TIMEOUT"] ??
                               _configuration["Shipping:Yurtici:Timeout"] ?? "30"),
            MaxRetries = int.Parse(_configuration["YURTICI_MAX_RETRIES"] ??
                                  _configuration["Shipping:Yurtici:MaxRetries"] ?? "3")
        };
    }

    public async Task<string> CreateShipmentAsync(ShipmentRequest request)
    {
        _logger?.LogInformation("Creating Yurtiçi Kargo TEST shipment for Order: {OrderId}", request.OrderId);

        try
        {
            // Giriş verilerini doğrula
            ValidateShipmentRequest(request);

            // Yurtiçi Kargo API isteği hazırla
            var yurticiRequest = MapToYurticiRequest(request);

            // Test API çağrısı yap
            var response = await CallCreateShipmentTestApiAsync(yurticiRequest);

            if (response.IsSuccess)
            {
                _logger?.LogInformation("Yurtiçi Kargo TEST shipment created successfully. Tracking: {TrackingNumber}", response.CargoKey);
                return response.CargoKey ?? throw new InvalidOperationException("TEST API'den geçerli kargo anahtarı alınamadı");
            }
            else
            {
                var errorMessage = $"Yurtiçi Kargo TEST API Error: {response.ErrorCode} - {response.ErrorMessage}";
                _logger?.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error creating Yurtiçi Kargo TEST shipment for Order: {OrderId}", request.OrderId);
            throw;
        }
    }

    public async Task<ShipmentTrackingInfo> GetTrackingInfoAsync(string trackingNumber)
    {
        _logger?.LogInformation("Getting Yurtiçi Kargo TEST tracking info for: {TrackingNumber}", trackingNumber);

        try
        {
            if (string.IsNullOrWhiteSpace(trackingNumber))
                throw new ArgumentException("Takip numarası boş olamaz", nameof(trackingNumber));

            // Yurtiçi Kargo API sorgulama isteği hazırla
            var queryRequest = new YurticiQueryShipmentRequest
            {
                Key = trackingNumber,
                KeyType = 1, // Kargo numarası
                OnlyTracking = false,
                AddHistoricalData = true
            };

            // Test API çağrısı yap
            var response = await CallQueryShipmentTestApiAsync(queryRequest);

            if (response.IsSuccess)
            {
                var trackingInfo = MapToTrackingInfo(response, trackingNumber);
                _logger?.LogInformation("Yurtiçi Kargo TEST tracking info retrieved successfully for: {TrackingNumber}", trackingNumber);
                return trackingInfo;
            }
            else
            {
                var errorMessage = $"Yurtiçi Kargo TEST API Error: {response.ErrorCode} - {response.ErrorMessage}";
                _logger?.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting Yurtiçi Kargo TEST tracking info for: {TrackingNumber}", trackingNumber);
            throw;
        }
    }

    public async Task<bool> CancelShipmentAsync(string trackingNumber)
    {
        _logger?.LogInformation("Cancelling Yurtiçi Kargo TEST shipment: {TrackingNumber}", trackingNumber);

        try
        {
            if (string.IsNullOrWhiteSpace(trackingNumber))
                throw new ArgumentException("Takip numarası boş olamaz", nameof(trackingNumber));

            cancelShipment request = new cancelShipment
            {
                wsUserName = _apiSettings.WsUserName,
                wsPassword = _apiSettings.WsPassword,
                userLanguage = _apiSettings.WsLanguage,
                cargoKeys = new string[] { trackingNumber }
            };

             using var client = new ShippingOrderDispatcherServicesClient();

            // Test ortamı için kısa timeout ayarları
            if (client.Endpoint.Binding is System.ServiceModel.BasicHttpBinding binding)
            {
                binding.SendTimeout = TimeSpan.FromSeconds(300); // Test için kısa timeout
                binding.ReceiveTimeout = TimeSpan.FromSeconds(300);
                binding.OpenTimeout = TimeSpan.FromSeconds(10);
                binding.CloseTimeout = TimeSpan.FromSeconds(10);
            }

            var response = await client.cancelShipmentAsync(request);
            
            _logger?.LogInformation("Yurtiçi Kargo TEST shipment cancellation simulated: {TrackingNumber}", trackingNumber);
            return true; // Test ortamında her zaman başarılı
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error cancelling Yurtiçi Kargo TEST shipment: {TrackingNumber}", trackingNumber);
            return false;
        }
    }

    public async Task<bool> ValidateSettingsAsync(Dictionary<string, string> settings)
    {
        try
        {
            // Test ortamında ayar doğrulama her zaman başarılı
            _logger?.LogInformation("Validating Yurtiçi Kargo TEST settings - always successful in test mode");
            await Task.Delay(50); // Simüle edilmiş gecikme
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error validating Yurtiçi Kargo TEST settings");
            return false;
        }
    }

    public Task<decimal?> CalculateShippingCostAsync(ShipmentRequest request)
    {
        _logger?.LogInformation("Calculating Yurtiçi Kargo TEST shipping cost for Order: {OrderId}", request.OrderId);

        try
        {
            // Test ortamında sabit maliyet hesaplama
            decimal baseCost = 10.00m; // Test temel ücret
            decimal weightCost = request.Weight * 2.00m; // Test ağırlık başına ücret
            decimal totalCost = baseCost + weightCost;

            // Test minimum ücret kontrolü
            if (totalCost < 8.00m)
                totalCost = 8.00m;

            _logger?.LogInformation("Yurtiçi Kargo TEST shipping cost calculated: {Cost} for Order: {OrderId}", totalCost, request.OrderId);

            return Task.FromResult<decimal?>(totalCost);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calculating Yurtiçi Kargo TEST shipping cost for Order: {OrderId}", request.OrderId);
            return Task.FromResult<decimal?>(null);
        }
    }

    /// <summary>
    /// Test ortamı için kargo oluşturma çağrısı
    /// </summary>
    private async Task<YurticiCreateShipmentResponse> CallCreateShipmentTestApiAsync(YurticiCreateShipmentRequest request)
    {
        _logger?.LogInformation("Using TEST environment for Yurtiçi Kargo createShipment with credentials: {Username}", _apiSettings.WsUserName);

        try
        {
            // Test ortamı için KOPSWebServicesTest client kullan
            using var client = new ShippingOrderDispatcherServicesClient();

            // Test ortamı için kısa timeout ayarları
            if (client.Endpoint.Binding is System.ServiceModel.BasicHttpBinding binding)
            {
                binding.SendTimeout = TimeSpan.FromSeconds(300); // Test için kısa timeout
                binding.ReceiveTimeout = TimeSpan.FromSeconds(300);
                binding.OpenTimeout = TimeSpan.FromSeconds(10);
                binding.CloseTimeout = TimeSpan.FromSeconds(10);
            }

            var createShipmentRequest = new createShipment
            {
                wsUserName = _apiSettings.WsUserName, // "YKTEST"
                wsPassword = _apiSettings.WsPassword, // "YK"
                userLanguage = _apiSettings.WsLanguage, // "TR"
                ShippingOrderVO = new ShippingOrderVO[]
                {
                    new ShippingOrderVO
                    {
                        cargoKey = request.CargoKey,
                        invoiceKey = request.InvoiceKey,
                        receiverCustName = request.ReceiverCustName,
                        receiverAddress = request.ReceiverAddress,
                        receiverPhone1 = request.ReceiverPhone1
                    }
                }
            };

            var response = await client.createShipmentAsync(createShipmentRequest);
            return ParseCreateShipmentResponse(response);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Yurtiçi Kargo createShipment TEST API");
            return new YurticiCreateShipmentResponse
            {
                ErrorCode = "EXCEPTION",
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Test ortamı için kargo sorgulama çağrısı
    /// </summary>
    public async Task<YurticiQueryShipmentResponse> QueryShipmentAsync(YurticiQueryShipmentRequest request)
    {
        _logger?.LogInformation("Using TEST environment for Yurtiçi Kargo queryShipment");

        try
        {
            // Test ortamı için KOPSWebServicesTest client kullan
            using var client = new ShippingOrderDispatcherServicesClient();

            // Test ortamı için kısa timeout ayarları
            if (client.Endpoint.Binding is System.ServiceModel.BasicHttpBinding binding)
            {
                binding.SendTimeout = TimeSpan.FromSeconds(15); // Test için kısa timeout
                binding.ReceiveTimeout = TimeSpan.FromSeconds(15);
                binding.OpenTimeout = TimeSpan.FromSeconds(10);
                binding.CloseTimeout = TimeSpan.FromSeconds(10);
            }

            var queryShipmentRequest = new queryShipment
            {
                wsUserName = _apiSettings.WsUserName, // "YKTEST"
                wsPassword = _apiSettings.WsPassword, // "YK"
                wsLanguage = _apiSettings.WsLanguage, // "TR"
                keyType = request.KeyType,
                keys = new string[] { request.Key },
                onlyTracking = request.OnlyTracking,
                addHistoricalData = request.AddHistoricalData
            };

            var response = await client.queryShipmentAsync(queryShipmentRequest);
            return ParseQueryShipmentResponse(response);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Yurtiçi Kargo queryShipment TEST API");
            return new YurticiQueryShipmentResponse
            {
                ErrorCode = "EXCEPTION",
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Test ortamı API bağlantısını test et
    /// </summary>
    public async Task<bool> TestApiConnectionAsync()
    {
        try
        {
            // Basit bir test sorgusu yap
            var testRequest = new YurticiQueryShipmentRequest
            {
                Key = "TEST123",
                KeyType = 1,
                OnlyTracking = true,
                AddHistoricalData = false
            };

            var response = await QueryShipmentAsync(testRequest);

            // API'ye ulaşabiliyorsak (hata kodu EXCEPTION değilse) bağlantı başarılı
            return response.ErrorCode != "EXCEPTION";
        }
        catch
        {
            return false;
        }
    }

    #region Private Helper Methods

    /// <summary>
    /// Kargo oluşturma isteğini doğrula
    /// </summary>
    private static void ValidateShipmentRequest(ShipmentRequest request)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        if (string.IsNullOrWhiteSpace(request.RecipientName))
            throw new ArgumentException("Alıcı adı boş olamaz", nameof(request.RecipientName));

        if (string.IsNullOrWhiteSpace(request.RecipientPhone))
            throw new ArgumentException("Alıcı telefonu boş olamaz", nameof(request.RecipientPhone));

        if (string.IsNullOrWhiteSpace(request.Address))
            throw new ArgumentException("Adres boş olamaz", nameof(request.Address));

        if (string.IsNullOrWhiteSpace(request.City))
            throw new ArgumentException("Şehir boş olamaz", nameof(request.City));

        if (request.Weight <= 0)
            throw new ArgumentException("Ağırlık 0'dan büyük olmalıdır", nameof(request.Weight));
    }

    /// <summary>
    /// ShipmentRequest'i Yurtiçi API formatına dönüştür (TEST)
    /// </summary>
    private static YurticiCreateShipmentRequest MapToYurticiRequest(ShipmentRequest request)
    {
        // Test için benzersiz kargo anahtarı oluştur
        var cargoKey = GenerateCargoKey();
        var invoiceKey = $"TESTINV{request.OrderId.ToString("N")[..8].ToUpper()}";

        // Telefon numarasını formatla (0 ile başlamalı)
        var phone = request.RecipientPhone.Trim();
        if (!phone.StartsWith("0"))
            phone = "0" + phone;

        return new YurticiCreateShipmentRequest
        {
            CargoKey = cargoKey,
            InvoiceKey = invoiceKey,
            ReceiverCustName = request.RecipientName.Trim(),
            ReceiverAddress = $"{request.Address}, {request.District}/{request.City}".Trim(),
            ReceiverPhone1 = phone,
            ReceiverEmail = request.RecipientEmail,
            DeclaredValue = request.DeclaredValue,
            Weight = request.Weight,
            SpecialInstructions = $"TEST: {request.SpecialInstructions}"
        };
    }

    /// <summary>
    /// Yurtiçi API yanıtını TrackingInfo'ya dönüştür (TEST)
    /// </summary>
    private static ShipmentTrackingInfo MapToTrackingInfo(YurticiQueryShipmentResponse response, string trackingNumber)
    {
        return new ShipmentTrackingInfo
        {
            TrackingNumber = trackingNumber,
            Status = MapYurticiStatusToStandard(response.Status),
            StatusDescription = $"TEST: {response.StatusDescription ?? "Durum bilgisi alınamadı"}",
            LastUpdate = response.LastUpdate ?? DateTime.UtcNow,
            EstimatedDelivery = response.EstimatedDelivery,
            CurrentLocation = $"TEST: {response.CurrentLocation ?? "Yurtiçi Kargo Test"}",
            TrackingEvents = new List<TrackingEvent>
            {
                new()
                {
                    EventDate = response.LastUpdate ?? DateTime.UtcNow,
                    Description = $"TEST: {response.StatusDescription ?? "Durum güncellendi"}",
                    Location = $"TEST: {response.CurrentLocation ?? "Yurtiçi Kargo Test"}"
                }
            }
        };
    }

    /// <summary>
    /// Test ortamı için kargo sorgulama çağrısı
    /// </summary>
    private async Task<YurticiQueryShipmentResponse> CallQueryShipmentTestApiAsync(YurticiQueryShipmentRequest request)
    {
        _logger?.LogInformation("Using TEST environment for Yurtiçi Kargo queryShipment");

        try
        {
            // Test ortamı için KOPSWebServicesTest client kullan
            using var client = new ShippingOrderDispatcherServicesClient();

            var queryShipmentRequest = new queryShipment
            {
                wsUserName = _apiSettings.WsUserName, // "YKTEST"
                wsPassword = _apiSettings.WsPassword, // "YK"
                wsLanguage = _apiSettings.WsLanguage, // "TR"
                keyType = request.KeyType,
                keys = new string[] { request.Key },
                onlyTracking = request.OnlyTracking,
                addHistoricalData = request.AddHistoricalData
            };

            var response = await client.queryShipmentAsync(queryShipmentRequest);
            return ParseQueryShipmentResponse(response);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Yurtiçi Kargo queryShipment TEST API");
            return new YurticiQueryShipmentResponse
            {
                ErrorCode = "EXCEPTION",
                ErrorMessage = ex.Message
            };
        }
    }

    #endregion

    #region Private Parse Methods

    /// <summary>
    /// Test ortamı kargo oluşturma SOAP yanıtını parse et
    /// </summary>
    private YurticiCreateShipmentResponse ParseCreateShipmentResponse(createShipmentResponse1 soapResponse)
    {
        try
        {
            var response = new YurticiCreateShipmentResponse();

            if (soapResponse?.createShipmentResponse?.ShippingOrderResultVO?.shippingOrderDetailVO != null)
            {
                var orderDetail = soapResponse.createShipmentResponse.ShippingOrderResultVO.shippingOrderDetailVO.FirstOrDefault();

                if (orderDetail != null)
                {
                    response.CargoKey = orderDetail.cargoKey;
                    response.InvoiceKey = orderDetail.invoiceKey;
                    response.ErrorCode = orderDetail.errCode.ToString();
                    response.ErrorMessage = orderDetail.errMessage;
                    response.OperationMessage = string.IsNullOrEmpty(orderDetail.errMessage) ? "Success" : orderDetail.errMessage;
                    response.OperationStatus = orderDetail.errCode == 0 ? "Success" : "Error";
                }
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error parsing Yurtiçi Kargo createShipment TEST SOAP response");
            return new YurticiCreateShipmentResponse
            {
                ErrorCode = "PARSE_ERROR",
                ErrorMessage = $"Response parsing error: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// Test ortamı kargo sorgulama SOAP yanıtını parse et
    /// </summary>
    private YurticiQueryShipmentResponse ParseQueryShipmentResponse(queryShipmentResponse1 soapResponse)
    {
        try
        {
            var response = new YurticiQueryShipmentResponse();

            if (soapResponse?.queryShipmentResponse?.ShippingDeliveryVO?.shippingDeliveryDetailVO != null)
            {
                var deliveryDetail = soapResponse.queryShipmentResponse.ShippingDeliveryVO.shippingDeliveryDetailVO.FirstOrDefault();

                if (deliveryDetail != null)
                {
                    response.ErrorCode = deliveryDetail.errCode.ToString();
                    response.ErrorMessage = deliveryDetail.errMessage;
                    response.InvoiceKey = deliveryDetail.invoiceKey;
                    response.OperationMessage = deliveryDetail.operationMessage;
                    response.OperationStatus = deliveryDetail.operationStatus;

                    // Kargo detayları
                    if (deliveryDetail.shippingDeliveryItemDetailVO != null)
                    {
                        var itemDetail = deliveryDetail.shippingDeliveryItemDetailVO;
                        response.CargoKey = itemDetail.docId;
                        response.TrackingUrl = itemDetail.trackingUrl;

                        // Durum bilgilerini çıkar
                        var statusText = itemDetail.cargoEventExplanation ?? itemDetail.deliveryTypeExplanation ?? "Kargo işlemde";
                        response.Status = MapYurticiStatusToStandard(statusText);
                        response.StatusDescription = statusText;

                        // Tarih bilgisi
                        if (DateTime.TryParse(itemDetail.deliveryDate, out var deliveryDate))
                            response.LastUpdate = deliveryDate;
                        else
                            response.LastUpdate = DateTime.UtcNow;

                        // Konum bilgisi
                        response.CurrentLocation = itemDetail.deliveryUnitName ?? itemDetail.arrivalUnitName ?? "Yurtiçi Kargo";
                    }
                }
            }

            // Hata durumunda varsayılan değerler
            if (string.IsNullOrEmpty(response.Status))
            {
                response.Status = "InTransit";
                response.StatusDescription = "Kargo takip edilebilir durumda";
                response.LastUpdate = DateTime.UtcNow;
                response.CurrentLocation = "Yurtiçi Kargo";
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error parsing Yurtiçi Kargo queryShipment TEST SOAP response");
            return new YurticiQueryShipmentResponse
            {
                ErrorCode = "PARSE_ERROR",
                ErrorMessage = $"Response parsing error: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// Yurtiçi Kargo durumunu standart duruma dönüştür
    /// </summary>
    private static string MapYurticiStatusToStandard(string? yurticiStatus)
    {
        return yurticiStatus?.ToLowerInvariant() switch
        {
            "alindi" or "kargo_alindi" => "Picked",
            "yolda" or "transfer_merkezi" => "InTransit",
            "dagitimda" or "dagitim_merkezi" => "OutForDelivery",
            "teslim_edildi" or "teslim" => "Delivered",
            "iptal" or "iptal_edildi" => "Cancelled",
            "iade" or "iade_edildi" => "Returned",
            _ => "InTransit"
        };
    }

    private static string GenerateCargoKey()
    {
        // 10 Haneli, harf ve sayılardan oluşan benzersiz bir anahtar oluştur
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var random = new Random();
        
        // İlk 2 karakter harf
        var key = new string(Enumerable.Range(0, 2)
            .Select(_ => chars[random.Next(0, 26)])
            .ToArray());
            
        // Son 8 karakter harf veya rakam
        key += new string(Enumerable.Range(0, 8)
            .Select(_ => chars[random.Next(chars.Length)])
            .ToArray());
            
        return key;
    }
    #endregion
}
