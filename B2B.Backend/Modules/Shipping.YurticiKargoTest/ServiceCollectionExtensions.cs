using Microsoft.Extensions.DependencyInjection;
using Shipping.Abstraction;

namespace Shipping.YurticiKargoTest;

/// <summary>
/// Yurtiçi Kargo modülü için DI extension metodları
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Yurtiçi Kargo servislerini DI container'a ekle
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddYurticiKargoTestShipping(this IServiceCollection services)
    {
        // Yurtiçi Kargo test servisini transient olarak kaydet
        services.AddTransient<IShippingService, YurticiShippingTestService>();
        services.AddTransient<YurticiShippingTestService>();

        services.AddSingleton<ShippingCarrierDefinition, YurticiTestCarrierDefinition>();
        services.AddSingleton<YurticiTestCarrierDefinition>();

        return services;
    }
}
