# Shipping.YurticiKargo <PERSON>ü<PERSON> modü<PERSON>, Yurtiçi Kargo API'si ile entegrasyon sağlayan SOLID prensiplerine uygun bir implementasyondur. Test ve production ortamları için ayrı servisler sunar.

## Özellikler

- ✅ **Kargo Oluşturma**: `createShipment` API'si ile yeni kargo oluşturma
- ✅ **Kargo Sorgulama**: `queryShipment` API'si ile kargo durumu sorgulama
- ✅ **Kargo İptal**: Kargo iptal işlemleri (API dokümantasyonuna göre güncellenecek)
- ✅ **Maliyet Hesaplama**: Ağırlık ve mesafe bazlı maliyet hesaplama
- ✅ **Ayar Doğrulama**: API bağlantı ayarlarını doğrulama
- ✅ **SOAP Web Servisi**: Gerçek Yurtiçi Kargo WSDL entegrasyonu
- ✅ **Ayrı Test Modülü**: "YURTICITEST" provider'ı ile bağımsız test ortamı
- ✅ **Production Modülü**: "YURTICI" provider'ı ile canlı ortam
- ✅ **Güvenli Test**: Test modunda sabit kimlik bilgileri, gerçek kargo oluşturmaz

## Teknolojiler

- .NET 9.0
- System.ServiceModel (SOAP Client)
- Microsoft.Extensions.Configuration
- Microsoft.Extensions.Logging
- Shipping.Abstraction (Base Interface)

## Kurulum

### 1. Proje Referansı

```xml
<ProjectReference Include="..\Shipping.YurticiKargo\Shipping.YurticiKargo.csproj" />
```

### 2. DI Container Kaydı

```csharp
// Startup.cs veya Program.cs
services.AddYurticiKargoShipping();
```

### 3. Ortam Değişkenleri

#### Test Ortamı (YURTICITEST Provider)
Test provider'ı için herhangi bir kimlik bilgisi gerekmez:

```bash
# Test ortamı için opsiyonel ayarlar
YURTICI_API_URL=https://api.yurtici.com.tr
YURTICI_TIMEOUT=30
YURTICI_MAX_RETRIES=3
```

#### Canlı Ortam (YURTICI Provider)
Canlı ortam için gerçek API bilgilerinizi ayarlayın:

```bash
# Canlı ortam için gerekli ayarlar
YURTICI_WS_USERNAME=your_real_username
YURTICI_WS_PASSWORD=your_real_password
YURTICI_WS_LANGUAGE=TR
YURTICI_API_URL=https://api.yurtici.com.tr
YURTICI_TIMEOUT=30
YURTICI_MAX_RETRIES=3
```

#### appsettings.json Alternatifi

Test ortamı için:
```json
{
  "Shipping": {
    "Yurtici": {
      "ApiUrl": "https://api.yurtici.com.tr",
      "Timeout": 30,
      "MaxRetries": 3
    }
  }
}
```

Canlı ortam için:
```json
{
  "Shipping": {
    "Yurtici": {
      "WsUserName": "your_real_username",
      "WsPassword": "your_real_password",
      "WsLanguage": "TR",
      "ApiUrl": "https://api.yurtici.com.tr",
      "Timeout": 30,
      "MaxRetries": 3
    }
  }
}
```

## Kullanım

### Factory Pattern ile Servis Alma

```csharp
public class OrderService
{
    private readonly IShippingServiceFactory _shippingFactory;

    public OrderService(IShippingServiceFactory shippingFactory)
    {
        _shippingFactory = shippingFactory;
    }

    public async Task CreateTestShipment(Order order)
    {
        // Test ortamı için YURTICITEST provider'ını kullan
        var testService = _shippingFactory.GetService("YURTICITEST");

        if (testService != null)
        {
            var request = new ShipmentRequest
            {
                OrderId = order.Id,
                RecipientName = order.CustomerName,
                RecipientPhone = order.Phone,
                Address = order.Address,
                City = order.City,
                District = order.District,
                PostalCode = order.PostalCode,
                Weight = order.TotalWeight
            };

            var trackingNumber = await testService.CreateShipmentAsync(request);
            // Test takip numarasını kaydet (TEST prefix ile)...
        }
    }

    public async Task CreateProductionShipment(Order order)
    {
        // Canlı ortam için YURTICI provider'ını kullan
        var productionService = _shippingFactory.GetService("YURTICI");

        if (productionService != null)
        {
            var request = new ShipmentRequest
            {
                OrderId = order.Id,
                RecipientName = order.CustomerName,
                RecipientPhone = order.Phone,
                Address = order.Address,
                City = order.City,
                District = order.District,
                PostalCode = order.PostalCode,
                Weight = order.TotalWeight
            };

            var trackingNumber = await productionService.CreateShipmentAsync(request);
            // Gerçek takip numarasını kaydet...
        }
    }
}
```

### Direkt Servis Kullanımı

```csharp
public class ShippingController : ControllerBase
{
    private readonly YurticiShippingService _yurticiService;

    public ShippingController(YurticiShippingService yurticiService)
    {
        _yurticiService = yurticiService;
    }

    [HttpGet("track/{trackingNumber}")]
    public async Task<IActionResult> TrackShipment(string trackingNumber)
    {
        var trackingInfo = await _yurticiService.GetTrackingInfoAsync(trackingNumber);
        return Ok(trackingInfo);
    }
}
```

## API Metodları

### CreateShipmentAsync
Yeni kargo oluşturur ve takip numarası döndürür.

### GetTrackingInfoAsync  
Takip numarası ile kargo durumunu sorgular.

### CancelShipmentAsync
Kargo iptal işlemi yapar.

### CalculateShippingCostAsync
Ağırlık ve mesafe bazlı kargo maliyeti hesaplar.

### ValidateSettingsAsync
API bağlantı ayarlarını doğrular.

## Hata Yönetimi

Tüm API çağrıları try-catch blokları ile korunmuştur ve detaylı loglama yapılmaktadır. Hata durumlarında anlamlı exception'lar fırlatılır.

## Loglama

Microsoft.Extensions.Logging kullanılarak detaylı loglama yapılmaktadır:

- Info: Başarılı işlemler
- Warning: Uyarı durumları  
- Error: Hata durumları

## Test

Modül unit test'ler için hazırdır. Mock'lanabilir interface'ler kullanılmıştır.

## Lisans

Bu modül B2B projesi kapsamında geliştirilmiştir.
