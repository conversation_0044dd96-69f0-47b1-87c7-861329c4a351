using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Shipping.Abstraction;
using System.Reflection;

namespace Shipping.Implementation;

/// <summary>
/// Kargo servisi factory implementasyonu
/// Modül discovery ve service creation işlemlerini yönetir
/// </summary>
public class ShippingServiceFactory : IShippingServiceFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ShippingServiceFactory> _logger;
    private readonly Dictionary<string, Type> _serviceTypes;
    private readonly Dictionary<Guid, Type> _serviceTypesByGuid;
    private readonly Dictionary<string, ShippingCarrierDefinition> _definitions;
    private readonly Dictionary<Guid, ShippingCarrierDefinition> _definitionsByGuid;

    public ShippingServiceFactory(IServiceProvider serviceProvider, ILogger<ShippingServiceFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _serviceTypes = new Dictionary<string, Type>();
        _serviceTypesByGuid = new Dictionary<Guid, Type>();
        _definitions = new Dictionary<string, ShippingCarrierDefinition>();
        _definitionsByGuid = new Dictionary<Guid, ShippingCarrierDefinition>();

        DiscoverShippingServices();
    }

    public IShippingService? GetService(Guid id, Dictionary<string, string>? settings = null)
    {
        try
        {
            if (!_serviceTypesByGuid.TryGetValue(id, out var serviceType))
            {
                _logger.LogWarning("Shipping service not found for ID: {Id}", id);
                return null;
            }

            return CreateServiceInstance(serviceType, settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating shipping service for ID: {Id}", id);
            return null;
        }
    }

    public IShippingService? GetService(string shortCode, Dictionary<string, string>? settings = null)
    {
        try
        {
            if (!_serviceTypes.TryGetValue(shortCode.ToUpperInvariant(), out var serviceType))
            {
                _logger.LogWarning("Shipping service not found for short code: {ShortCode}", shortCode);
                return null;
            }

            return CreateServiceInstance(serviceType, settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating shipping service for short code: {ShortCode}", shortCode);
            return null;
        }
    }

    public IEnumerable<ShippingCarrierDefinition> GetAvailableCarriers()
    {
        return _definitions.Values;
    }

    public bool IsCarrierImplemented(Guid id)
    {
        return _serviceTypesByGuid.ContainsKey(id);
    }

    public bool IsCarrierImplemented(string shortCode)
    {
        return _serviceTypes.ContainsKey(shortCode.ToUpperInvariant());
    }

    public ShippingCarrierDefinition? GetCarrierDefinition(Guid id)
    {
        _definitionsByGuid.TryGetValue(id, out var definition);
        return definition;
    }

    public ShippingCarrierDefinition? GetCarrierDefinition(string shortCode)
    {
        _definitions.TryGetValue(shortCode.ToUpperInvariant(), out var definition);
        return definition;
    }

    public void RefreshImplementations()
    {
        _serviceTypes.Clear();
        _serviceTypesByGuid.Clear();
        _definitions.Clear();
        _definitionsByGuid.Clear();
        DiscoverShippingServices();
    }

    private void DiscoverShippingServices()
    {
        try
        {
            _logger.LogInformation("Discovering shipping services...");

            // Mevcut assembly'leri tara
            var assemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => !a.IsDynamic && a.FullName != null && 
                           (a.FullName.Contains("Shipping") || a.FullName.Contains("Kargo")))
                .ToList();

            foreach (var assembly in assemblies)
            {
                try
                {
                    DiscoverServicesInAssembly(assembly);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error discovering services in assembly: {AssemblyName}", assembly.FullName);
                }
            }

            _logger.LogInformation("Discovered {Count} shipping services", _serviceTypes.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during shipping service discovery");
        }
    }

    private void DiscoverServicesInAssembly(Assembly assembly)
    {
        var serviceTypes = assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract && 
                       typeof(IShippingService).IsAssignableFrom(t))
            .ToList();

        foreach (var serviceType in serviceTypes)
        {
            try
            {
                // Service instance oluştur ve definition'ı al
                var service = CreateServiceInstance(serviceType);
                if (service?.Definition != null)
                {
                    var shortCode = service.Definition.ShortCode.ToUpperInvariant();
                    var id = service.Definition.Id;

                    _serviceTypes[shortCode] = serviceType;
                    _serviceTypesByGuid[id] = serviceType;
                    _definitions[shortCode] = service.Definition;
                    _definitionsByGuid[id] = service.Definition;

                    _logger.LogInformation("Registered shipping service: {Name} ({ShortCode}) - {Id}", 
                        service.Definition.Name, shortCode, id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error registering shipping service: {ServiceType}", serviceType.Name);
            }
        }
    }

    private IShippingService? CreateServiceInstance(Type serviceType, Dictionary<string, string>? settings = null)
    {
        try
        {
            // DI container'dan service instance al
            var service = _serviceProvider.GetService(serviceType) as IShippingService;
            if (service != null)
            {
                return service;
            }

            // Eğer DI'da kayıtlı değilse, manuel olarak oluştur
            var constructors = serviceType.GetConstructors()
                .OrderByDescending(c => c.GetParameters().Length);

            foreach (var constructor in constructors)
            {
                try
                {
                    var parameters = constructor.GetParameters();
                    var args = new object[parameters.Length];

                    for (int i = 0; i < parameters.Length; i++)
                    {
                        var paramType = parameters[i].ParameterType;
                        
                        if (paramType == typeof(Dictionary<string, string>) && settings != null)
                        {
                            args[i] = settings;
                        }
                        else
                        {
                            args[i] = _serviceProvider.GetService(paramType) ?? 
                                     throw new InvalidOperationException($"Cannot resolve parameter: {paramType.Name}");
                        }
                    }

                    return (IShippingService)Activator.CreateInstance(serviceType, args)!;
                }
                catch
                {
                    // Bu constructor çalışmadı, bir sonrakini dene
                    continue;
                }
            }

            _logger.LogWarning("Could not create instance of shipping service: {ServiceType}", serviceType.Name);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating shipping service instance: {ServiceType}", serviceType.Name);
            return null;
        }
    }
}
