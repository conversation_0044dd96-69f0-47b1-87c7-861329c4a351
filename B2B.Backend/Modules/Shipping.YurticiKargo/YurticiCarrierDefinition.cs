using Shipping.Abstraction;

namespace Shipping.YurticiKargo;

/// <summary>
/// Yurtiçi Kargo firması tanımı
/// </summary>
public class YurticiCarrierDefinition : ShippingCarrierDefinition
{
    public override string Name => "Yurtiçi Kargo";
    
    public override string ShortCode => "YURTICI";
    
    public override string Description => "Yurtiçi Kargo entegrasyonu - Türkiye'nin lider kargo firması. Gerçek API entegrasyonu ile kargo oluşturma, takip ve iptal işlemleri.";
    
    public override bool RequiresApiKey => true;
    
    public override bool RequiresApiUrl => true;
    
    public override string? LogoUrl => "/images/carriers/yurtici-logo.png";
    
    public override ShippingFeatures SupportedFeatures =>
        ShippingFeatures.CreateShipment |
        ShippingFeatures.TrackShipment |
        ShippingFeatures.CancelShipment |
        ShippingFeatures.GetShippingCost;

    public override Dictionary<string, string> DefaultSettings => new()
    {
        { "ApiUrl", "https://api.yurtici.com.tr" },
        { "WsUserName", "" }, // Ortam değişkeninden okunacak
        { "WsPassword", "" }, // Ortam değişkeninden okunacak
        { "WsLanguage", "TR" },
        { "TestMode", "true" },
        { "Timeout", "30" },
        { "MaxRetries", "3" },
        { "DefaultServiceType", "Standard" },
        { "InsuranceEnabled", "false" },
        { "SmsNotification", "true" },
        { "EmailNotification", "true" },
        { "AutoCreateShipment", "false" },
        { "RequirePhoneValidation", "true" },
        { "MinWeight", "0.1" },
        { "MaxWeight", "30.0" },
        { "SupportedCountries", "TR" },
        { "BusinessDaysOnly", "true" },
        { "CutoffTime", "17:00" }
    };
}
