using System.ComponentModel.DataAnnotations;

namespace Shipping.YurticiKargo.Models;

/// <summary>
/// Yurtiçi Kargo API ayarları
/// </summary>
public class YurticiApiSettings
{
    /// <summary>
    /// Web servis kullanıcı adı
    /// </summary>
    [Required]
    public string WsUserName { get; set; } = null!;

    /// <summary>
    /// Web servis şifresi
    /// </summary>
    [Required]
    public string WsPassword { get; set; } = null!;

    /// <summary>
    /// Web servis dili (TR, EN)
    /// </summary>
    public string WsLanguage { get; set; } = "TR";

    /// <summary>
    /// API URL'i
    /// </summary>
    public string ApiUrl { get; set; } = "https://api.yurtici.com.tr";

    /// <summary>
    /// Test modu aktif mi?
    /// </summary>
    public bool TestMode { get; set; } = true;

    /// <summary>
    /// Timeout süresi (saniye)
    /// </summary>
    public int Timeout { get; set; } = 30;

    /// <summary>
    /// Maksimum yeniden deneme sayısı
    /// </summary>
    public int MaxRetries { get; set; } = 3;
}

/// <summary>
/// Kargo oluşturma isteği
/// </summary>
public class YurticiCreateShipmentRequest
{
    /// <summary>
    /// Kargo anahtarı (benzersiz)
    /// </summary>
    [Required]
    public string CargoKey { get; set; } = null!;

    /// <summary>
    /// Fatura anahtarı
    /// </summary>
    [Required]
    public string InvoiceKey { get; set; } = null!;

    /// <summary>
    /// Alıcı adı soyadı
    /// </summary>
    [Required]
    public string ReceiverCustName { get; set; } = null!;

    /// <summary>
    /// Alıcı adresi
    /// </summary>
    [Required]
    public string ReceiverAddress { get; set; } = null!;

    /// <summary>
    /// Alıcı telefon numarası
    /// </summary>
    [Required]
    public string ReceiverPhone1 { get; set; } = null!;

    /// <summary>
    /// Alıcı e-posta adresi (opsiyonel)
    /// </summary>
    public string? ReceiverEmail { get; set; }

    /// <summary>
    /// Kargo değeri (sigorta için)
    /// </summary>
    public decimal? DeclaredValue { get; set; }

    /// <summary>
    /// Ağırlık (kg)
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// Özel talimatlar
    /// </summary>
    public string? SpecialInstructions { get; set; }
}

/// <summary>
/// Kargo oluşturma yanıtı
/// </summary>
public class YurticiCreateShipmentResponse
{
    /// <summary>
    /// Kargo anahtarı
    /// </summary>
    public string? CargoKey { get; set; }

    /// <summary>
    /// Fatura anahtarı
    /// </summary>
    public string? InvoiceKey { get; set; }

    /// <summary>
    /// Hata kodu
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Hata mesajı
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// İşlem mesajı
    /// </summary>
    public string? OperationMessage { get; set; }

    /// <summary>
    /// İşlem durumu
    /// </summary>
    public string? OperationStatus { get; set; }

    /// <summary>
    /// Takip URL'i
    /// </summary>
    public string? TrackingUrl { get; set; }

    /// <summary>
    /// İşlem başarılı mı?
    /// </summary>
    public bool IsSuccess => string.IsNullOrEmpty(ErrorCode) || ErrorCode == "0";
}

/// <summary>
/// Kargo sorgulama isteği
/// </summary>
public class YurticiQueryShipmentRequest
{
    /// <summary>
    /// Sorgulanacak anahtar (kargo no, fatura no vb.)
    /// </summary>
    [Required]
    public string Key { get; set; } = null!;

    /// <summary>
    /// Anahtar tipi (1: Kargo No, 2: Fatura No)
    /// </summary>
    public int KeyType { get; set; } = 1;

    /// <summary>
    /// Sadece takip bilgisi mi?
    /// </summary>
    public bool OnlyTracking { get; set; } = false;

    /// <summary>
    /// Geçmiş veriler dahil edilsin mi?
    /// </summary>
    public bool AddHistoricalData { get; set; } = true;
}

/// <summary>
/// Kargo sorgulama yanıtı
/// </summary>
public class YurticiQueryShipmentResponse
{
    /// <summary>
    /// Kargo anahtarı
    /// </summary>
    public string? CargoKey { get; set; }

    /// <summary>
    /// Hata kodu
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Hata mesajı
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Fatura anahtarı
    /// </summary>
    public string? InvoiceKey { get; set; }

    /// <summary>
    /// İşlem mesajı
    /// </summary>
    public string? OperationMessage { get; set; }

    /// <summary>
    /// İşlem durumu
    /// </summary>
    public string? OperationStatus { get; set; }

    /// <summary>
    /// Takip URL'i
    /// </summary>
    public string? TrackingUrl { get; set; }

    /// <summary>
    /// Kargo durumu
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Durum açıklaması
    /// </summary>
    public string? StatusDescription { get; set; }

    /// <summary>
    /// Son güncelleme tarihi
    /// </summary>
    public DateTime? LastUpdate { get; set; }

    /// <summary>
    /// Tahmini teslimat tarihi
    /// </summary>
    public DateTime? EstimatedDelivery { get; set; }

    /// <summary>
    /// Mevcut konum
    /// </summary>
    public string? CurrentLocation { get; set; }

    /// <summary>
    /// İşlem başarılı mı?
    /// </summary>
    public bool IsSuccess => string.IsNullOrEmpty(ErrorCode) || ErrorCode == "0";
}

/// <summary>
/// Takip olayı
/// </summary>
public class YurticiTrackingEvent
{
    /// <summary>
    /// Olay tarihi
    /// </summary>
    public DateTime EventDate { get; set; }

    /// <summary>
    /// Olay açıklaması
    /// </summary>
    public string Description { get; set; } = null!;

    /// <summary>
    /// Konum
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// Durum kodu
    /// </summary>
    public string? StatusCode { get; set; }
}
