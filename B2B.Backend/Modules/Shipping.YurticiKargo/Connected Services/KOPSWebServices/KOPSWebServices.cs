﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Shipping.YurticiKargo.KOPSWebServices
{
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/WSExceptions/")]
    public partial class YurticikargoWSException
    {
        
        private int errorCodeField;
        
        private string errTextField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public int errorCode
        {
            get
            {
                return this.errorCodeField;
            }
            set
            {
                this.errorCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string errText
        {
            get
            {
                return this.errTextField;
            }
            set
            {
                this.errTextField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XEDocCargo
    {
        
        private string barcodeStringValueField;
        
        private string docCargoZplField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string barcodeStringValue
        {
            get
            {
                return this.barcodeStringValueField;
            }
            set
            {
                this.barcodeStringValueField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string docCargoZpl
        {
            get
            {
                return this.docCargoZplField;
            }
            set
            {
                this.docCargoZplField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XESpecialField
    {
        
        private string specialFieldNameField;
        
        private string specialFieldValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string specialFieldName
        {
            get
            {
                return this.specialFieldNameField;
            }
            set
            {
                this.specialFieldNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string specialFieldValue
        {
            get
            {
                return this.specialFieldValueField;
            }
            set
            {
                this.specialFieldValueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shipmentDeliveryDetailVO
    {
        
        private string cargoKeyField;
        
        private int errCodeField;
        
        private bool errCodeFieldSpecified;
        
        private string errMessageField;
        
        private string invoiceKeyField;
        
        private long jobIdField;
        
        private int operationCodeField;
        
        private bool operationCodeFieldSpecified;
        
        private string operationMessageField;
        
        private string operationStatusField;
        
        private ShipmentDeliveryItemDetailVO shipmentDeliveryItemDetailVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string cargoKey
        {
            get
            {
                return this.cargoKeyField;
            }
            set
            {
                this.cargoKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public int errCode
        {
            get
            {
                return this.errCodeField;
            }
            set
            {
                this.errCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool errCodeSpecified
        {
            get
            {
                return this.errCodeFieldSpecified;
            }
            set
            {
                this.errCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string errMessage
        {
            get
            {
                return this.errMessageField;
            }
            set
            {
                this.errMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string invoiceKey
        {
            get
            {
                return this.invoiceKeyField;
            }
            set
            {
                this.invoiceKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public long jobId
        {
            get
            {
                return this.jobIdField;
            }
            set
            {
                this.jobIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public int operationCode
        {
            get
            {
                return this.operationCodeField;
            }
            set
            {
                this.operationCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool operationCodeSpecified
        {
            get
            {
                return this.operationCodeFieldSpecified;
            }
            set
            {
                this.operationCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string operationMessage
        {
            get
            {
                return this.operationMessageField;
            }
            set
            {
                this.operationMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string operationStatus
        {
            get
            {
                return this.operationStatusField;
            }
            set
            {
                this.operationStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public ShipmentDeliveryItemDetailVO shipmentDeliveryItemDetailVO
        {
            get
            {
                return this.shipmentDeliveryItemDetailVOField;
            }
            set
            {
                this.shipmentDeliveryItemDetailVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class ShipmentDeliveryItemDetailVO : ShippingDeliveryItemDetailVO
    {
        
        private string estimatedDeliveryDateField;
        
        private string estimatedDeliveryTimeField;
        
        private string perfCFlagField;
        
        private string delivDurationWorkDayField;
        
        private string delivDurationField;
        
        private string perfLateCEventField;
        
        private string perfLateCReasonField;
        
        private string receiverPhoneField;
        
        private string receiverGsmField;
        
        private string shipmentDistanceField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string estimatedDeliveryDate
        {
            get
            {
                return this.estimatedDeliveryDateField;
            }
            set
            {
                this.estimatedDeliveryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string estimatedDeliveryTime
        {
            get
            {
                return this.estimatedDeliveryTimeField;
            }
            set
            {
                this.estimatedDeliveryTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string perfCFlag
        {
            get
            {
                return this.perfCFlagField;
            }
            set
            {
                this.perfCFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string delivDurationWorkDay
        {
            get
            {
                return this.delivDurationWorkDayField;
            }
            set
            {
                this.delivDurationWorkDayField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string delivDuration
        {
            get
            {
                return this.delivDurationField;
            }
            set
            {
                this.delivDurationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string perfLateCEvent
        {
            get
            {
                return this.perfLateCEventField;
            }
            set
            {
                this.perfLateCEventField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string perfLateCReason
        {
            get
            {
                return this.perfLateCReasonField;
            }
            set
            {
                this.perfLateCReasonField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string receiverPhone
        {
            get
            {
                return this.receiverPhoneField;
            }
            set
            {
                this.receiverPhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string receiverGsm
        {
            get
            {
                return this.receiverGsmField;
            }
            set
            {
                this.receiverGsmField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string shipmentDistance
        {
            get
            {
                return this.shipmentDistanceField;
            }
            set
            {
                this.shipmentDistanceField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ShipmentDeliveryItemDetailVO))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class ShippingDeliveryItemDetailVO
    {
        
        private string cargoKeyField;
        
        private string docIdField;
        
        private string invoiceNumberField;
        
        private string docNumberField;
        
        private string waybillNoField;
        
        private string documentDebitIdField;
        
        private string senderCustIdField;
        
        private string senderCustNameField;
        
        private string senderAddressTxtField;
        
        private string receiverCustIdField;
        
        private string receiverCustNameField;
        
        private string receiverAddressTxtField;
        
        private string documentDateField;
        
        private string documentTimeField;
        
        private string documentDelFlagField;
        
        private string receiverInfoField;
        
        private string docTypeField;
        
        private string docTypeExplanationField;
        
        private string contractIdField;
        
        private string trackingUrlField;
        
        private string cargoTypeField;
        
        private string cargoTypeExplanationField;
        
        private string pickupTypeField;
        
        private string pickupTypeExplanationField;
        
        private string deliveryTypeField;
        
        private string deliveryTypeExplanationField;
        
        private string deliveryDateField;
        
        private string deliveryTimeField;
        
        private string totalPriceField;
        
        private string totalVatField;
        
        private string totalAmountField;
        
        private string productField;
        
        private string totalDesiField;
        
        private string totalKgField;
        
        private string totalDesiKgField;
        
        private string totalCargoField;
        
        private string arrivalUnitIdField;
        
        private string arrivalUnitNameField;
        
        private string departureUnitIdField;
        
        private string departureUnitNameField;
        
        private string arrivalTrCenterUnitIdField;
        
        private string arrivalTrCenterNameField;
        
        private string departureTrCenterUnitIdField;
        
        private string departureTrCenterNameField;
        
        private string deliveryUnitIdField;
        
        private string deliveryUnitNameField;
        
        private string deliveryUnitTypeField;
        
        private string deliveryUnitTypeExplanationField;
        
        private string cargoEventIdField;
        
        private string cargoEventExplanationField;
        
        private string cargoReasonIdField;
        
        private string cargoReasonExplanationField;
        
        private string documentEventIdField;
        
        private string documentReasonIdField;
        
        private string documentEventExplanationField;
        
        private string documentReasonExplanationField;
        
        private string delInfoDeliveryFlagField;
        
        private string delInfoDelUnitIdField;
        
        private string delEmpNameField;
        
        private string delEmpIdField;
        
        private string rejectStatusField;
        
        private string rejectStatusExplanationField;
        
        private string rejectDescriptionField;
        
        private string rejectReasonExplanationField;
        
        private string returnDocIdField;
        
        private string returnDocumentDateField;
        
        private string returnDeliveryDateField;
        
        private string returnStatusField;
        
        private string returnStatusExplanationField;
        
        private InvDocFieldVO[] invDocFieldVOArrayField;
        
        private InvDocCargoVO[] invDocCargoVOArrayField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string cargoKey
        {
            get
            {
                return this.cargoKeyField;
            }
            set
            {
                this.cargoKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string docId
        {
            get
            {
                return this.docIdField;
            }
            set
            {
                this.docIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string invoiceNumber
        {
            get
            {
                return this.invoiceNumberField;
            }
            set
            {
                this.invoiceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string docNumber
        {
            get
            {
                return this.docNumberField;
            }
            set
            {
                this.docNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string waybillNo
        {
            get
            {
                return this.waybillNoField;
            }
            set
            {
                this.waybillNoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string documentDebitId
        {
            get
            {
                return this.documentDebitIdField;
            }
            set
            {
                this.documentDebitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string senderCustId
        {
            get
            {
                return this.senderCustIdField;
            }
            set
            {
                this.senderCustIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string senderCustName
        {
            get
            {
                return this.senderCustNameField;
            }
            set
            {
                this.senderCustNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string senderAddressTxt
        {
            get
            {
                return this.senderAddressTxtField;
            }
            set
            {
                this.senderAddressTxtField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string receiverCustId
        {
            get
            {
                return this.receiverCustIdField;
            }
            set
            {
                this.receiverCustIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string receiverCustName
        {
            get
            {
                return this.receiverCustNameField;
            }
            set
            {
                this.receiverCustNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string receiverAddressTxt
        {
            get
            {
                return this.receiverAddressTxtField;
            }
            set
            {
                this.receiverAddressTxtField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string documentDate
        {
            get
            {
                return this.documentDateField;
            }
            set
            {
                this.documentDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string documentTime
        {
            get
            {
                return this.documentTimeField;
            }
            set
            {
                this.documentTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string documentDelFlag
        {
            get
            {
                return this.documentDelFlagField;
            }
            set
            {
                this.documentDelFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string receiverInfo
        {
            get
            {
                return this.receiverInfoField;
            }
            set
            {
                this.receiverInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string docType
        {
            get
            {
                return this.docTypeField;
            }
            set
            {
                this.docTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string docTypeExplanation
        {
            get
            {
                return this.docTypeExplanationField;
            }
            set
            {
                this.docTypeExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string contractId
        {
            get
            {
                return this.contractIdField;
            }
            set
            {
                this.contractIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string trackingUrl
        {
            get
            {
                return this.trackingUrlField;
            }
            set
            {
                this.trackingUrlField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string cargoType
        {
            get
            {
                return this.cargoTypeField;
            }
            set
            {
                this.cargoTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string cargoTypeExplanation
        {
            get
            {
                return this.cargoTypeExplanationField;
            }
            set
            {
                this.cargoTypeExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string pickupType
        {
            get
            {
                return this.pickupTypeField;
            }
            set
            {
                this.pickupTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string pickupTypeExplanation
        {
            get
            {
                return this.pickupTypeExplanationField;
            }
            set
            {
                this.pickupTypeExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public string deliveryType
        {
            get
            {
                return this.deliveryTypeField;
            }
            set
            {
                this.deliveryTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public string deliveryTypeExplanation
        {
            get
            {
                return this.deliveryTypeExplanationField;
            }
            set
            {
                this.deliveryTypeExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=26)]
        public string deliveryDate
        {
            get
            {
                return this.deliveryDateField;
            }
            set
            {
                this.deliveryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=27)]
        public string deliveryTime
        {
            get
            {
                return this.deliveryTimeField;
            }
            set
            {
                this.deliveryTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=28)]
        public string totalPrice
        {
            get
            {
                return this.totalPriceField;
            }
            set
            {
                this.totalPriceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=29)]
        public string totalVat
        {
            get
            {
                return this.totalVatField;
            }
            set
            {
                this.totalVatField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=30)]
        public string totalAmount
        {
            get
            {
                return this.totalAmountField;
            }
            set
            {
                this.totalAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=31)]
        public string product
        {
            get
            {
                return this.productField;
            }
            set
            {
                this.productField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=32)]
        public string totalDesi
        {
            get
            {
                return this.totalDesiField;
            }
            set
            {
                this.totalDesiField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=33)]
        public string totalKg
        {
            get
            {
                return this.totalKgField;
            }
            set
            {
                this.totalKgField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=34)]
        public string totalDesiKg
        {
            get
            {
                return this.totalDesiKgField;
            }
            set
            {
                this.totalDesiKgField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=35)]
        public string totalCargo
        {
            get
            {
                return this.totalCargoField;
            }
            set
            {
                this.totalCargoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=36)]
        public string arrivalUnitId
        {
            get
            {
                return this.arrivalUnitIdField;
            }
            set
            {
                this.arrivalUnitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=37)]
        public string arrivalUnitName
        {
            get
            {
                return this.arrivalUnitNameField;
            }
            set
            {
                this.arrivalUnitNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=38)]
        public string departureUnitId
        {
            get
            {
                return this.departureUnitIdField;
            }
            set
            {
                this.departureUnitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=39)]
        public string departureUnitName
        {
            get
            {
                return this.departureUnitNameField;
            }
            set
            {
                this.departureUnitNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=40)]
        public string arrivalTrCenterUnitId
        {
            get
            {
                return this.arrivalTrCenterUnitIdField;
            }
            set
            {
                this.arrivalTrCenterUnitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=41)]
        public string arrivalTrCenterName
        {
            get
            {
                return this.arrivalTrCenterNameField;
            }
            set
            {
                this.arrivalTrCenterNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=42)]
        public string departureTrCenterUnitId
        {
            get
            {
                return this.departureTrCenterUnitIdField;
            }
            set
            {
                this.departureTrCenterUnitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=43)]
        public string departureTrCenterName
        {
            get
            {
                return this.departureTrCenterNameField;
            }
            set
            {
                this.departureTrCenterNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=44)]
        public string deliveryUnitId
        {
            get
            {
                return this.deliveryUnitIdField;
            }
            set
            {
                this.deliveryUnitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=45)]
        public string deliveryUnitName
        {
            get
            {
                return this.deliveryUnitNameField;
            }
            set
            {
                this.deliveryUnitNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=46)]
        public string deliveryUnitType
        {
            get
            {
                return this.deliveryUnitTypeField;
            }
            set
            {
                this.deliveryUnitTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=47)]
        public string deliveryUnitTypeExplanation
        {
            get
            {
                return this.deliveryUnitTypeExplanationField;
            }
            set
            {
                this.deliveryUnitTypeExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=48)]
        public string cargoEventId
        {
            get
            {
                return this.cargoEventIdField;
            }
            set
            {
                this.cargoEventIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=49)]
        public string cargoEventExplanation
        {
            get
            {
                return this.cargoEventExplanationField;
            }
            set
            {
                this.cargoEventExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=50)]
        public string cargoReasonId
        {
            get
            {
                return this.cargoReasonIdField;
            }
            set
            {
                this.cargoReasonIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=51)]
        public string cargoReasonExplanation
        {
            get
            {
                return this.cargoReasonExplanationField;
            }
            set
            {
                this.cargoReasonExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=52)]
        public string documentEventId
        {
            get
            {
                return this.documentEventIdField;
            }
            set
            {
                this.documentEventIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=53)]
        public string documentReasonId
        {
            get
            {
                return this.documentReasonIdField;
            }
            set
            {
                this.documentReasonIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=54)]
        public string documentEventExplanation
        {
            get
            {
                return this.documentEventExplanationField;
            }
            set
            {
                this.documentEventExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=55)]
        public string documentReasonExplanation
        {
            get
            {
                return this.documentReasonExplanationField;
            }
            set
            {
                this.documentReasonExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=56)]
        public string delInfoDeliveryFlag
        {
            get
            {
                return this.delInfoDeliveryFlagField;
            }
            set
            {
                this.delInfoDeliveryFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=57)]
        public string delInfoDelUnitId
        {
            get
            {
                return this.delInfoDelUnitIdField;
            }
            set
            {
                this.delInfoDelUnitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=58)]
        public string delEmpName
        {
            get
            {
                return this.delEmpNameField;
            }
            set
            {
                this.delEmpNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=59)]
        public string delEmpId
        {
            get
            {
                return this.delEmpIdField;
            }
            set
            {
                this.delEmpIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=60)]
        public string rejectStatus
        {
            get
            {
                return this.rejectStatusField;
            }
            set
            {
                this.rejectStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=61)]
        public string rejectStatusExplanation
        {
            get
            {
                return this.rejectStatusExplanationField;
            }
            set
            {
                this.rejectStatusExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=62)]
        public string rejectDescription
        {
            get
            {
                return this.rejectDescriptionField;
            }
            set
            {
                this.rejectDescriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=63)]
        public string rejectReasonExplanation
        {
            get
            {
                return this.rejectReasonExplanationField;
            }
            set
            {
                this.rejectReasonExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=64)]
        public string returnDocId
        {
            get
            {
                return this.returnDocIdField;
            }
            set
            {
                this.returnDocIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=65)]
        public string returnDocumentDate
        {
            get
            {
                return this.returnDocumentDateField;
            }
            set
            {
                this.returnDocumentDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=66)]
        public string returnDeliveryDate
        {
            get
            {
                return this.returnDeliveryDateField;
            }
            set
            {
                this.returnDeliveryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=67)]
        public string returnStatus
        {
            get
            {
                return this.returnStatusField;
            }
            set
            {
                this.returnStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=68)]
        public string returnStatusExplanation
        {
            get
            {
                return this.returnStatusExplanationField;
            }
            set
            {
                this.returnStatusExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("invDocFieldVOArray", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=69)]
        public InvDocFieldVO[] invDocFieldVOArray
        {
            get
            {
                return this.invDocFieldVOArrayField;
            }
            set
            {
                this.invDocFieldVOArrayField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("invDocCargoVOArray", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=70)]
        public InvDocCargoVO[] invDocCargoVOArray
        {
            get
            {
                return this.invDocCargoVOArrayField;
            }
            set
            {
                this.invDocCargoVOArrayField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class InvDocFieldVO
    {
        
        private string docIdField;
        
        private string fieldNameField;
        
        private string fieldNameExplanationField;
        
        private string fieldValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string docId
        {
            get
            {
                return this.docIdField;
            }
            set
            {
                this.docIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string fieldName
        {
            get
            {
                return this.fieldNameField;
            }
            set
            {
                this.fieldNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string fieldNameExplanation
        {
            get
            {
                return this.fieldNameExplanationField;
            }
            set
            {
                this.fieldNameExplanationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string fieldValue
        {
            get
            {
                return this.fieldValueField;
            }
            set
            {
                this.fieldValueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class InvDocCargoVO
    {
        
        private string unitIdField;
        
        private string unitNameField;
        
        private string eventIdField;
        
        private string eventNameField;
        
        private string reasonIdField;
        
        private string reasonNameField;
        
        private string eventDateField;
        
        private string eventTimeField;
        
        private string cityNameField;
        
        private string townNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string unitId
        {
            get
            {
                return this.unitIdField;
            }
            set
            {
                this.unitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string unitName
        {
            get
            {
                return this.unitNameField;
            }
            set
            {
                this.unitNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string eventId
        {
            get
            {
                return this.eventIdField;
            }
            set
            {
                this.eventIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string eventName
        {
            get
            {
                return this.eventNameField;
            }
            set
            {
                this.eventNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string reasonId
        {
            get
            {
                return this.reasonIdField;
            }
            set
            {
                this.reasonIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string reasonName
        {
            get
            {
                return this.reasonNameField;
            }
            set
            {
                this.reasonNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string eventDate
        {
            get
            {
                return this.eventDateField;
            }
            set
            {
                this.eventDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string eventTime
        {
            get
            {
                return this.eventTimeField;
            }
            set
            {
                this.eventTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string cityName
        {
            get
            {
                return this.cityNameField;
            }
            set
            {
                this.cityNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string townName
        {
            get
            {
                return this.townNameField;
            }
            set
            {
                this.townNameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shippingCancelDetailVO
    {
        
        private string cargoKeyField;
        
        private long docIdField;
        
        private int errCodeField;
        
        private bool errCodeFieldSpecified;
        
        private string errMessageField;
        
        private string invoiceKeyField;
        
        private long jobIdField;
        
        private int operationCodeField;
        
        private bool operationCodeFieldSpecified;
        
        private string operationMessageField;
        
        private string operationStatusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string cargoKey
        {
            get
            {
                return this.cargoKeyField;
            }
            set
            {
                this.cargoKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public long docId
        {
            get
            {
                return this.docIdField;
            }
            set
            {
                this.docIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public int errCode
        {
            get
            {
                return this.errCodeField;
            }
            set
            {
                this.errCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool errCodeSpecified
        {
            get
            {
                return this.errCodeFieldSpecified;
            }
            set
            {
                this.errCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string errMessage
        {
            get
            {
                return this.errMessageField;
            }
            set
            {
                this.errMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string invoiceKey
        {
            get
            {
                return this.invoiceKeyField;
            }
            set
            {
                this.invoiceKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public long jobId
        {
            get
            {
                return this.jobIdField;
            }
            set
            {
                this.jobIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public int operationCode
        {
            get
            {
                return this.operationCodeField;
            }
            set
            {
                this.operationCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool operationCodeSpecified
        {
            get
            {
                return this.operationCodeFieldSpecified;
            }
            set
            {
                this.operationCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string operationMessage
        {
            get
            {
                return this.operationMessageField;
            }
            set
            {
                this.operationMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string operationStatus
        {
            get
            {
                return this.operationStatusField;
            }
            set
            {
                this.operationStatusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shippingOrderDetailVO
    {
        
        private string cargoKeyField;
        
        private int errCodeField;
        
        private bool errCodeFieldSpecified;
        
        private string errMessageField;
        
        private string invoiceKeyField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string cargoKey
        {
            get
            {
                return this.cargoKeyField;
            }
            set
            {
                this.cargoKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public int errCode
        {
            get
            {
                return this.errCodeField;
            }
            set
            {
                this.errCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool errCodeSpecified
        {
            get
            {
                return this.errCodeFieldSpecified;
            }
            set
            {
                this.errCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string errMessage
        {
            get
            {
                return this.errMessageField;
            }
            set
            {
                this.errMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string invoiceKey
        {
            get
            {
                return this.invoiceKeyField;
            }
            set
            {
                this.invoiceKeyField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shippingDeliveryDetailVO
    {
        
        private string cargoKeyField;
        
        private int errCodeField;
        
        private bool errCodeFieldSpecified;
        
        private string errMessageField;
        
        private string invoiceKeyField;
        
        private long jobIdField;
        
        private int operationCodeField;
        
        private bool operationCodeFieldSpecified;
        
        private string operationMessageField;
        
        private string operationStatusField;
        
        private ShippingDeliveryItemDetailVO shippingDeliveryItemDetailVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string cargoKey
        {
            get
            {
                return this.cargoKeyField;
            }
            set
            {
                this.cargoKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public int errCode
        {
            get
            {
                return this.errCodeField;
            }
            set
            {
                this.errCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool errCodeSpecified
        {
            get
            {
                return this.errCodeFieldSpecified;
            }
            set
            {
                this.errCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string errMessage
        {
            get
            {
                return this.errMessageField;
            }
            set
            {
                this.errMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string invoiceKey
        {
            get
            {
                return this.invoiceKeyField;
            }
            set
            {
                this.invoiceKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public long jobId
        {
            get
            {
                return this.jobIdField;
            }
            set
            {
                this.jobIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public int operationCode
        {
            get
            {
                return this.operationCodeField;
            }
            set
            {
                this.operationCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool operationCodeSpecified
        {
            get
            {
                return this.operationCodeFieldSpecified;
            }
            set
            {
                this.operationCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string operationMessage
        {
            get
            {
                return this.operationMessageField;
            }
            set
            {
                this.operationMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string operationStatus
        {
            get
            {
                return this.operationStatusField;
            }
            set
            {
                this.operationStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public ShippingDeliveryItemDetailVO shippingDeliveryItemDetailVO
        {
            get
            {
                return this.shippingDeliveryItemDetailVOField;
            }
            set
            {
                this.shippingDeliveryItemDetailVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(extendedBaseResultVO))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(XEDocumentResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shipmentDeliveryResultVO))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shippingCancelResultVO))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shippingOrderResultVO))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shippingDeliveryResultVO))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class baseResultVO
    {
        
        private string outFlagField;
        
        private string outResultField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string outFlag
        {
            get
            {
                return this.outFlagField;
            }
            set
            {
                this.outFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string outResult
        {
            get
            {
                return this.outResultField;
            }
            set
            {
                this.outResultField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(XEDocumentResponse))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shipmentDeliveryResultVO))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shippingCancelResultVO))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shippingOrderResultVO))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shippingDeliveryResultVO))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class extendedBaseResultVO : baseResultVO
    {
        
        private string errCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string errCode
        {
            get
            {
                return this.errCodeField;
            }
            set
            {
                this.errCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XEDocumentResponse : extendedBaseResultVO
    {
        
        private string transactionChannelField;
        
        private string projectIdField;
        
        private string jobIdField;
        
        private string docIdField;
        
        private string docNumberField;
        
        private string tempListOrderSeqField;
        
        private string routingFlagField;
        
        private XESpecialField[] docFieldArrayField;
        
        private XEDocCargo[] docCargoV2ArrayField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string transactionChannel
        {
            get
            {
                return this.transactionChannelField;
            }
            set
            {
                this.transactionChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string projectId
        {
            get
            {
                return this.projectIdField;
            }
            set
            {
                this.projectIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string jobId
        {
            get
            {
                return this.jobIdField;
            }
            set
            {
                this.jobIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string docId
        {
            get
            {
                return this.docIdField;
            }
            set
            {
                this.docIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string docNumber
        {
            get
            {
                return this.docNumberField;
            }
            set
            {
                this.docNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string tempListOrderSeq
        {
            get
            {
                return this.tempListOrderSeqField;
            }
            set
            {
                this.tempListOrderSeqField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string routingFlag
        {
            get
            {
                return this.routingFlagField;
            }
            set
            {
                this.routingFlagField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("docFieldArray", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public XESpecialField[] docFieldArray
        {
            get
            {
                return this.docFieldArrayField;
            }
            set
            {
                this.docFieldArrayField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("docCargoV2Array", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public XEDocCargo[] docCargoV2Array
        {
            get
            {
                return this.docCargoV2ArrayField;
            }
            set
            {
                this.docCargoV2ArrayField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shipmentDeliveryResultVO : extendedBaseResultVO
    {
        
        private long countField;
        
        private string deliveryResultDataField;
        
        private long senderCustIdField;
        
        private shipmentDeliveryDetailVO[] shipmentDeliveryDetailVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string deliveryResultData
        {
            get
            {
                return this.deliveryResultDataField;
            }
            set
            {
                this.deliveryResultDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public long senderCustId
        {
            get
            {
                return this.senderCustIdField;
            }
            set
            {
                this.senderCustIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("shipmentDeliveryDetailVO", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public shipmentDeliveryDetailVO[] shipmentDeliveryDetailVO
        {
            get
            {
                return this.shipmentDeliveryDetailVOField;
            }
            set
            {
                this.shipmentDeliveryDetailVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shippingCancelResultVO : extendedBaseResultVO
    {
        
        private long countField;
        
        private long senderCustIdField;
        
        private shippingCancelDetailVO[] shippingCancelDetailVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public long senderCustId
        {
            get
            {
                return this.senderCustIdField;
            }
            set
            {
                this.senderCustIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("shippingCancelDetailVO", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public shippingCancelDetailVO[] shippingCancelDetailVO
        {
            get
            {
                return this.shippingCancelDetailVOField;
            }
            set
            {
                this.shippingCancelDetailVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shippingOrderResultVO : extendedBaseResultVO
    {
        
        private long countField;
        
        private long jobIdField;
        
        private shippingOrderDetailVO[] shippingOrderDetailVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public long jobId
        {
            get
            {
                return this.jobIdField;
            }
            set
            {
                this.jobIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("shippingOrderDetailVO", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public shippingOrderDetailVO[] shippingOrderDetailVO
        {
            get
            {
                return this.shippingOrderDetailVOField;
            }
            set
            {
                this.shippingOrderDetailVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shippingDeliveryResultVO : extendedBaseResultVO
    {
        
        private long countField;
        
        private long senderCustIdField;
        
        private shippingDeliveryDetailVO[] shippingDeliveryDetailVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public long senderCustId
        {
            get
            {
                return this.senderCustIdField;
            }
            set
            {
                this.senderCustIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("shippingDeliveryDetailVO", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public shippingDeliveryDetailVO[] shippingDeliveryDetailVO
        {
            get
            {
                return this.shippingDeliveryDetailVOField;
            }
            set
            {
                this.shippingDeliveryDetailVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class cancelReturnShipmentCodeResponse
    {
        
        private extendedBaseResultVO extendedBaseResultVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public extendedBaseResultVO ExtendedBaseResultVO
        {
            get
            {
                return this.extendedBaseResultVOField;
            }
            set
            {
                this.extendedBaseResultVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class cancelReturnShipmentCode
    {
        
        private string wsUserNameField;
        
        private string wsPasswordField;
        
        private string wsLanguageField;
        
        private string fieldNameField;
        
        private string returnCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string wsUserName
        {
            get
            {
                return this.wsUserNameField;
            }
            set
            {
                this.wsUserNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string wsPassword
        {
            get
            {
                return this.wsPasswordField;
            }
            set
            {
                this.wsPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string wsLanguage
        {
            get
            {
                return this.wsLanguageField;
            }
            set
            {
                this.wsLanguageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string fieldName
        {
            get
            {
                return this.fieldNameField;
            }
            set
            {
                this.fieldNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string returnCode
        {
            get
            {
                return this.returnCodeField;
            }
            set
            {
                this.returnCodeField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", ConfigurationName="Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices")]
    public interface ShippingOrderDispatcherServices
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Shipping.YurticiKargo.KOPSWebServices.YurticikargoWSException), Action="", Name="YurticikargoWSException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseResultVO))]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeResponse1> cancelReturnShipmentCodeAsync(Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Shipping.YurticiKargo.KOPSWebServices.YurticikargoWSException), Action="", Name="YurticikargoWSException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseResultVO))]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.cancelShipmentResponse1> cancelShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.cancelShipmentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Shipping.YurticiKargo.KOPSWebServices.YurticikargoWSException), Action="", Name="YurticikargoWSException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseResultVO))]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentResponse1> createShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.createShipmentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Shipping.YurticiKargo.KOPSWebServices.YurticikargoWSException), Action="", Name="YurticikargoWSException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseResultVO))]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailResponse1> createShipmentDetailAsync(Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Shipping.YurticiKargo.KOPSWebServices.YurticikargoWSException), Action="", Name="YurticikargoWSException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseResultVO))]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryResponse1> createShipmentWithDeliveryAsync(Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Shipping.YurticiKargo.KOPSWebServices.YurticikargoWSException), Action="", Name="YurticikargoWSException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseResultVO))]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.queryShipmentResponse1> queryShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.queryShipmentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Shipping.YurticiKargo.KOPSWebServices.YurticikargoWSException), Action="", Name="YurticikargoWSException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseResultVO))]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailResponse1> queryShipmentDetailAsync(Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Shipping.YurticiKargo.KOPSWebServices.YurticikargoWSException), Action="", Name="YurticikargoWSException")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(baseResultVO))]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeResponse1> saveReturnShipmentCodeAsync(Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class cancelReturnShipmentCodeRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCode cancelReturnShipmentCode;
        
        public cancelReturnShipmentCodeRequest()
        {
        }
        
        public cancelReturnShipmentCodeRequest(Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCode cancelReturnShipmentCode)
        {
            this.cancelReturnShipmentCode = cancelReturnShipmentCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class cancelReturnShipmentCodeResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeResponse cancelReturnShipmentCodeResponse;
        
        public cancelReturnShipmentCodeResponse1()
        {
        }
        
        public cancelReturnShipmentCodeResponse1(Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeResponse cancelReturnShipmentCodeResponse)
        {
            this.cancelReturnShipmentCodeResponse = cancelReturnShipmentCodeResponse;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class cancelShipment
    {
        
        private string wsUserNameField;
        
        private string wsPasswordField;
        
        private string userLanguageField;
        
        private string[] cargoKeysField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string wsUserName
        {
            get
            {
                return this.wsUserNameField;
            }
            set
            {
                this.wsUserNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string wsPassword
        {
            get
            {
                return this.wsPasswordField;
            }
            set
            {
                this.wsPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string userLanguage
        {
            get
            {
                return this.userLanguageField;
            }
            set
            {
                this.userLanguageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("cargoKeys", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string[] cargoKeys
        {
            get
            {
                return this.cargoKeysField;
            }
            set
            {
                this.cargoKeysField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class cancelShipmentResponse
    {
        
        private shippingCancelResultVO shippingOrderResultVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public shippingCancelResultVO ShippingOrderResultVO
        {
            get
            {
                return this.shippingOrderResultVOField;
            }
            set
            {
                this.shippingOrderResultVOField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class cancelShipmentRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.cancelShipment cancelShipment;
        
        public cancelShipmentRequest()
        {
        }
        
        public cancelShipmentRequest(Shipping.YurticiKargo.KOPSWebServices.cancelShipment cancelShipment)
        {
            this.cancelShipment = cancelShipment;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class cancelShipmentResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.cancelShipmentResponse cancelShipmentResponse;
        
        public cancelShipmentResponse1()
        {
        }
        
        public cancelShipmentResponse1(Shipping.YurticiKargo.KOPSWebServices.cancelShipmentResponse cancelShipmentResponse)
        {
            this.cancelShipmentResponse = cancelShipmentResponse;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class createShipment
    {
        
        private string wsUserNameField;
        
        private string wsPasswordField;
        
        private string userLanguageField;
        
        private ShippingOrderVO[] shippingOrderVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string wsUserName
        {
            get
            {
                return this.wsUserNameField;
            }
            set
            {
                this.wsUserNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string wsPassword
        {
            get
            {
                return this.wsPasswordField;
            }
            set
            {
                this.wsPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string userLanguage
        {
            get
            {
                return this.userLanguageField;
            }
            set
            {
                this.userLanguageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ShippingOrderVO", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ShippingOrderVO[] ShippingOrderVO
        {
            get
            {
                return this.shippingOrderVOField;
            }
            set
            {
                this.shippingOrderVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(shippingOrderCreateVO))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class ShippingOrderVO
    {
        
        private string cargoKeyField;
        
        private string invoiceKeyField;
        
        private string receiverCustNameField;
        
        private string receiverAddressField;
        
        private string cityNameField;
        
        private string townNameField;
        
        private string receiverPhone1Field;
        
        private string receiverPhone2Field;
        
        private string receiverPhone3Field;
        
        private string emailAddressField;
        
        private long taxOfficeIdField;
        
        private string taxNumberField;
        
        private string taxOfficeNameField;
        
        private double desiField;
        
        private bool desiFieldSpecified;
        
        private double kgField;
        
        private bool kgFieldSpecified;
        
        private int cargoCountField;
        
        private string waybillNoField;
        
        private string specialField1Field;
        
        private string specialField2Field;
        
        private string specialField3Field;
        
        private double ttInvoiceAmountField;
        
        private bool ttInvoiceAmountFieldSpecified;
        
        private long ttDocumentIdField;
        
        private string ttCollectionTypeField;
        
        private string ttDocumentSaveTypeField;
        
        private long dcSelectedCreditField;
        
        private long dcCreditRuleField;
        
        private string descriptionField;
        
        private string orgGeoCodeField;
        
        private string privilegeOrderField;
        
        private string custProdIdField;
        
        private string orgReceiverCustIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string cargoKey
        {
            get
            {
                return this.cargoKeyField;
            }
            set
            {
                this.cargoKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string invoiceKey
        {
            get
            {
                return this.invoiceKeyField;
            }
            set
            {
                this.invoiceKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string receiverCustName
        {
            get
            {
                return this.receiverCustNameField;
            }
            set
            {
                this.receiverCustNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string receiverAddress
        {
            get
            {
                return this.receiverAddressField;
            }
            set
            {
                this.receiverAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string cityName
        {
            get
            {
                return this.cityNameField;
            }
            set
            {
                this.cityNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string townName
        {
            get
            {
                return this.townNameField;
            }
            set
            {
                this.townNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string receiverPhone1
        {
            get
            {
                return this.receiverPhone1Field;
            }
            set
            {
                this.receiverPhone1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string receiverPhone2
        {
            get
            {
                return this.receiverPhone2Field;
            }
            set
            {
                this.receiverPhone2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string receiverPhone3
        {
            get
            {
                return this.receiverPhone3Field;
            }
            set
            {
                this.receiverPhone3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string emailAddress
        {
            get
            {
                return this.emailAddressField;
            }
            set
            {
                this.emailAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public long taxOfficeId
        {
            get
            {
                return this.taxOfficeIdField;
            }
            set
            {
                this.taxOfficeIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string taxNumber
        {
            get
            {
                return this.taxNumberField;
            }
            set
            {
                this.taxNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string taxOfficeName
        {
            get
            {
                return this.taxOfficeNameField;
            }
            set
            {
                this.taxOfficeNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public double desi
        {
            get
            {
                return this.desiField;
            }
            set
            {
                this.desiField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool desiSpecified
        {
            get
            {
                return this.desiFieldSpecified;
            }
            set
            {
                this.desiFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public double kg
        {
            get
            {
                return this.kgField;
            }
            set
            {
                this.kgField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool kgSpecified
        {
            get
            {
                return this.kgFieldSpecified;
            }
            set
            {
                this.kgFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public int cargoCount
        {
            get
            {
                return this.cargoCountField;
            }
            set
            {
                this.cargoCountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string waybillNo
        {
            get
            {
                return this.waybillNoField;
            }
            set
            {
                this.waybillNoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string specialField1
        {
            get
            {
                return this.specialField1Field;
            }
            set
            {
                this.specialField1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string specialField2
        {
            get
            {
                return this.specialField2Field;
            }
            set
            {
                this.specialField2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string specialField3
        {
            get
            {
                return this.specialField3Field;
            }
            set
            {
                this.specialField3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public double ttInvoiceAmount
        {
            get
            {
                return this.ttInvoiceAmountField;
            }
            set
            {
                this.ttInvoiceAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ttInvoiceAmountSpecified
        {
            get
            {
                return this.ttInvoiceAmountFieldSpecified;
            }
            set
            {
                this.ttInvoiceAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public long ttDocumentId
        {
            get
            {
                return this.ttDocumentIdField;
            }
            set
            {
                this.ttDocumentIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string ttCollectionType
        {
            get
            {
                return this.ttCollectionTypeField;
            }
            set
            {
                this.ttCollectionTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string ttDocumentSaveType
        {
            get
            {
                return this.ttDocumentSaveTypeField;
            }
            set
            {
                this.ttDocumentSaveTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public long dcSelectedCredit
        {
            get
            {
                return this.dcSelectedCreditField;
            }
            set
            {
                this.dcSelectedCreditField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public long dcCreditRule
        {
            get
            {
                return this.dcCreditRuleField;
            }
            set
            {
                this.dcCreditRuleField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=26)]
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=27)]
        public string orgGeoCode
        {
            get
            {
                return this.orgGeoCodeField;
            }
            set
            {
                this.orgGeoCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=28)]
        public string privilegeOrder
        {
            get
            {
                return this.privilegeOrderField;
            }
            set
            {
                this.privilegeOrderField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=29)]
        public string custProdId
        {
            get
            {
                return this.custProdIdField;
            }
            set
            {
                this.custProdIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=30)]
        public string orgReceiverCustId
        {
            get
            {
                return this.orgReceiverCustIdField;
            }
            set
            {
                this.orgReceiverCustIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class shippingOrderCreateVO : ShippingOrderVO
    {
        
        private string orderDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string orderData
        {
            get
            {
                return this.orderDataField;
            }
            set
            {
                this.orderDataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class createShipmentResponse
    {
        
        private shippingOrderResultVO shippingOrderResultVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public shippingOrderResultVO ShippingOrderResultVO
        {
            get
            {
                return this.shippingOrderResultVOField;
            }
            set
            {
                this.shippingOrderResultVOField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createShipmentRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.createShipment createShipment;
        
        public createShipmentRequest()
        {
        }
        
        public createShipmentRequest(Shipping.YurticiKargo.KOPSWebServices.createShipment createShipment)
        {
            this.createShipment = createShipment;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createShipmentResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.createShipmentResponse createShipmentResponse;
        
        public createShipmentResponse1()
        {
        }
        
        public createShipmentResponse1(Shipping.YurticiKargo.KOPSWebServices.createShipmentResponse createShipmentResponse)
        {
            this.createShipmentResponse = createShipmentResponse;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class createShipmentDetail
    {
        
        private string wsUserNameField;
        
        private string wsPasswordField;
        
        private string userLanguageField;
        
        private shippingOrderCreateVO[] shippingOrderVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string wsUserName
        {
            get
            {
                return this.wsUserNameField;
            }
            set
            {
                this.wsUserNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string wsPassword
        {
            get
            {
                return this.wsPasswordField;
            }
            set
            {
                this.wsPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string userLanguage
        {
            get
            {
                return this.userLanguageField;
            }
            set
            {
                this.userLanguageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ShippingOrderVO", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public shippingOrderCreateVO[] ShippingOrderVO
        {
            get
            {
                return this.shippingOrderVOField;
            }
            set
            {
                this.shippingOrderVOField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class createShipmentDetailResponse
    {
        
        private shippingOrderResultVO shippingOrderResultVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public shippingOrderResultVO ShippingOrderResultVO
        {
            get
            {
                return this.shippingOrderResultVOField;
            }
            set
            {
                this.shippingOrderResultVOField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createShipmentDetailRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.createShipmentDetail createShipmentDetail;
        
        public createShipmentDetailRequest()
        {
        }
        
        public createShipmentDetailRequest(Shipping.YurticiKargo.KOPSWebServices.createShipmentDetail createShipmentDetail)
        {
            this.createShipmentDetail = createShipmentDetail;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createShipmentDetailResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailResponse createShipmentDetailResponse;
        
        public createShipmentDetailResponse1()
        {
        }
        
        public createShipmentDetailResponse1(Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailResponse createShipmentDetailResponse)
        {
            this.createShipmentDetailResponse = createShipmentDetailResponse;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class createShipmentWithDelivery
    {
        
        private string wsUserNameField;
        
        private string wsPasswordField;
        
        private string wsUserLanguageField;
        
        private XEDocument xEDocumentField;
        
        private XEReferenceKeys xEReferenceKeysField;
        
        private XEComplementaryProduct[] xEComplementaryProductField;
        
        private XECod xECodField;
        
        private XESpecialField[] xESpecialFieldArrayField;
        
        private XESender xESenderField;
        
        private XEReceiver xEReceiverField;
        
        private XEDelivery xEDeliveryField;
        
        private XEDocCargoZplFormatData xEDocCargoZplFormatDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string wsUserName
        {
            get
            {
                return this.wsUserNameField;
            }
            set
            {
                this.wsUserNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string wsPassword
        {
            get
            {
                return this.wsPasswordField;
            }
            set
            {
                this.wsPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string wsUserLanguage
        {
            get
            {
                return this.wsUserLanguageField;
            }
            set
            {
                this.wsUserLanguageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public XEDocument XEDocument
        {
            get
            {
                return this.xEDocumentField;
            }
            set
            {
                this.xEDocumentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public XEReferenceKeys XEReferenceKeys
        {
            get
            {
                return this.xEReferenceKeysField;
            }
            set
            {
                this.xEReferenceKeysField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("XEComplementaryProduct", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public XEComplementaryProduct[] XEComplementaryProduct
        {
            get
            {
                return this.xEComplementaryProductField;
            }
            set
            {
                this.xEComplementaryProductField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public XECod XECod
        {
            get
            {
                return this.xECodField;
            }
            set
            {
                this.xECodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("XESpecialFieldArray", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public XESpecialField[] XESpecialFieldArray
        {
            get
            {
                return this.xESpecialFieldArrayField;
            }
            set
            {
                this.xESpecialFieldArrayField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public XESender XESender
        {
            get
            {
                return this.xESenderField;
            }
            set
            {
                this.xESenderField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public XEReceiver XEReceiver
        {
            get
            {
                return this.xEReceiverField;
            }
            set
            {
                this.xEReceiverField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public XEDelivery XEDelivery
        {
            get
            {
                return this.xEDeliveryField;
            }
            set
            {
                this.xEDeliveryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public XEDocCargoZplFormatData XEDocCargoZplFormatData
        {
            get
            {
                return this.xEDocCargoZplFormatDataField;
            }
            set
            {
                this.xEDocCargoZplFormatDataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XEDocument
    {
        
        private string mainProdCodeField;
        
        private long totalCargoCountField;
        
        private double totalDesiField;
        
        private double totalWeightField;
        
        private string personGiverField;
        
        private string shipmentContentField;
        
        private string descriptionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string mainProdCode
        {
            get
            {
                return this.mainProdCodeField;
            }
            set
            {
                this.mainProdCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public long totalCargoCount
        {
            get
            {
                return this.totalCargoCountField;
            }
            set
            {
                this.totalCargoCountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public double totalDesi
        {
            get
            {
                return this.totalDesiField;
            }
            set
            {
                this.totalDesiField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double totalWeight
        {
            get
            {
                return this.totalWeightField;
            }
            set
            {
                this.totalWeightField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string personGiver
        {
            get
            {
                return this.personGiverField;
            }
            set
            {
                this.personGiverField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string shipmentContent
        {
            get
            {
                return this.shipmentContentField;
            }
            set
            {
                this.shipmentContentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XEReferenceKeys
    {
        
        private string cargoKeyField;
        
        private string invoiceKeyField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string cargoKey
        {
            get
            {
                return this.cargoKeyField;
            }
            set
            {
                this.cargoKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string invoiceKey
        {
            get
            {
                return this.invoiceKeyField;
            }
            set
            {
                this.invoiceKeyField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XEComplementaryProduct
    {
        
        private string complementaryProductCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string complementaryProductCode
        {
            get
            {
                return this.complementaryProductCodeField;
            }
            set
            {
                this.complementaryProductCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XECod
    {
        
        private string ttDocumentIdField;
        
        private double ttInvoiceAmountField;
        
        private string ttCollectionTypeField;
        
        private string ttDocumentSaveTypeField;
        
        private long dcSelectedCreditField;
        
        private string dcCreditRuleField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ttDocumentId
        {
            get
            {
                return this.ttDocumentIdField;
            }
            set
            {
                this.ttDocumentIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public double ttInvoiceAmount
        {
            get
            {
                return this.ttInvoiceAmountField;
            }
            set
            {
                this.ttInvoiceAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ttCollectionType
        {
            get
            {
                return this.ttCollectionTypeField;
            }
            set
            {
                this.ttCollectionTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ttDocumentSaveType
        {
            get
            {
                return this.ttDocumentSaveTypeField;
            }
            set
            {
                this.ttDocumentSaveTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public long dcSelectedCredit
        {
            get
            {
                return this.dcSelectedCreditField;
            }
            set
            {
                this.dcSelectedCreditField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string dcCreditRule
        {
            get
            {
                return this.dcCreditRuleField;
            }
            set
            {
                this.dcCreditRuleField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XESender
    {
        
        private string senderCustIdField;
        
        private string senderAddressIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string senderCustId
        {
            get
            {
                return this.senderCustIdField;
            }
            set
            {
                this.senderCustIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string senderAddressId
        {
            get
            {
                return this.senderAddressIdField;
            }
            set
            {
                this.senderAddressIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XEReceiver
    {
        
        private string custNameField;
        
        private string addressField;
        
        private string cityIdField;
        
        private string townNameField;
        
        private string phoneField;
        
        private string mobilePhoneField;
        
        private string emailAddressField;
        
        private string custReferenceIdField;
        
        private string addressReferenceIdField;
        
        private string additionalInfoField;
        
        private string taxNoField;
        
        private string tcknField;
        
        private string countryTaxOfficeIdField;
        
        private string latitudeField;
        
        private string longitudeField;
        
        private string receiverCustIdField;
        
        private string receiverAddressIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string custName
        {
            get
            {
                return this.custNameField;
            }
            set
            {
                this.custNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string address
        {
            get
            {
                return this.addressField;
            }
            set
            {
                this.addressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string cityId
        {
            get
            {
                return this.cityIdField;
            }
            set
            {
                this.cityIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string townName
        {
            get
            {
                return this.townNameField;
            }
            set
            {
                this.townNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string phone
        {
            get
            {
                return this.phoneField;
            }
            set
            {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string mobilePhone
        {
            get
            {
                return this.mobilePhoneField;
            }
            set
            {
                this.mobilePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string emailAddress
        {
            get
            {
                return this.emailAddressField;
            }
            set
            {
                this.emailAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string custReferenceId
        {
            get
            {
                return this.custReferenceIdField;
            }
            set
            {
                this.custReferenceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string addressReferenceId
        {
            get
            {
                return this.addressReferenceIdField;
            }
            set
            {
                this.addressReferenceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string additionalInfo
        {
            get
            {
                return this.additionalInfoField;
            }
            set
            {
                this.additionalInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string taxNo
        {
            get
            {
                return this.taxNoField;
            }
            set
            {
                this.taxNoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string tckn
        {
            get
            {
                return this.tcknField;
            }
            set
            {
                this.tcknField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string countryTaxOfficeId
        {
            get
            {
                return this.countryTaxOfficeIdField;
            }
            set
            {
                this.countryTaxOfficeIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string latitude
        {
            get
            {
                return this.latitudeField;
            }
            set
            {
                this.latitudeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string longitude
        {
            get
            {
                return this.longitudeField;
            }
            set
            {
                this.longitudeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string receiverCustId
        {
            get
            {
                return this.receiverCustIdField;
            }
            set
            {
                this.receiverCustIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string receiverAddressId
        {
            get
            {
                return this.receiverAddressIdField;
            }
            set
            {
                this.receiverAddressIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XEDelivery
    {
        
        private string ktnIdField;
        
        private string deliveryTypeField;
        
        private string deliveryMobilePhoneField;
        
        private string ktnTranIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ktnId
        {
            get
            {
                return this.ktnIdField;
            }
            set
            {
                this.ktnIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string deliveryType
        {
            get
            {
                return this.deliveryTypeField;
            }
            set
            {
                this.deliveryTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string deliveryMobilePhone
        {
            get
            {
                return this.deliveryMobilePhoneField;
            }
            set
            {
                this.deliveryMobilePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ktnTranId
        {
            get
            {
                return this.ktnTranIdField;
            }
            set
            {
                this.ktnTranIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class XEDocCargoZplFormatData
    {
        
        private string docCargoZplDataFormatField;
        
        private string docCargoLabelTypeField;
        
        private string zplTypeField;
        
        private string encodingField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string docCargoZplDataFormat
        {
            get
            {
                return this.docCargoZplDataFormatField;
            }
            set
            {
                this.docCargoZplDataFormatField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string docCargoLabelType
        {
            get
            {
                return this.docCargoLabelTypeField;
            }
            set
            {
                this.docCargoLabelTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string zplType
        {
            get
            {
                return this.zplTypeField;
            }
            set
            {
                this.zplTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string encoding
        {
            get
            {
                return this.encodingField;
            }
            set
            {
                this.encodingField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class createShipmentWithDeliveryResponse
    {
        
        private XEDocumentResponse xEDocumentResponseField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public XEDocumentResponse XEDocumentResponse
        {
            get
            {
                return this.xEDocumentResponseField;
            }
            set
            {
                this.xEDocumentResponseField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createShipmentWithDeliveryRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDelivery createShipmentWithDelivery;
        
        public createShipmentWithDeliveryRequest()
        {
        }
        
        public createShipmentWithDeliveryRequest(Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDelivery createShipmentWithDelivery)
        {
            this.createShipmentWithDelivery = createShipmentWithDelivery;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createShipmentWithDeliveryResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryResponse createShipmentWithDeliveryResponse;
        
        public createShipmentWithDeliveryResponse1()
        {
        }
        
        public createShipmentWithDeliveryResponse1(Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryResponse createShipmentWithDeliveryResponse)
        {
            this.createShipmentWithDeliveryResponse = createShipmentWithDeliveryResponse;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class queryShipment
    {
        
        private string wsUserNameField;
        
        private string wsPasswordField;
        
        private string wsLanguageField;
        
        private string[] keysField;
        
        private int keyTypeField;
        
        private bool addHistoricalDataField;
        
        private bool onlyTrackingField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string wsUserName
        {
            get
            {
                return this.wsUserNameField;
            }
            set
            {
                this.wsUserNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string wsPassword
        {
            get
            {
                return this.wsPasswordField;
            }
            set
            {
                this.wsPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string wsLanguage
        {
            get
            {
                return this.wsLanguageField;
            }
            set
            {
                this.wsLanguageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("keys", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string[] keys
        {
            get
            {
                return this.keysField;
            }
            set
            {
                this.keysField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public int keyType
        {
            get
            {
                return this.keyTypeField;
            }
            set
            {
                this.keyTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public bool addHistoricalData
        {
            get
            {
                return this.addHistoricalDataField;
            }
            set
            {
                this.addHistoricalDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public bool onlyTracking
        {
            get
            {
                return this.onlyTrackingField;
            }
            set
            {
                this.onlyTrackingField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class queryShipmentResponse
    {
        
        private shippingDeliveryResultVO shippingDeliveryVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public shippingDeliveryResultVO ShippingDeliveryVO
        {
            get
            {
                return this.shippingDeliveryVOField;
            }
            set
            {
                this.shippingDeliveryVOField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class queryShipmentRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.queryShipment queryShipment;
        
        public queryShipmentRequest()
        {
        }
        
        public queryShipmentRequest(Shipping.YurticiKargo.KOPSWebServices.queryShipment queryShipment)
        {
            this.queryShipment = queryShipment;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class queryShipmentResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.queryShipmentResponse queryShipmentResponse;
        
        public queryShipmentResponse1()
        {
        }
        
        public queryShipmentResponse1(Shipping.YurticiKargo.KOPSWebServices.queryShipmentResponse queryShipmentResponse)
        {
            this.queryShipmentResponse = queryShipmentResponse;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class queryShipmentDetail
    {
        
        private string wsUserNameField;
        
        private string wsPasswordField;
        
        private string wsLanguageField;
        
        private string[] keysField;
        
        private int keyTypeField;
        
        private bool addHistoricalDataField;
        
        private bool onlyTrackingField;
        
        private bool jsonDataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string wsUserName
        {
            get
            {
                return this.wsUserNameField;
            }
            set
            {
                this.wsUserNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string wsPassword
        {
            get
            {
                return this.wsPasswordField;
            }
            set
            {
                this.wsPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string wsLanguage
        {
            get
            {
                return this.wsLanguageField;
            }
            set
            {
                this.wsLanguageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("keys", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string[] keys
        {
            get
            {
                return this.keysField;
            }
            set
            {
                this.keysField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public int keyType
        {
            get
            {
                return this.keyTypeField;
            }
            set
            {
                this.keyTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public bool addHistoricalData
        {
            get
            {
                return this.addHistoricalDataField;
            }
            set
            {
                this.addHistoricalDataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public bool onlyTracking
        {
            get
            {
                return this.onlyTrackingField;
            }
            set
            {
                this.onlyTrackingField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public bool jsonData
        {
            get
            {
                return this.jsonDataField;
            }
            set
            {
                this.jsonDataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class queryShipmentDetailResponse
    {
        
        private shipmentDeliveryResultVO shipmentDeliveryResultVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public shipmentDeliveryResultVO ShipmentDeliveryResultVO
        {
            get
            {
                return this.shipmentDeliveryResultVOField;
            }
            set
            {
                this.shipmentDeliveryResultVOField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class queryShipmentDetailRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetail queryShipmentDetail;
        
        public queryShipmentDetailRequest()
        {
        }
        
        public queryShipmentDetailRequest(Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetail queryShipmentDetail)
        {
            this.queryShipmentDetail = queryShipmentDetail;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class queryShipmentDetailResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailResponse queryShipmentDetailResponse;
        
        public queryShipmentDetailResponse1()
        {
        }
        
        public queryShipmentDetailResponse1(Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailResponse queryShipmentDetailResponse)
        {
            this.queryShipmentDetailResponse = queryShipmentDetailResponse;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class saveReturnShipmentCode
    {
        
        private string wsUserNameField;
        
        private string wsPasswordField;
        
        private string wsLanguageField;
        
        private string fieldNameField;
        
        private string returnCodeField;
        
        private string startDateField;
        
        private string endDateField;
        
        private int maxCountField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string wsUserName
        {
            get
            {
                return this.wsUserNameField;
            }
            set
            {
                this.wsUserNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string wsPassword
        {
            get
            {
                return this.wsPasswordField;
            }
            set
            {
                this.wsPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string wsLanguage
        {
            get
            {
                return this.wsLanguageField;
            }
            set
            {
                this.wsLanguageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string fieldName
        {
            get
            {
                return this.fieldNameField;
            }
            set
            {
                this.fieldNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string returnCode
        {
            get
            {
                return this.returnCodeField;
            }
            set
            {
                this.returnCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string startDate
        {
            get
            {
                return this.startDateField;
            }
            set
            {
                this.startDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string endDate
        {
            get
            {
                return this.endDateField;
            }
            set
            {
                this.endDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public int maxCount
        {
            get
            {
                return this.maxCountField;
            }
            set
            {
                this.maxCountField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices")]
    public partial class saveReturnShipmentCodeResponse
    {
        
        private extendedBaseResultVO extendedBaseResultVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public extendedBaseResultVO ExtendedBaseResultVO
        {
            get
            {
                return this.extendedBaseResultVOField;
            }
            set
            {
                this.extendedBaseResultVOField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class saveReturnShipmentCodeRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCode saveReturnShipmentCode;
        
        public saveReturnShipmentCodeRequest()
        {
        }
        
        public saveReturnShipmentCodeRequest(Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCode saveReturnShipmentCode)
        {
            this.saveReturnShipmentCode = saveReturnShipmentCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class saveReturnShipmentCodeResponse1
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://yurticikargo.com.tr/ShippingOrderDispatcherServices", Order=0)]
        public Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeResponse saveReturnShipmentCodeResponse;
        
        public saveReturnShipmentCodeResponse1()
        {
        }
        
        public saveReturnShipmentCodeResponse1(Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeResponse saveReturnShipmentCodeResponse)
        {
            this.saveReturnShipmentCodeResponse = saveReturnShipmentCodeResponse;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface ShippingOrderDispatcherServicesChannel : Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class ShippingOrderDispatcherServicesClient : System.ServiceModel.ClientBase<Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices>, Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public ShippingOrderDispatcherServicesClient() : 
                base(ShippingOrderDispatcherServicesClient.GetDefaultBinding(), ShippingOrderDispatcherServicesClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.ShippingOrderDispatcherServicesPort.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ShippingOrderDispatcherServicesClient(EndpointConfiguration endpointConfiguration) : 
                base(ShippingOrderDispatcherServicesClient.GetBindingForEndpoint(endpointConfiguration), ShippingOrderDispatcherServicesClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ShippingOrderDispatcherServicesClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(ShippingOrderDispatcherServicesClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ShippingOrderDispatcherServicesClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(ShippingOrderDispatcherServicesClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ShippingOrderDispatcherServicesClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeResponse1> Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices.cancelReturnShipmentCodeAsync(Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeRequest request)
        {
            return base.Channel.cancelReturnShipmentCodeAsync(request);
        }
        
        public System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeResponse1> cancelReturnShipmentCodeAsync(Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCode cancelReturnShipmentCode)
        {
            Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeRequest inValue = new Shipping.YurticiKargo.KOPSWebServices.cancelReturnShipmentCodeRequest();
            inValue.cancelReturnShipmentCode = cancelReturnShipmentCode;
            return ((Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices)(this)).cancelReturnShipmentCodeAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.cancelShipmentResponse1> Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices.cancelShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.cancelShipmentRequest request)
        {
            return base.Channel.cancelShipmentAsync(request);
        }
        
        public System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.cancelShipmentResponse1> cancelShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.cancelShipment cancelShipment)
        {
            Shipping.YurticiKargo.KOPSWebServices.cancelShipmentRequest inValue = new Shipping.YurticiKargo.KOPSWebServices.cancelShipmentRequest();
            inValue.cancelShipment = cancelShipment;
            return ((Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices)(this)).cancelShipmentAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentResponse1> Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices.createShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.createShipmentRequest request)
        {
            return base.Channel.createShipmentAsync(request);
        }
        
        public System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentResponse1> createShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.createShipment createShipment)
        {
            Shipping.YurticiKargo.KOPSWebServices.createShipmentRequest inValue = new Shipping.YurticiKargo.KOPSWebServices.createShipmentRequest();
            inValue.createShipment = createShipment;
            return ((Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices)(this)).createShipmentAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailResponse1> Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices.createShipmentDetailAsync(Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailRequest request)
        {
            return base.Channel.createShipmentDetailAsync(request);
        }
        
        public System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailResponse1> createShipmentDetailAsync(Shipping.YurticiKargo.KOPSWebServices.createShipmentDetail createShipmentDetail)
        {
            Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailRequest inValue = new Shipping.YurticiKargo.KOPSWebServices.createShipmentDetailRequest();
            inValue.createShipmentDetail = createShipmentDetail;
            return ((Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices)(this)).createShipmentDetailAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryResponse1> Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices.createShipmentWithDeliveryAsync(Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryRequest request)
        {
            return base.Channel.createShipmentWithDeliveryAsync(request);
        }
        
        public System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryResponse1> createShipmentWithDeliveryAsync(Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDelivery createShipmentWithDelivery)
        {
            Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryRequest inValue = new Shipping.YurticiKargo.KOPSWebServices.createShipmentWithDeliveryRequest();
            inValue.createShipmentWithDelivery = createShipmentWithDelivery;
            return ((Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices)(this)).createShipmentWithDeliveryAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.queryShipmentResponse1> Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices.queryShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.queryShipmentRequest request)
        {
            return base.Channel.queryShipmentAsync(request);
        }
        
        public System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.queryShipmentResponse1> queryShipmentAsync(Shipping.YurticiKargo.KOPSWebServices.queryShipment queryShipment)
        {
            Shipping.YurticiKargo.KOPSWebServices.queryShipmentRequest inValue = new Shipping.YurticiKargo.KOPSWebServices.queryShipmentRequest();
            inValue.queryShipment = queryShipment;
            return ((Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices)(this)).queryShipmentAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailResponse1> Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices.queryShipmentDetailAsync(Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailRequest request)
        {
            return base.Channel.queryShipmentDetailAsync(request);
        }
        
        public System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailResponse1> queryShipmentDetailAsync(Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetail queryShipmentDetail)
        {
            Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailRequest inValue = new Shipping.YurticiKargo.KOPSWebServices.queryShipmentDetailRequest();
            inValue.queryShipmentDetail = queryShipmentDetail;
            return ((Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices)(this)).queryShipmentDetailAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeResponse1> Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices.saveReturnShipmentCodeAsync(Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeRequest request)
        {
            return base.Channel.saveReturnShipmentCodeAsync(request);
        }
        
        public System.Threading.Tasks.Task<Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeResponse1> saveReturnShipmentCodeAsync(Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCode saveReturnShipmentCode)
        {
            Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeRequest inValue = new Shipping.YurticiKargo.KOPSWebServices.saveReturnShipmentCodeRequest();
            inValue.saveReturnShipmentCode = saveReturnShipmentCode;
            return ((Shipping.YurticiKargo.KOPSWebServices.ShippingOrderDispatcherServices)(this)).saveReturnShipmentCodeAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.ShippingOrderDispatcherServicesPort))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.ShippingOrderDispatcherServicesPort))
            {
                return new System.ServiceModel.EndpointAddress("https://ws.yurticikargo.com/KOPSWebServices/ShippingOrderDispatcherServices");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return ShippingOrderDispatcherServicesClient.GetBindingForEndpoint(EndpointConfiguration.ShippingOrderDispatcherServicesPort);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return ShippingOrderDispatcherServicesClient.GetEndpointAddress(EndpointConfiguration.ShippingOrderDispatcherServicesPort);
        }
        
        public enum EndpointConfiguration
        {
            
            ShippingOrderDispatcherServicesPort,
        }
    }
}
