namespace Modules.Mail.Abstraction.Models
{
    public class MailMessage
    {
        public string Title { get; set; } = null!;
        public string MailContent { get; set; } = null!;
        public List<string> To { get; set; } = [];
        public List<string> CC { get; set; } = [];
        public List<string> BCC { get; set; } = [];
        public List<MailAttachment> Attachments { get; set; } = [];
        public string Provider { get; set; } = "SMTP"; // Varsayılan olarak SMTP
    }
}
