## Common ignores
.DS_Store
CHECKLIST.md
*~
*.swp
*.swo
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.vscode/
.idea/

## .NET (B2B.Backend) ignores
# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# Visual Studio directory
.vs/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/
**/Properties/launchSettings.json

# Test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
*.VisualState.xml
TestResult.xml
coverage/
*.coverage
*.coveragexml

# NuGet Packages
*.nupkg
**/packages/*
!**/packages/build/
*.nuget.props
*.nuget.targets

## Next.js (B2B.Frontend) ignores
# dependencies
/B2B.Frontend/node_modules
/B2B.Frontend/.pnp
/B2B.Frontend/.pnp.js

# testing
/B2B.Frontend/coverage
/B2B.Frontend/.nyc_output

# next.js build output
/B2B.Frontend/.next/
/B2B.Frontend/out/
/B2B.Frontend/build

# misc
/B2B.Frontend/npm-debug.log*
/B2B.Frontend/yarn-debug.log*
/B2B.Frontend/yarn-error.log*

# local env files
/B2B.Frontend/.env*.local

# vercel
/B2B.Frontend/.vercel

# typescript
/B2B.Frontend/*.tsbuildinfo
/B2B.Frontend/next-env.d.ts

# PWA files
/B2B.Frontend/public/sw.js
/B2B.Frontend/public/workbox-*.js
/B2B.Frontend/public/worker-*.js
/B2B.Frontend/public/sw.js.map
/B2B.Frontend/public/workbox-*.js.map
/B2B.Frontend/public/worker-*.js.map

/B2B.Backend/MediaAPI/wwwroot/images/
/b2b.frontend/public/images/
# Sensitive configuration files
appsettings.*.json
!appsettings.example.json
appsettings.json
node_modules

/original-themes